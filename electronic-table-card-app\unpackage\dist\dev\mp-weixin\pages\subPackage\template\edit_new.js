(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/subPackage/template/edit_new"],{

/***/ 281:
/*!************************************************************************************************************************!*\
  !*** D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/main.js?{"page":"pages%2FsubPackage%2Ftemplate%2Fedit_new"} ***!
  \************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _edit_new = _interopRequireDefault(__webpack_require__(/*! ./pages/subPackage/template/edit_new.vue */ 282));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_edit_new.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 282:
/*!***************************************************************************************************!*\
  !*** D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/edit_new.vue ***!
  \***************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _edit_new_vue_vue_type_template_id_4aed2b70_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./edit_new.vue?vue&type=template&id=4aed2b70&scoped=true& */ 283);
/* harmony import */ var _edit_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./edit_new.vue?vue&type=script&lang=js& */ 285);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _edit_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _edit_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _edit_new_vue_vue_type_style_index_0_id_4aed2b70_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./edit_new.vue?vue&type=style&index=0&id=4aed2b70&scoped=true&lang=css& */ 287);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 32);

var renderjs





/* normalize component */

var component = Object(_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _edit_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _edit_new_vue_vue_type_template_id_4aed2b70_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _edit_new_vue_vue_type_template_id_4aed2b70_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "4aed2b70",
  null,
  false,
  _edit_new_vue_vue_type_template_id_4aed2b70_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/subPackage/template/edit_new.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 283:
/*!**********************************************************************************************************************************************!*\
  !*** D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/edit_new.vue?vue&type=template&id=4aed2b70&scoped=true& ***!
  \**********************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_new_vue_vue_type_template_id_4aed2b70_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit_new.vue?vue&type=template&id=4aed2b70&scoped=true& */ 284);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_new_vue_vue_type_template_id_4aed2b70_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_new_vue_vue_type_template_id_4aed2b70_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_new_vue_vue_type_template_id_4aed2b70_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_new_vue_vue_type_template_id_4aed2b70_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 284:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/edit_new.vue?vue&type=template&id=4aed2b70&scoped=true& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uPopup: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-popup/u-popup */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-popup/u-popup")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-popup/u-popup.vue */ 323))
    },
    uPicker: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-picker/u-picker */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-picker/u-picker")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-picker/u-picker.vue */ 349))
    },
    uSlider: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-slider/u-slider */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-slider/u-slider")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-slider/u-slider.vue */ 357))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = _vm.editingElement ? _vm.getFontFamilyIndex() : null
  var m1 =
    _vm.editingElement && _vm.editingElement.type === "text"
      ? _vm.getColorName()
      : null
  var m2 = _vm.editingElement ? _vm.getColorIndex() : null
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      _vm.showFontPicker = true
    }
    _vm.e1 = function ($event) {
      _vm.showFontPicker = false
    }
    _vm.e2 = function ($event) {
      _vm.showFontPicker = false
    }
    _vm.e3 = function ($event) {
      _vm.showColorPicker = true
    }
    _vm.e4 = function ($event) {
      _vm.showColorPicker = false
    }
    _vm.e5 = function ($event) {
      _vm.showColorPicker = false
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        m1: m1,
        m2: m2,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 285:
/*!****************************************************************************************************************************!*\
  !*** D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/edit_new.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit_new.vue?vue&type=script&lang=js& */ 286);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 286:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/edit_new.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  data: function data() {
    return {
      // 模板基础信息
      templateName: '新增模板',
      insertType: 'template-text',
      backgroundType: 'select',
      // 背景相关
      backgroundImageUrl: '',
      backgroundColor: '#FFFFFF',
      // 画布相关
      canvasWidth: 0,
      canvasHeight: 0,
      previewContainerStyle: 0,
      canvasContext: null,
      // 文字属性
      fontFamilies: ['微软雅黑', '宋体', '黑体', '楷体', 'Arial', 'Times New Roman'],
      textColor: '#000000',
      fontWeight: 'normal',
      fontStyle: 'normal',
      textDecoration: 'none',
      fontSize: 30,
      // 电子桌牌类型
      cardType: 'six-color',
      // 'three-color' | 'six-color'

      //六色电子桌牌
      //黑、白、红、黄、蓝、绿
      //三色电子桌牌
      //黑白红 
      // 颜色选择器
      colorOptions: ['黑色', '白色', '红色', '黄色', '蓝色', '绿色'],
      colorValues: ['#000000', '#FFFFFF', '#FF0000', '#FFFF00', '#0000FF', '#00FF00'],
      // 三色桌牌颜色选项
      threeColorOptions: ['黑色', '白色', '红色'],
      threeColorValues: ['#000000', '#FFFFFF', '#FF0000'],
      // 六色桌牌颜色选项
      sixColorOptions: ['黑色', '白色', '红色', '黄色', '蓝色', '绿色'],
      sixColorValues: ['#000000', '#FFFFFF', '#FF0000', '#FFFF00', '#0000FF', '#00FF00'],
      // 预览相关
      previewImagePath: '',
      previewLoading: false,
      showTemplatePopup: false,
      // 模板字段输入数据
      templateFields: {
        name: '',
        position: '',
        company: '',
        other: ''
      },
      // 画布元素
      canvasElements: [],
      selectedElement: null,
      dragging: false,
      lastTouchX: 0,
      lastTouchY: 0,
      touchStartTime: 0,
      // 属性编辑弹窗
      showPropertyPopup: false,
      editingElement: null,
      // picker控制
      showFontPicker: false,
      showColorPicker: false,
      // 元素操作按钮
      showElementButtons: false,
      elementButtonsPosition: {
        x: 0,
        y: 0
      },
      buttonHideTimer: null,
      // 缩放和旋转控制（保留基础变量用于其他功能）
      scaleStartDistance: 0,
      rotateStartAngle: 0
    };
  },
  //
  computed: {},
  mounted: function mounted() {
    this.initCanvas();
    // 初始化防抖函数
    this.debouncedDrawCanvas = this.debounce(this.drawCanvas, 16); // 约60fps
  },
  onShow: function onShow() {
    // 检查是否有选择的背景图片
    this.checkSelectedBackground();
  },
  beforeDestroy: function beforeDestroy() {
    // 清理资源
    if (this.canvasContext) {
      this.canvasContext = null;
    }
    this.canvasElements = [];

    // 清理定时器
    if (this.buttonHideTimer) {
      clearTimeout(this.buttonHideTimer);
      this.buttonHideTimer = null;
    }
  },
  methods: {
    // 初始化画布
    initCanvas: function initCanvas() {
      var _this = this;
      var systemInfo = uni.getSystemInfoSync();

      // 获取状态栏高度和安全区域信息
      var statusBarHeight = systemInfo.statusBarHeight || 0;
      var safeAreaTop = systemInfo.safeArea ? systemInfo.safeArea.top : statusBarHeight;

      // 计算可用屏幕高度（减去状态栏、导航栏等系统UI占用的空间）
      var availableHeight = systemInfo.windowHeight - safeAreaTop;

      // 考虑页面其他元素的空间占用
      // 预估其他UI元素总高度：模板设置(80px) + 背景设置(80px) + 批量操作(60px) + 属性面板(200px) + 保存按钮(75px) + 各种margin/padding(80px)
      var otherElementsHeight = 80 + 80 + 60 + 200 + 75 + 80;

      // 计算画布容器可用高度
      var containerAvailableHeight = Math.max(300, availableHeight - otherElementsHeight);

      // 考虑preview-container的margin(20px)、padding(16px)和border(4px)
      var containerOverhead = 20 + 16 + 4; // margin: 10px*2 + padding: 8px*2 + border: 2px*2
      var maxCanvasHeight = containerAvailableHeight - containerOverhead;
      console.log("maxCanvasHeight", maxCanvasHeight);
      // 电子桌牌标准比例 800:480 = 5:3
      var aspectRatio = 5 / 3;

      // 计算画布可用宽度（考虑容器的margin、padding和border）
      var widthOverhead = 20 + 16 + 4; // margin: 10px*2 + padding: 8px*2 + border: 2px*2
      var maxCanvasWidth = systemInfo.windowWidth - widthOverhead;

      // 根据比例和可用空间计算最终尺寸
      var finalWidth, finalHeight;

      // 按宽度优先计算
      finalWidth = maxCanvasWidth;
      finalHeight = finalWidth / aspectRatio;

      // 如果高度超出限制，则按高度重新计算
      if (finalHeight > maxCanvasHeight) {
        finalHeight = maxCanvasHeight;
        finalWidth = finalHeight * aspectRatio;
      }

      // 确保最小尺寸，但要考虑屏幕限制
      var minWidth = Math.min(320, maxCanvasWidth); // 最小宽度不超过可用宽度
      var minHeight = minWidth / aspectRatio;

      // 确保尺寸不超出限制
      finalWidth = Math.min(finalWidth, maxCanvasWidth);
      finalHeight = Math.min(finalHeight, maxCanvasHeight);
      this.canvasWidth = Math.max(minWidth, Math.floor(finalWidth));
      this.canvasHeight = Math.max(minHeight, Math.floor(finalHeight));

      // 输出调试信息
      console.log('画布尺寸计算:', {
        screenSize: "".concat(systemInfo.windowWidth, "x").concat(systemInfo.windowHeight),
        statusBarHeight: statusBarHeight,
        safeAreaTop: safeAreaTop,
        availableHeight: availableHeight,
        otherElementsHeight: otherElementsHeight,
        containerAvailableHeight: containerAvailableHeight,
        maxCanvasWidth: maxCanvasWidth,
        maxCanvasHeight: maxCanvasHeight,
        finalSize: "".concat(this.canvasWidth, "x").concat(this.canvasHeight),
        aspectRatio: (this.canvasWidth / this.canvasHeight).toFixed(2)
      });
      this.$nextTick(function () {
        try {
          _this.canvasContext = uni.createCanvasContext('templateCanvas', _this);
          if (_this.canvasContext) {
            // 设置canvas的实际渲染尺寸
            if (typeof _this.canvasContext.setCanvasSize === 'function') {
              _this.canvasContext.setCanvasSize(_this.canvasWidth, _this.canvasHeight);
            }
            _this.drawCanvas();
          } else {
            console.error('Canvas context creation failed');
          }
        } catch (error) {
          console.error('Canvas initialization error:', error);
        }
      });
    },
    // 绘制画布 - 优化版本
    drawCanvas: function drawCanvas() {
      var _this2 = this;
      if (!this.canvasContext) {
        console.warn('Canvas context not available');
        return;
      }
      if (!this.canvasWidth || !this.canvasHeight) {
        console.warn('Canvas dimensions not set');
        return;
      }
      try {
        // 清空画布
        this.canvasContext.clearRect(0, 0, this.canvasWidth, this.canvasHeight);

        // 绘制背景
        this.drawBackground();

        // 绘制选中元素的高亮边框
        if (this.selectedElement) {
          this.drawElementHighlight(this.selectedElement);
        }

        // 绘制元素
        this.canvasElements.forEach(function (element) {
          _this2.drawElement(element);
        });
        this.canvasContext.draw();
      } catch (error) {
        console.error('Canvas drawing error:', error);
      }
    },
    // 绘制背景
    drawBackground: function drawBackground() {
      var ctx = this.canvasContext;
      switch (this.backgroundType) {
        case 'select':
          if (this.backgroundImageUrl) {
            // 绘制背景图片
            ctx.drawImage(this.backgroundImageUrl, 0, 0, this.canvasWidth, this.canvasHeight);
          } else {
            // 没有选择图片时使用默认白色背景
            ctx.setFillStyle('#FFFFFF');
            ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
          }
          break;
        case 'solid':
          // 绘制纯色背景
          ctx.setFillStyle(this.backgroundColor || '#FFFFFF');
          ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
          break;
        case 'clear':
          // 透明背景，不绘制任何背景
          break;
        default:
          // 默认白色背景
          ctx.setFillStyle('#FFFFFF');
          ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
          break;
      }
    },
    // 绘制元素高亮边框
    drawElementHighlight: function drawElementHighlight(element) {
      // 设置高亮边框样式
      this.canvasContext.setStrokeStyle('#007AFF');
      this.canvasContext.setLineWidth(2);
      if (element.type === 'text') {
        var width = this.getElementWidth(element);
        var fontSize = element.fontSize || this.fontSize;

        // 计算文本边界，考虑基线位置
        // element.y 是基线位置，文本顶部在基线上方 fontSize * 0.8
        var textTop = element.y - fontSize * 0.8;
        var textBottom = element.y + fontSize * 0.2;
        var textHeight = textBottom - textTop;

        // 绘制高亮边框，添加5px边距
        this.canvasContext.strokeRect(element.x - width / 2 - 5, textTop - 5, width + 10, textHeight + 10);
      } else if (element.type === 'image') {
        // 图片元素的高亮边框
        var padding = 5; // 边框内边距
        this.canvasContext.strokeRect(element.x - element.width / 2 - padding, element.y - element.height / 2 - padding, element.width + padding * 2, element.height + padding * 2);
      }
    },
    // 绘制元素
    drawElement: function drawElement(element) {
      if (element.type === 'text') {
        this.canvasContext.setFillStyle(element.color || this.textColor);
        this.canvasContext.setFontSize(element.fontSize || this.fontSize);

        // 构建字体样式字符串
        var fontWeight = element.fontWeight || 'normal';
        var fontStyle = element.fontStyle || 'normal';
        var fontFamily = element.fontFamily || this.fontFamilies[0];
        var fontSize = element.fontSize || this.fontSize;

        // 设置字体样式 - 使用uni-app canvas的正确方式
        // 注意：uni-app的canvas可能不完全支持CSS font属性，需要分别设置
        this.canvasContext.setFontSize(fontSize);

        // 尝试设置字体样式（某些平台可能不支持）
        try {
          var fontString = "".concat(fontStyle, " ").concat(fontWeight, " ").concat(fontSize, "px ").concat(fontFamily);
          if (this.canvasContext.font !== undefined) {
            this.canvasContext.font = fontString;
          }
        } catch (e) {
          console.warn('设置字体样式失败，使用默认样式:', e);
        }

        // 设置文本对齐方式
        this.canvasContext.textAlign = 'center';

        // 绘制文本
        this.canvasContext.fillText(element.text, element.x, element.y);

        // 处理文本装饰（下划线和删除线）
        if (element.textDecoration && element.textDecoration !== 'none') {
          this.drawTextDecoration(element);
        }
      } else if (element.type === 'image') {
        // 绘制图片（支持旋转）
        if (element.src) {
          var rotation = element.rotation || 0;
          if (rotation !== 0) {
            // 保存当前状态
            this.canvasContext.save();

            // 移动到图片中心点
            this.canvasContext.translate(element.x, element.y);

            // 旋转
            this.canvasContext.rotate(rotation * Math.PI / 180);

            // 绘制图片（相对于旋转后的坐标系）
            this.canvasContext.drawImage(element.src, -element.width / 2, -element.height / 2, element.width, element.height);

            // 恢复状态
            this.canvasContext.restore();
          } else {
            // 无旋转时的正常绘制
            this.canvasContext.drawImage(element.src, element.x - element.width / 2, element.y - element.height / 2, element.width, element.height);
          }
        }
      }
    },
    // 绘制文本装饰（下划线和删除线）
    drawTextDecoration: function drawTextDecoration(element) {
      if (!element.textDecoration || element.textDecoration === 'none') return;
      var fontSize = element.fontSize || this.fontSize;
      var textWidth = this.getElementWidth(element);
      var lineThickness = Math.max(1, fontSize / 20); // 线条粗细根据字体大小调整

      // 设置线条样式
      this.canvasContext.setStrokeStyle(element.color || this.textColor);
      this.canvasContext.setLineWidth(lineThickness);
      var startX = element.x - textWidth / 2;
      var endX = element.x + textWidth / 2;
      if (element.textDecoration === 'underline') {
        // 下划线位置：文本基线下方
        var lineY = element.y + fontSize * 0.1;
        this.canvasContext.beginPath();
        this.canvasContext.moveTo(startX, lineY);
        this.canvasContext.lineTo(endX, lineY);
        this.canvasContext.stroke();
      } else if (element.textDecoration === 'line-through') {
        // 删除线位置：文本中间
        var _lineY = element.y - fontSize * 0.3;
        this.canvasContext.beginPath();
        this.canvasContext.moveTo(startX, _lineY);
        this.canvasContext.lineTo(endX, _lineY);
        this.canvasContext.stroke();
      }
    },
    // 画布触摸事件
    onCanvasTouchStart: function onCanvasTouchStart(e) {
      var touch = e.touches[0];
      this.lastTouchX = touch.x;
      this.lastTouchY = touch.y;
      this.touchStartTime = Date.now();

      // 检查是否点击了元素
      var element = this.getElementAtPosition(touch.x, touch.y);
      if (element) {
        this.selectedElement = element;
        this.dragging = true;
        // 显示操作按钮
        this.showElementOperationButtons(element);
        // 重绘画布以显示高亮边框
        this.drawCanvas();
      } else {
        // 点击空白区域，隐藏按钮
        this.hideElementButtons();
        this.selectedElement = null;
        // 重绘画布以移除高亮边框
        this.drawCanvas();
      }
    },
    onCanvasTouchMove: function onCanvasTouchMove(e) {
      if (!this.dragging || !this.selectedElement) return;
      var touch = e.touches[0];
      var deltaX = touch.x - this.lastTouchX;
      var deltaY = touch.y - this.lastTouchY;

      // 计算新位置
      var newX = this.selectedElement.x + deltaX;
      var newY = this.selectedElement.y + deltaY;

      // 边界检测 - 防止元素超出画布范围
      var elementWidth = this.getElementWidth(this.selectedElement);
      var elementHeight = this.getElementHeight(this.selectedElement);

      // 改进边界检测，确保元素完全在画布内
      // 设置合理的边距，防止元素贴边或超出
      var margin = 10;

      // 对于文本元素，考虑文本对齐方式的影响
      if (this.selectedElement.type === 'text') {
        var fontSize = this.selectedElement.fontSize || this.fontSize;

        // 文本居中对齐
        var minX = margin + elementWidth / 2;
        var maxX = this.canvasWidth - margin - elementWidth / 2;

        // 考虑文本基线偏移，newY是基线位置
        // 文本顶部需要 fontSize * 0.8 的空间，底部需要 fontSize * 0.2 的空间
        var minY = margin + fontSize * 0.8;
        var maxY = this.canvasHeight - margin - fontSize * 0.2;
        newX = Math.max(minX, Math.min(newX, maxX));
        newY = Math.max(minY, Math.min(newY, maxY));
      } else if (this.selectedElement.type === 'image') {
        // 图片元素的边界检测
        var _minX = margin + elementWidth / 2;
        var _maxX = this.canvasWidth - margin - elementWidth / 2;
        var _minY = margin + elementHeight / 2;
        var _maxY = this.canvasHeight - margin - elementHeight / 2;
        newX = Math.max(_minX, Math.min(newX, _maxX));
        newY = Math.max(_minY, Math.min(newY, _maxY));
      } else {
        // 其他类型元素的边界检测
        var _minX2 = margin + elementWidth / 2;
        var _maxX2 = this.canvasWidth - margin - elementWidth / 2;
        var _minY2 = margin + elementHeight / 2;
        var _maxY2 = this.canvasHeight - margin - elementHeight / 2;
        newX = Math.max(_minX2, Math.min(newX, _maxX2));
        newY = Math.max(_minY2, Math.min(newY, _maxY2));
      }
      this.selectedElement.x = newX;
      this.selectedElement.y = newY;
      this.lastTouchX = touch.x;
      this.lastTouchY = touch.y;

      // 使用防抖渲染提高性能
      this.debouncedDrawCanvas();
    },
    onCanvasTouchEnd: function onCanvasTouchEnd(e) {
      var touchEndTime = Date.now();
      var touchDuration = touchEndTime - this.touchStartTime;

      // 如果是拖拽结束，更新按钮位置
      if (this.dragging && this.selectedElement) {
        this.updateElementButtonsPosition(this.selectedElement);
      }
      this.dragging = false;
    },
    // 打开元素属性编辑弹窗
    openElementPropertyPopup: function openElementPropertyPopup() {
      if (!this.selectedElement) return;

      // 设置当前编辑的元素
      this.editingElement = this.selectedElement;

      // 显示弹窗
      this.showPropertyPopup = true;
    },
    // 关闭属性编辑弹窗
    closePropertyPopup: function closePropertyPopup() {
      this.showPropertyPopup = false;
      this.editingElement = null;
    },
    // 文字内容编辑
    onTextChange: function onTextChange(e) {
      var _this3 = this;
      var newText = e.detail.value.trim();
      console.log(this.editingElement.text);
      if (newText === '') {
        uni.showToast({
          title: '文字内容不能为空',
          icon: 'none'
        });
        // 恢复原来的文字
        this.$nextTick(function () {
          _this3.editingElement.text = _this3.editingElement.text || '文字';
        });
        return;
      }
      this.editingElement.text = newText;
      this.drawCanvas();
    },
    // 获取字体索引
    getFontFamilyIndex: function getFontFamilyIndex() {
      if (!this.editingElement || !this.editingElement.fontFamily) return 0;
      return this.fontFamilies.indexOf(this.editingElement.fontFamily) || 0;
    },
    // 属性编辑相关方法（立即生效）
    onFontPickerConfirm: function onFontPickerConfirm(e) {
      var indexs = e.indexs,
        value = e.value;
      if (this.editingElement && this.editingElement.type === 'text') {
        this.editingElement.fontFamily = value[0];
        this.drawCanvas();
      }
      this.showFontPicker = false;
    },
    toggleFontWeight: function toggleFontWeight() {
      if (!this.editingElement || this.editingElement.type !== 'text') return;
      this.editingElement.fontWeight = this.editingElement.fontWeight === 'bold' ? 'normal' : 'bold';
      this.drawCanvas();
    },
    toggleFontStyle: function toggleFontStyle() {
      if (!this.editingElement || this.editingElement.type !== 'text') return;
      this.editingElement.fontStyle = this.editingElement.fontStyle === 'italic' ? 'normal' : 'italic';
      this.drawCanvas();
    },
    toggleTextDecoration: function toggleTextDecoration() {
      if (!this.editingElement || this.editingElement.type !== 'text') return;
      if (this.editingElement.textDecoration === 'underline') {
        this.editingElement.textDecoration = 'none';
      } else {
        this.editingElement.textDecoration = 'underline';
      }
      this.drawCanvas();
    },
    toggleStrikethrough: function toggleStrikethrough() {
      if (!this.editingElement || this.editingElement.type !== 'text') return;
      if (this.editingElement.textDecoration === 'line-through') {
        this.editingElement.textDecoration = 'none';
      } else {
        this.editingElement.textDecoration = 'line-through';
      }
      this.drawCanvas();
    },
    onFontSizeChange: function onFontSizeChange(value) {
      if (this.editingElement && this.editingElement.type === 'text') {
        this.editingElement.fontSize = value;
        this.drawCanvas();
      }
    },
    // 获取指定位置的元素 - 改进的碰撞检测算法
    getElementAtPosition: function getElementAtPosition(x, y) {
      // 从后往前遍历，优先选择最上层的元素
      for (var i = this.canvasElements.length - 1; i >= 0; i--) {
        var element = this.canvasElements[i];
        if (this.isPointInElement(x, y, element)) {
          return element;
        }
      }
      return null;
    },
    // 检查点是否在元素内
    isPointInElement: function isPointInElement(x, y, element) {
      if (element.type === 'text') {
        var textWidth = this.getElementWidth(element);
        var textHeight = this.getElementHeight(element);
        var fontSize = element.fontSize || this.fontSize;

        // 考虑文本基线偏移，element.y是基线位置
        // 文本顶部位置 = element.y - fontSize * 0.8
        // 文本底部位置 = element.y + fontSize * 0.2
        var textTop = element.y - fontSize * 0.8;
        var textBottom = element.y + fontSize * 0.2;
        return x >= element.x - textWidth / 2 && x <= element.x + textWidth / 2 && y >= textTop && y <= textBottom;
      } else if (element.type === 'image') {
        // 图片元素的碰撞检测
        return x >= element.x - element.width / 2 && x <= element.x + element.width / 2 && y >= element.y - element.height / 2 && y <= element.y + element.height / 2;
      }
      return false;
    },
    // 获取元素宽度
    getElementWidth: function getElementWidth(element) {
      if (element.type === 'text') {
        var fontSize = element.fontSize || this.fontSize;
        var text = element.text || '';

        // 对于模板字段，使用字段名称长度来估算宽度
        if (element.isTemplate && element.fieldName) {
          return element.fieldName.length * fontSize * 0.8;
        }

        // 改进的文本宽度计算
        // 考虑中文字符和英文字符的不同宽度
        var width = 0;
        for (var i = 0; i < text.length; i++) {
          var char = text.charAt(i);
          // 中文字符、全角字符宽度约为fontSize
          if (/[\u4e00-\u9fa5\uff00-\uffef]/.test(char)) {
            width += fontSize * 0.9;
          } else {
            // 英文字符、数字等宽度约为fontSize的0.5-0.6倍
            width += fontSize * 0.55;
          }
        }
        return Math.max(width, fontSize * 0.5); // 最小宽度
      } else if (element.type === 'image') {
        return element.width || 100;
      }
      return 50; // 默认宽度
    },
    // 获取元素高度
    getElementHeight: function getElementHeight(element) {
      if (element.type === 'text') {
        return element.fontSize || this.fontSize;
      } else if (element.type === 'image') {
        return element.height || 100;
      }
      return 20; // 默认高度
    },
    //添加元素 - 增强版本
    addElement: function addElement(x, y) {
      // 如果是模板文字类型，通过按钮打开弹窗，这里不处理
      if (this.insertType === 'template-text') {
        return;
      }
      if (this.insertType === 'fixed-text') {
        // 改进的边界验证
        var estimatedWidth = '固定文字'.length * this.fontSize * 0.6;
        var margin = 10;

        // 文本居中对齐
        var minX = margin + estimatedWidth / 2;
        var maxX = this.canvasWidth - margin - estimatedWidth / 2;

        // 考虑文本基线偏移，Y坐标应该是基线位置
        // 文本顶部需要 fontSize * 0.8 的空间，底部需要 fontSize * 0.2 的空间
        var minY = margin + this.fontSize * 0.8;
        var maxY = this.canvasHeight - margin - this.fontSize * 0.2;

        // 使用验证后的位置
        var snappedX = Math.max(minX, Math.min(x, maxX));
        var snappedY = Math.max(minY, Math.min(y, maxY));
        var newElement = {
          id: Date.now(),
          type: 'text',
          text: '固定文字',
          isTemplate: false,
          x: snappedX,
          y: snappedY,
          color: this.textColor,
          fontSize: this.fontSize,
          fontWeight: this.fontWeight,
          fontStyle: this.fontStyle,
          fontFamily: this.fontFamilies[0],
          textDecoration: this.textDecoration
        };
        this.canvasElements.push(newElement);
        this.selectedElement = newElement; // 自动选中新添加的元素
        this.drawCanvas();

        // 提供用户反馈
        uni.showToast({
          title: '元素已添加',
          icon: 'success',
          duration: 1000
        });
      }
    },
    //设置插入类型
    setInsertType: function setInsertType(type) {
      console.log('setInsertType called with:', type);
      this.insertType = type;
      // 如果选择模板文字，显示字段选择弹窗
      if (type === 'template-text') {
        // 打开模板文字弹窗
        this.showTemplatePopup = true;
      } else if (type === 'fixed-text') {
        // 处理固定文字逻辑
        this.showFixedTextInput();
      } else if (type === 'image') {
        // 处理图片逻辑
        this.addImage();
      }
      // 添加用户反馈
      uni.showToast({
        title: "\u5DF2\u9009\u62E9: ".concat(type === 'template-text' ? '模板文字' : type === 'fixed-text' ? '固定文字' : '图片'),
        icon: 'none',
        duration: 1000
      });
      // 强制更新视图
      this.$forceUpdate();
    },
    // 显示固定文字输入弹窗
    showFixedTextInput: function showFixedTextInput() {
      var _this4 = this;
      uni.showModal({
        title: '添加固定文字',
        content: '请输入文字内容',
        editable: true,
        placeholderText: '请输入文字内容',
        success: function success(res) {
          if (res.confirm && res.content && res.content.trim()) {
            _this4.addFixedText(res.content.trim());
          } else if (res.confirm && (!res.content || !res.content.trim())) {
            uni.showToast({
              title: '文字内容不能为空',
              icon: 'none'
            });
          }
        }
      });
    },
    // 添加固定文字
    addFixedText: function addFixedText() {
      var text = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '固定文字';
      // 创建临时元素用于计算宽度
      var tempElement = {
        type: 'text',
        text: text,
        fontSize: this.fontSize
      };

      // 计算文字宽度
      var textWidth = this.getElementWidth(tempElement);
      var margin = 10;

      // 计算安全的初始位置
      var minX = margin + textWidth / 2;
      var maxX = this.canvasWidth - margin - textWidth / 2;
      var minY = margin + this.fontSize * 0.8;
      var maxY = this.canvasHeight - margin - this.fontSize * 0.2;

      // 使用画布中心位置，但确保在边界内
      var centerX = this.canvasWidth / 2;
      var centerY = this.canvasHeight / 2;
      var safeX = Math.max(minX, Math.min(centerX, maxX));
      var safeY = Math.max(minY, Math.min(centerY, maxY));
      var newElement = {
        id: Date.now(),
        type: 'text',
        text: text,
        x: safeX,
        y: safeY,
        fontSize: this.fontSize,
        fontFamily: this.fontFamilies[0],
        color: this.textColor,
        fontWeight: this.fontWeight,
        fontStyle: this.fontStyle,
        textDecoration: this.textDecoration
      };
      this.canvasElements.push(newElement);

      // 自动选中新添加的元素
      this.selectedElement = newElement;
      this.drawCanvas();
      uni.showToast({
        title: '文字已添加',
        icon: 'success',
        duration: 1000
      });
    },
    // 添加图片
    addImage: function addImage() {
      var _this5 = this;
      uni.chooseImage({
        count: 1,
        sourceType: ['album', 'camera'],
        success: function success(res) {
          var tempFilePath = res.tempFilePaths[0];
          var newElement = {
            id: Date.now(),
            type: 'image',
            src: tempFilePath,
            x: _this5.canvasWidth / 2,
            y: _this5.canvasHeight / 2,
            width: 100,
            height: 100,
            rotation: 0
          };
          _this5.canvasElements.push(newElement);
          _this5.drawCanvas();
        },
        fail: function fail(err) {
          uni.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      });
    },
    //设置背景类型
    setBackgroundType: function setBackgroundType(type) {
      this.backgroundType = type;

      // 根据背景类型执行相应操作
      switch (type) {
        case 'select':
          this.selectBackgroundImage();
          break;
        case 'solid':
          this.setSolidBackground();
          break;
        case 'clear':
          this.clearBackground();
          break;
      }
      this.drawCanvas();
    },
    // 选择背景图片
    selectBackgroundImage: function selectBackgroundImage() {
      uni.navigateTo({
        url: '/pages/subPackage/template/backgroundList?select=true',
        fail: function fail(error) {
          console.error('跳转背景选择页面失败:', error);
          uni.showToast({
            title: '打开背景选择失败',
            icon: 'none'
          });
        }
      });
    },
    // 检查选择的背景图片
    checkSelectedBackground: function checkSelectedBackground() {
      try {
        var selectedBackground = uni.getStorageSync('selectedBackgroundImage');
        if (selectedBackground) {
          // 设置背景图片
          this.backgroundImageUrl = selectedBackground;
          this.backgroundType = 'select';
          this.drawCanvas();

          // 清除存储，避免重复使用
          uni.removeStorageSync('selectedBackgroundImage');
          uni.showToast({
            title: '背景设置成功',
            icon: 'success'
          });
        }
      } catch (error) {
        console.error('检查背景选择失败:', error);
      }
    },
    // 设置桌牌类型
    setCardType: function setCardType(type) {
      this.cardType = type;

      // 更新颜色选择器选项
      if (type === 'three-color') {
        this.colorOptions = (0, _toConsumableArray2.default)(this.threeColorOptions);
        this.colorValues = (0, _toConsumableArray2.default)(this.threeColorValues);
      } else {
        this.colorOptions = (0, _toConsumableArray2.default)(this.sixColorOptions);
        this.colorValues = (0, _toConsumableArray2.default)(this.sixColorValues);
      }
      uni.showToast({
        title: "\u5DF2\u5207\u6362\u5230".concat(type === 'three-color' ? '三色' : '六色', "\u684C\u724C\u6A21\u5F0F"),
        icon: 'success'
      });
    },
    // 设置纯色背景
    setSolidBackground: function setSolidBackground() {
      var _this6 = this;
      // 根据桌牌类型提供不同的背景选择
      var itemList = [];
      var colorMap = {};
      if (this.cardType === 'three-color') {
        // 三色桌牌：黑、白、红
        itemList = ['黑色背景', '白色背景', '红色背景'];
        colorMap = {
          0: '#000000',
          1: '#FFFFFF',
          2: '#FF0000'
        };
      } else {
        // 六色桌牌：黑、白、红、黄、蓝、绿
        itemList = ['黑色背景', '白色背景', '红色背景', '黄色背景', '蓝色背景', '绿色背景'];
        colorMap = {
          0: '#000000',
          1: '#FFFFFF',
          2: '#FF0000',
          3: '#FFFF00',
          4: '#0000FF',
          5: '#00FF00'
        };
      }
      uni.showActionSheet({
        itemList: itemList,
        success: function success(res) {
          var lastIndex = itemList.length - 1;

          // 使用预设颜色
          _this6.backgroundColor = colorMap[res.tapIndex];
          _this6.backgroundImageUrl = '';
          _this6.drawCanvas();
          uni.showToast({
            title: '背景设置成功',
            icon: 'success'
          });
        }
      });
    },
    // 清除背景
    clearBackground: function clearBackground() {
      var _this7 = this;
      uni.showModal({
        title: '清除背景',
        content: '确定要清除当前背景吗？',
        success: function success(res) {
          if (res.confirm) {
            _this7.backgroundImageUrl = '';
            _this7.backgroundColor = 'transparent';
            _this7.drawCanvas();
            uni.showToast({
              title: '背景已清除',
              icon: 'success'
            });
          }
        }
      });
    },
    // 颜色选择相关方法
    getColorIndex: function getColorIndex() {
      if (!this.editingElement || !this.editingElement.color) return 0;
      var index = this.colorValues.indexOf(this.editingElement.color);
      return index >= 0 ? index : 0;
    },
    getColorName: function getColorName() {
      if (!this.editingElement || !this.editingElement.color) return this.colorOptions[0];
      var index = this.colorValues.indexOf(this.editingElement.color);
      return index >= 0 ? this.colorOptions[index] : this.colorOptions[0];
    },
    onColorPickerConfirm: function onColorPickerConfirm(e) {
      var indexs = e.indexs,
        value = e.value;
      if (this.editingElement && this.editingElement.type === 'text') {
        var colorIndex = indexs[0];
        this.editingElement.color = this.colorValues[colorIndex];
        this.drawCanvas();
      }
      this.showColorPicker = false;
    },
    // 防抖函数
    debounce: function debounce(func, wait) {
      var timeout;
      return function executedFunction() {
        var _this8 = this;
        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
          args[_key] = arguments[_key];
        }
        var later = function later() {
          clearTimeout(timeout);
          func.apply(_this8, args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },
    // 保存模板 - 增强版本
    saveTemplate: function saveTemplate() {
      var _this9 = this;
      // 验证模板数据
      if (!this.templateName.trim()) {
        uni.showToast({
          title: '请输入模板名称',
          icon: 'none'
        });
        return;
      }
      if (this.canvasElements.length === 0) {
        uni.showModal({
          title: '提示',
          content: '模板中没有任何元素，确定要保存吗？',
          success: function success(res) {
            if (res.confirm) {
              _this9.performSave();
            }
          }
        });
        return;
      }
      this.performSave();
    },
    // 执行保存操作
    performSave: function performSave() {
      var _this10 = this;
      var templateData = {
        name: this.templateName.trim(),
        canvasSize: {
          width: this.canvasWidth,
          height: this.canvasHeight
        },
        elements: this.canvasElements.map(function (el) {
          return _objectSpread(_objectSpread({}, el), {}, {
            // 确保所有属性都被保存
            fontFamily: _this10.fontFamilies[_this10.fontFamilyIndex]
          });
        }),
        settings: {
          backgroundType: this.backgroundType,
          backgroundImageUrl: this.backgroundImageUrl,
          backgroundColor: this.backgroundColor
        },
        createdAt: new Date().toISOString(),
        version: '1.0'
      };

      // 这里可以添加实际的保存逻辑，如发送到服务器
      console.log('保存模板数据:', templateData);
      uni.showToast({
        title: '模板保存成功',
        icon: 'success'
      });
    },
    // 模板文字弹窗相关方法
    confirmAddTemplateText: function confirmAddTemplateText() {
      var _this11 = this;
      // 检查是否有输入内容
      if (!this.templateFields.name && !this.templateFields.position && !this.templateFields.company && !this.templateFields.other) {
        uni.showToast({
          title: '请至少输入一项内容',
          icon: 'none'
        });
        return;
      }

      // 获取画布中心位置
      var centerX = this.canvasWidth / 2;
      var centerY = this.canvasHeight / 2;

      // 为每个有内容的字段创建独立的元素对象，设置字体层次和居中布局
      // 根据画布高度动态调整间距，适应不同屏幕尺寸
      var baseSpacing = Math.min(this.canvasHeight * 0.15, 40); // 基础间距不超过40px

      // 根据画布大小动态计算字体粗细
      var canvasArea = this.canvasWidth * this.canvasHeight;
      var baseFontWeight = Math.min(Math.max(canvasArea / 50000, 0.5), 1.2); // 基础字体粗细系数

      var fieldConfigs = [{
        key: 'name',
        label: '姓名',
        offsetY: -baseSpacing * 1.5,
        fontSizeRatio: 1.3,
        fontWeight: Math.round(700 * baseFontWeight).toString()
      }, {
        key: 'position',
        label: '职位',
        offsetY: -baseSpacing * 0.5,
        fontSizeRatio: 1.0,
        fontWeight: Math.round(600 * baseFontWeight).toString()
      }, {
        key: 'company',
        label: '公司',
        offsetY: baseSpacing * 0.5,
        fontSizeRatio: 0.9,
        fontWeight: Math.round(500 * baseFontWeight).toString()
      }, {
        key: 'other',
        label: '其他',
        offsetY: baseSpacing * 1.5,
        fontSizeRatio: 0.8,
        fontWeight: Math.round(400 * baseFontWeight).toString()
      }];
      var addedCount = 0;
      var newElements = [];
      fieldConfigs.forEach(function (config) {
        var fieldValue = _this11.templateFields[config.key];
        if (fieldValue && fieldValue.trim()) {
          var fontSize = Math.round(_this11.fontSize * config.fontSizeRatio);

          // 计算文本尺寸用于边界检测
          var textWidth = fieldValue.length * fontSize * 0.6;
          var textHeight = fontSize * _this11.heightStretch;

          // 边界检测，确保文本不超出画布
          var margin = 20;
          var minX = margin + textWidth / 2;
          var maxX = _this11.canvasWidth - margin - textWidth / 2;
          // 考虑文本基线偏移，Y坐标应该是基线位置
          var minY = margin + fontSize * 0.8;
          var maxY = _this11.canvasHeight - margin - fontSize * 0.2;

          // 应用边界约束
          var safeX = Math.max(minX, Math.min(centerX, maxX));
          var safeY = Math.max(minY, Math.min(centerY + config.offsetY, maxY));
          var newElement = {
            id: Date.now() + addedCount,
            type: 'text',
            text: fieldValue,
            isTemplate: true,
            templateKey: config.key,
            templateLabel: config.label,
            templateData: (0, _defineProperty2.default)({}, config.key, fieldValue),
            x: safeX,
            y: safeY,
            color: _this11.textColor,
            fontSize: fontSize,
            fontWeight: config.fontWeight,
            fontStyle: _this11.fontStyle,
            fontFamily: _this11.fontFamilies[0],
            textDecoration: _this11.textDecoration
          };
          newElements.push(newElement);
          _this11.canvasElements.push(newElement);
          addedCount++;
        }
      });

      // 选中最后一个添加的元素
      if (newElements.length > 0) {
        this.selectedElement = newElements[newElements.length - 1];
      }
      this.drawCanvas();

      // 关闭弹窗并清空输入
      this.onTemplatePopupClose();
      uni.showToast({
        title: "\u5DF2\u6DFB\u52A0".concat(addedCount, "\u4E2A\u6A21\u677F\u5B57\u6BB5"),
        icon: 'success',
        duration: 1500
      });
    },
    onTemplatePopupClose: function onTemplatePopupClose() {
      // 关闭弹窗时重置插入类型
      this.showTemplatePopup = false;
      this.insertType = '';
      // 清空输入字段
      this.templateFields = {
        name: '',
        position: '',
        company: '',
        other: ''
      };
    },
    // 批量修改模板字段方法
    batchUpdateTemplateFields: function batchUpdateTemplateFields(updates) {
      // updates 格式: { key: newValue, key2: newValue2 }
      var updatedCount = 0;
      this.canvasElements.forEach(function (element) {
        if (element.isTemplate && element.templateKey) {
          var newValue = updates[element.templateKey];
          if (newValue !== undefined && newValue !== null) {
            // 更新元素文本
            element.text = newValue;
            // 更新模板数据
            element.templateData[element.templateKey] = newValue;
            updatedCount++;
          }
        }
      });
      if (updatedCount > 0) {
        this.drawCanvas();
        uni.showToast({
          title: "\u5DF2\u66F4\u65B0".concat(updatedCount, "\u4E2A\u5B57\u6BB5"),
          icon: 'success',
          duration: 1500
        });
      }
      return updatedCount;
    },
    // 根据模板key获取所有相关元素
    getElementsByTemplateKey: function getElementsByTemplateKey(templateKey) {
      return this.canvasElements.filter(function (element) {
        return element.isTemplate && element.templateKey === templateKey;
      });
    },
    // 删除指定模板key的所有元素
    removeElementsByTemplateKey: function removeElementsByTemplateKey(templateKey) {
      var initialLength = this.canvasElements.length;
      this.canvasElements = this.canvasElements.filter(function (element) {
        return !(element.isTemplate && element.templateKey === templateKey);
      });
      var removedCount = initialLength - this.canvasElements.length;
      if (removedCount > 0) {
        this.selectedElement = null;
        this.drawCanvas();
        uni.showToast({
          title: "\u5DF2\u5220\u9664".concat(removedCount, "\u4E2A").concat(templateKey, "\u5B57\u6BB5"),
          icon: 'success',
          duration: 1500
        });
      }
      return removedCount;
    },
    // 获取所有模板字段的统计信息
    getTemplateFieldsStats: function getTemplateFieldsStats() {
      var stats = {};
      this.canvasElements.forEach(function (element) {
        if (element.isTemplate && element.templateKey) {
          if (!stats[element.templateKey]) {
            stats[element.templateKey] = {
              count: 0,
              label: element.templateLabel || element.templateKey,
              elements: []
            };
          }
          stats[element.templateKey].count++;
          stats[element.templateKey].elements.push(element);
        }
      });
      return stats;
    },
    // 预览弹窗相关方法
    openPreview: function openPreview() {
      var _this12 = this;
      // 生成预览图片
      this.previewLoading = true;
      this.showPreview = true;

      // 模拟生成预览图片的过程
      setTimeout(function () {
        // 这里应该调用画布截图API生成预览图
        _this12.generatePreviewImage();
      }, 500);
    },
    generatePreviewImage: function generatePreviewImage() {
      var _this13 = this;
      // 使用canvas生成预览图片
      uni.canvasToTempFilePath({
        canvasId: 'templateCanvas',
        success: function success(res) {
          _this13.previewImagePath = res.tempFilePath;
          _this13.previewLoading = false;
        },
        fail: function fail(err) {
          console.error('生成预览图片失败:', err);
          _this13.previewLoading = false;
          uni.showToast({
            title: '预览生成失败',
            icon: 'none'
          });
        }
      }, this);
    },
    closePreview: function closePreview() {
      this.showPreview = false;
      this.previewImagePath = '';
      this.previewLoading = false;
    },
    savePreviewImage: function savePreviewImage() {
      // 保存预览图片的逻辑
      uni.showToast({
        title: '图片保存功能待实现',
        icon: 'none'
      });
    },
    //批量操作方法
    centerAllElements: function centerAllElements() {
      var _this14 = this;
      if (this.canvasElements.length === 0) {
        uni.showToast({
          title: '没有可操作的元素',
          icon: 'none'
        });
        return;
      }

      // 按Y坐标排序元素
      var sortedElements = (0, _toConsumableArray2.default)(this.canvasElements).sort(function (a, b) {
        return a.y - b.y;
      });

      // 计算元素尺寸和边界
      var margin = 20; // 边界留白
      var elementsWithSize = sortedElements.map(function (element) {
        var textWidth = _this14.getElementWidth(element);
        var textHeight = element.fontSize || _this14.fontSize;
        return _objectSpread(_objectSpread({}, element), {}, {
          textWidth: textWidth,
          textHeight: textHeight
        });
      });
      // 计算可用空间和分布参数
      var maxTextWidth = Math.max.apply(Math, (0, _toConsumableArray2.default)(elementsWithSize.map(function (e) {
        return e.textWidth;
      })));
      var totalTextHeight = elementsWithSize.reduce(function (sum, e) {
        return sum + e.textHeight;
      }, 0);
      var availableWidth = this.canvasWidth - 2 * margin;
      var availableHeight = this.canvasHeight - 2 * margin;

      // 检查是否有足够空间
      if (maxTextWidth > availableWidth) {
        uni.showToast({
          title: '元素宽度超出画布范围',
          icon: 'none'
        });
        return;
      }

      // 计算垂直分布
      var canvasCenterX = this.canvasWidth / 2;
      var remainingHeight = availableHeight - totalTextHeight;
      var spacing = elementsWithSize.length > 1 ? remainingHeight / (elementsWithSize.length + 1) : remainingHeight / 2;

      // 重新分布元素，确保不超出边界
      var currentY = margin + spacing;
      elementsWithSize.forEach(function (element) {
        // 水平居中，确保不超出边界
        var halfWidth = element.textWidth / 2;
        element.x = Math.max(margin + halfWidth, Math.min(canvasCenterX, _this14.canvasWidth - margin - halfWidth));

        // 垂直分布，确保不超出边界
        element.y = Math.max(margin + element.textHeight / 2, Math.min(currentY + element.textHeight / 2, _this14.canvasHeight - margin - element.textHeight / 2));
        currentY += element.textHeight + spacing;
      });
      this.drawCanvas();
      uni.showToast({
        title: '元素已垂直分布',
        icon: 'success',
        duration: 1000
      });
    },
    // 清空画布
    clearAllElements: function clearAllElements() {
      var _this15 = this;
      uni.showModal({
        title: '确认清空',
        content: '确定要清空画布上的所有元素吗？此操作不可撤销。',
        success: function success(res) {
          if (res.confirm) {
            var elementCount = _this15.canvasElements.length;
            _this15.canvasElements = [];
            _this15.selectedElement = null;
            _this15.hideElementButtons();
            _this15.drawCanvas();
            uni.showToast({
              title: "\u5DF2\u6E05\u7A7A".concat(elementCount, "\u4E2A\u5143\u7D20"),
              icon: 'success',
              duration: 1500
            });
          }
        }
      });
    },
    // 显示元素操作按钮
    showElementOperationButtons: function showElementOperationButtons(element) {
      var _this16 = this;
      if (!element) return;

      // 清除之前的定时器
      if (this.buttonHideTimer) {
        clearTimeout(this.buttonHideTimer);
        this.buttonHideTimer = null;
      }

      // 计算按钮位置
      this.updateElementButtonsPosition(element);

      // 显示按钮
      this.showElementButtons = true;

      // 设置自动隐藏定时器
      this.buttonHideTimer = setTimeout(function () {
        _this16.hideElementButtons();
      }, 3000); // 3秒后自动隐藏
    },
    // 更新按钮位置
    updateElementButtonsPosition: function updateElementButtonsPosition(element) {
      if (!element) return;
      var elementWidth = this.getElementWidth(element);
      var elementHeight = this.getElementHeight(element);

      // 计算元素边界
      var elementLeft, elementTop, elementRight, elementBottom;
      if (element.type === 'text') {
        var fontSize = element.fontSize || this.fontSize;
        elementLeft = element.x - elementWidth / 2;
        elementRight = element.x + elementWidth / 2;
        elementTop = element.y - fontSize * 0.8;
        elementBottom = element.y + fontSize * 0.2;
      } else if (element.type === 'image') {
        // 图片元素边界
        elementLeft = element.x - element.width / 2;
        elementRight = element.x + element.width / 2;
        elementTop = element.y - element.height / 2;
        elementBottom = element.y + element.height / 2;
      }

      // 按钮尺寸
      var buttonSize = 32;
      var buttonSpacing = 8;

      // 根据元素类型计算按钮数量
      var buttonCount = element.type === 'image' ? 3 : 2;
      var totalButtonWidth = buttonSize * buttonCount + buttonSpacing * (buttonCount - 1);

      // 计算按钮位置（在元素右上角）
      var buttonX = elementRight + 5;
      var buttonY = elementTop - buttonSize - 5;

      // 边界检测，确保按钮不超出画布
      if (buttonX + totalButtonWidth > this.canvasWidth) {
        buttonX = elementLeft - totalButtonWidth - 5;
      }
      if (buttonY < 0) {
        buttonY = elementBottom + 5;
      }

      // 确保按钮不超出左边界
      if (buttonX < 0) {
        buttonX = 5;
      }

      // 确保按钮不超出下边界
      if (buttonY + buttonSize > this.canvasHeight) {
        buttonY = this.canvasHeight - buttonSize - 5;
      }
      this.elementButtonsPosition = {
        x: buttonX,
        y: buttonY
      };
    },
    // 隐藏元素操作按钮
    hideElementButtons: function hideElementButtons() {
      this.showElementButtons = false;
      if (this.buttonHideTimer) {
        clearTimeout(this.buttonHideTimer);
        this.buttonHideTimer = null;
      }
    },
    // 编辑选中元素
    editSelectedElement: function editSelectedElement() {
      if (!this.selectedElement) return;

      // 隐藏操作按钮
      this.hideElementButtons();

      // 打开属性编辑弹窗
      this.openElementPropertyPopup();
    },
    // 删除选中元素
    deleteSelectedElement: function deleteSelectedElement() {
      var _this17 = this;
      if (!this.selectedElement) return;
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个元素吗？',
        success: function success(res) {
          if (res.confirm) {
            // 找到元素索引并删除
            var index = _this17.canvasElements.findIndex(function (el) {
              return el.id === _this17.selectedElement.id;
            });
            if (index !== -1) {
              _this17.canvasElements.splice(index, 1);

              // 清除选中状态和按钮
              _this17.selectedElement = null;
              _this17.hideElementButtons();

              // 重绘画布
              _this17.drawCanvas();
              uni.showToast({
                title: '元素已删除',
                icon: 'success',
                duration: 1000
              });
            }
          }
        }
      });
    },
    // 放大图片
    zoomInImage: function zoomInImage() {
      if (!this.selectedElement || this.selectedElement.type !== 'image') return;
      console.log('放大图片');
      var currentWidth = this.selectedElement.width || 100;
      var currentHeight = this.selectedElement.height || 100;

      // 等比例放大5%
      var newWidth = currentWidth * 1.05;
      var newHeight = currentHeight * 1.05;

      // 边界检测，确保放大后的图片不超出画布
      var margin = 10;
      var maxWidth = this.canvasWidth - margin * 2;
      var maxHeight = this.canvasHeight - margin * 2;
      this.selectedElement.width = Math.min(newWidth, maxWidth);
      this.selectedElement.height = Math.min(newHeight, maxHeight);

      // 确保图片中心不超出边界
      var halfWidth = this.selectedElement.width / 2;
      var halfHeight = this.selectedElement.height / 2;
      this.selectedElement.x = Math.max(halfWidth + margin, Math.min(this.selectedElement.x, this.canvasWidth - halfWidth - margin));
      this.selectedElement.y = Math.max(halfHeight + margin, Math.min(this.selectedElement.y, this.canvasHeight - halfHeight - margin));
      console.log('图片放大完成:', {
        width: this.selectedElement.width,
        height: this.selectedElement.height
      });
      this.drawCanvas();
    },
    // 缩小图片
    zoomOutImage: function zoomOutImage() {
      if (!this.selectedElement || this.selectedElement.type !== 'image') return;
      console.log('缩小图片');
      var currentWidth = this.selectedElement.width || 100;
      var currentHeight = this.selectedElement.height || 100;

      // 等比例缩小5%
      var newWidth = currentWidth * 0.95;
      var newHeight = currentHeight * 0.95;

      // 设置最小尺寸限制
      var minSize = 20;
      this.selectedElement.width = Math.max(newWidth, minSize);
      this.selectedElement.height = Math.max(newHeight, minSize);
      console.log('图片缩小完成:', {
        width: this.selectedElement.width,
        height: this.selectedElement.height
      });
      this.drawCanvas();
    },
    // 旋转图片
    rotateImage: function rotateImage() {
      if (!this.selectedElement || this.selectedElement.type !== 'image') return;
      console.log('旋转图片');
      var currentRotation = this.selectedElement.rotation || 0;

      // 每次点击旋转15度
      this.selectedElement.rotation = (currentRotation + 15) % 360;
      console.log('图片旋转完成:', {
        rotation: this.selectedElement.rotation
      });
      this.drawCanvas();
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 287:
/*!************************************************************************************************************************************************************!*\
  !*** D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/edit_new.vue?vue&type=style&index=0&id=4aed2b70&scoped=true&lang=css& ***!
  \************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_new_vue_vue_type_style_index_0_id_4aed2b70_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit_new.vue?vue&type=style&index=0&id=4aed2b70&scoped=true&lang=css& */ 288);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_new_vue_vue_type_style_index_0_id_4aed2b70_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_new_vue_vue_type_style_index_0_id_4aed2b70_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_new_vue_vue_type_style_index_0_id_4aed2b70_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_new_vue_vue_type_style_index_0_id_4aed2b70_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_new_vue_vue_type_style_index_0_id_4aed2b70_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 288:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/edit_new.vue?vue&type=style&index=0&id=4aed2b70&scoped=true&lang=css& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[281,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/subPackage/template/edit_new.js.map