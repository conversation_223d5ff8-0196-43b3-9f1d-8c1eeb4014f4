{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/detail.vue?4bb0", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/detail.vue?6472", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/detail.vue?a04c", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/detail.vue?99c4", "uni-app:///pages/subPackage/meeting/detail.vue", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/detail.vue?71be", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/detail.vue?3a9b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "roomId", "roomInfo", "id", "name", "model", "version", "type", "code", "channel", "status", "isBound", "createTime", "updateTime", "onLoad", "uni", "title", "icon", "setTimeout", "methods", "getRoomDetail", "editRoom", "url", "bindDevice", "duration", "unbindDevice", "content", "success", "deleteRoom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAstB,CAAgB,srBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCsF1uB;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;MACA;MACA;MACA;IACA;MACAC;QACAC;QACAC;MACA;MACAC;QACAH;MACA;IACA;EACA;EACAI;IACA;IACAC;MACA;MACA;MACA;QACA;UACAjB;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;QACA;UACAV;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;MAEA;QACA;QACA;QACA;QACA;UACA;YACA;UACA;QACA;MACA;QACAE;UACAC;UACAC;QACA;QACAC;UACAH;QACA;MACA;IACA;IAEA;IACAM;MACAN;QACAO;MACA;IACA;IAEA;IACAC;MAAA;MACAR;QACAC;QACAC;QACAO;MACA;;MAEA;MACAN;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEAH;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAQ;MAAA;MACAV;QACAC;QACAU;QACAC;UACA;YACAZ;cACAC;cACAC;cACAO;YACA;;YAEA;YACAN;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cAEAH;gBACAC;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAW;MACAb;QACAC;QACAU;QACAC;UACA;YACA;YACAZ;cACAC;cACAC;YACA;;YAEA;YACAC;cACAH;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrQA;AAAA;AAAA;AAAA;AAA61C,CAAgB,6tCAAG,EAAC,C;;;;;;;;;;;ACAj3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/subPackage/meeting/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/subPackage/meeting/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=7046c8c5&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/subPackage/meeting/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=7046c8c5&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<!-- 主内容区域 -->\n\t\t<view class=\"content\">\n\t\t\t<view class=\"detail-card\">\n\t\t\t\t<!-- 会议室基本信息 -->\n\t\t\t\t<view class=\"room-header\">\n\t\t\t\t\t<view class=\"room-icon\">\n\t\t\t\t\t\t<view class=\"status-dot\" :class=\"{active: roomInfo.status === 'active'}\"></view>\n\t\t\t\t\t\t<image class=\"icon-image\" src=\"/static/images/logo_icon.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"room-title\">\n\t\t\t\t\t\t<text class=\"room-name\">{{ roomInfo.name }}</text>\n\t\t\t\t\t\t<text class=\"room-status\">{{ roomInfo.status === 'active' ? '已启用' : '已禁用' }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 会议室详细信息 -->\n\t\t\t\t<view class=\"info-section\">\n\t\t\t\t\t<view class=\"info-title\">\n\t\t\t\t\t\t<text>设备信息</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"info-list\">\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<text class=\"info-label\">型号</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ roomInfo.model || '未绑定' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<text class=\"info-label\">版本</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ roomInfo.version || '未绑定' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<text class=\"info-label\">类型</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ roomInfo.type || '未绑定' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<text class=\"info-label\">编号</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ roomInfo.code || '未绑定' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<text class=\"info-label\">频道</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ roomInfo.channel || '未绑定' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<text class=\"info-label\">创建时间</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ roomInfo.createTime || '未知' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<text class=\"info-label\">最后更新</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ roomInfo.updateTime || '未知' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 设备绑定状态 -->\n\t\t\t\t<view class=\"binding-section\">\n\t\t\t\t\t<view class=\"binding-status\" :class=\"{bound: roomInfo.isBound}\">\n\t\t\t\t\t\t<image class=\"binding-icon\" :src=\"roomInfo.isBound ? '/static/images/gateway_binding.png' : '/static/images/gateway_unbinding.png'\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t<text class=\"binding-text\">{{ roomInfo.isBound ? '已绑定设备' : '未绑定设备' }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 底部操作按钮 -->\n\t\t<view class=\"footer\">\n\t\t\t<view class=\"button-group\">\n\t\t\t\t<view class=\"action-button edit\" @click=\"editRoom\">\n\t\t\t\t\t<text>编辑</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"action-button bind\" @click=\"bindDevice\" v-if=\"!roomInfo.isBound\">\n\t\t\t\t\t<text>绑定设备</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"action-button unbind\" @click=\"unbindDevice\" v-if=\"roomInfo.isBound\">\n\t\t\t\t\t<text>解绑设备</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"action-button delete\" @click=\"deleteRoom\">\n\t\t\t\t\t<text>删除</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\troomId: '',\n\t\t\t\troomInfo: {\n\t\t\t\t\tid: '',\n\t\t\t\t\tname: '',\n\t\t\t\t\tmodel: '',\n\t\t\t\t\tversion: '',\n\t\t\t\t\ttype: '',\n\t\t\t\t\tcode: '',\n\t\t\t\t\tchannel: '',\n\t\t\t\t\tstatus: 'active',\n\t\t\t\t\tisBound: false,\n\t\t\t\t\tcreateTime: '2023-05-20 10:30:00',\n\t\t\t\t\tupdateTime: '2023-05-20 10:30:00'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\tif (options.id) {\n\t\t\t\tthis.roomId = options.id;\n\t\t\t\t// 获取会议室详情\n\t\t\t\tthis.getRoomDetail(options.id);\n\t\t\t} else {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '参数错误',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t}, 1500);\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 获取会议室详情\n\t\t\tgetRoomDetail(id) {\n\t\t\t\t// 这里应该调用API获取会议室详情\n\t\t\t\t// 目前使用模拟数据\n\t\t\t\tconst mockData = {\n\t\t\t\t\t'1': {\n\t\t\t\t\t\tid: '1',\n\t\t\t\t\t\tname: '1号会议室',\n\t\t\t\t\t\tmodel: '未绑定',\n\t\t\t\t\t\tversion: '未绑定',\n\t\t\t\t\t\ttype: '未绑定',\n\t\t\t\t\t\tcode: '未绑定',\n\t\t\t\t\t\tchannel: '未绑定',\n\t\t\t\t\t\tstatus: 'active',\n\t\t\t\t\t\tisBound: false,\n\t\t\t\t\t\tcreateTime: '2023-05-20 10:30:00',\n\t\t\t\t\t\tupdateTime: '2023-05-20 10:30:00'\n\t\t\t\t\t},\n\t\t\t\t\t'2': {\n\t\t\t\t\t\tid: '2',\n\t\t\t\t\t\tname: '121212',\n\t\t\t\t\t\tmodel: '未绑定',\n\t\t\t\t\t\tversion: '未绑定',\n\t\t\t\t\t\ttype: '未绑定',\n\t\t\t\t\t\tcode: '未绑定',\n\t\t\t\t\t\tchannel: '未绑定',\n\t\t\t\t\t\tstatus: 'inactive',\n\t\t\t\t\t\tisBound: false,\n\t\t\t\t\t\tcreateTime: '2023-05-21 14:20:00',\n\t\t\t\t\t\tupdateTime: '2023-05-21 14:20:00'\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tif (mockData[id]) {\n\t\t\t\t\t// this.roomInfo = { ...mockData[id] };\n\t\t\t\t\t// 使用传统方式复制对象\n\t\t\t\t\tconst roomData = mockData[id];\n\t\t\t\t\tfor (let key in roomData) {\n\t\t\t\t\t\tif (roomData.hasOwnProperty(key)) {\n\t\t\t\t\t\t\tthis.roomInfo[key] = roomData[key];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '会议室不存在',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t}, 1500);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 编辑会议室\n\t\t\teditRoom() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/meeting/edit?id=${this.roomId}`\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 绑定设备\n\t\t\tbindDevice() {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '正在绑定设备...',\n\t\t\t\t\ticon: 'loading',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 模拟绑定过程\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.roomInfo.isBound = true;\n\t\t\t\t\tthis.roomInfo.model = 'E-Link 2000';\n\t\t\t\t\tthis.roomInfo.version = 'v1.0.5';\n\t\t\t\t\tthis.roomInfo.type = '电子桌牌';\n\t\t\t\t\tthis.roomInfo.code = 'DP' + Math.floor(Math.random() * 10000);\n\t\t\t\t\tthis.roomInfo.channel = 'CH-' + Math.floor(Math.random() * 100);\n\t\t\t\t\tthis.roomInfo.updateTime = new Date().toLocaleString();\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '设备绑定成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t}, 2000);\n\t\t\t},\n\t\t\t\n\t\t\t// 解绑设备\n\t\t\tunbindDevice() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认解绑',\n\t\t\t\t\tcontent: '确定要解绑当前设备吗？',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '正在解绑设备...',\n\t\t\t\t\t\t\t\ticon: 'loading',\n\t\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 模拟解绑过程\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tthis.roomInfo.isBound = false;\n\t\t\t\t\t\t\t\tthis.roomInfo.model = '未绑定';\n\t\t\t\t\t\t\t\tthis.roomInfo.version = '未绑定';\n\t\t\t\t\t\t\t\tthis.roomInfo.type = '未绑定';\n\t\t\t\t\t\t\t\tthis.roomInfo.code = '未绑定';\n\t\t\t\t\t\t\t\tthis.roomInfo.channel = '未绑定';\n\t\t\t\t\t\t\t\tthis.roomInfo.updateTime = new Date().toLocaleString();\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '设备解绑成功',\n\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 删除会议室\n\t\t\tdeleteRoom() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认删除',\n\t\t\t\t\tcontent: `确定要删除${this.roomInfo.name}吗？`,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t// 这里应该调用API删除会议室\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '删除成功',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 返回列表页\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n.container {\n\tflex: 1;\n\tbackground-color: #f5f5f5;\n\tbackground-image: url('/static/images/bg.svg');\n\tbackground-size: cover;\n\tbackground-position: center;\n}\n\n.content {\n\tpadding: 30rpx;\n\tflex: 1;\n\tpadding-bottom: 150rpx; /* 为底部按钮留出空间 */\n}\n\n/* 详情卡片样式 */\n.detail-card {\n\tbackground-color: #ffffff;\n\tborder-radius: 16rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n}\n\n/* 会议室头部信息 */\n.room-header {\n\tpadding: 30rpx;\n\tdisplay: flex;\n\tflex-direction: row;\n\talign-items: center;\n\tborder-bottom: 1px solid #f0f0f0;\n}\n\n.room-icon {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tposition: relative;\n\tmargin-right: 30rpx;\n}\n\n.status-dot {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 20rpx;\n\theight: 20rpx;\n\tborder-radius: 10rpx;\n\tbackground-color: #cccccc;\n}\n\n.status-dot.active {\n\tbackground-color: #8B0000;\n}\n\n.icon-image {\n\twidth: 100%;\n\theight: 100%;\n\tborder-radius: 8rpx;\n\tbackground-color: #8B0000;\n}\n\n.room-title {\n\tflex: 1;\n}\n\n.room-name {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tdisplay: block;\n\tmargin-bottom: 10rpx;\n}\n\n.room-status {\n\tfont-size: 24rpx;\n\tcolor: #8B0000;\n\tbackground-color: rgba(139, 0, 0, 0.1);\n\tpadding: 4rpx 16rpx;\n\tborder-radius: 20rpx;\n}\n\n/* 信息部分样式 */\n.info-section {\n\tpadding: 30rpx;\n\tborder-bottom: 1px solid #f0f0f0;\n}\n\n.info-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 20rpx;\n}\n\n.info-list {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.info-item {\n\tdisplay: flex;\n\tflex-direction: row;\n\tmargin-bottom: 15rpx;\n\talign-items: center;\n}\n\n.info-label {\n\twidth: 150rpx;\n\tfont-size: 28rpx;\n\tcolor: #999;\n}\n\n.info-value {\n\tflex: 1;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n/* 绑定状态部分 */\n.binding-section {\n\tpadding: 30rpx;\n}\n\n.binding-status {\n\tdisplay: flex;\n\tflex-direction: row;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 20rpx;\n\tbackground-color: #f9f9f9;\n\tborder-radius: 8rpx;\n}\n\n.binding-status.bound {\n\tbackground-color: rgba(139, 0, 0, 0.1);\n}\n\n.binding-icon {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tmargin-right: 20rpx;\n}\n\n.binding-text {\n\tfont-size: 28rpx;\n\tcolor: #666;\n}\n\n.binding-status.bound .binding-text {\n\tcolor: #8B0000;\n}\n\n/* 底部按钮样式 */\n.footer {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground-color: transparent;\n\tpadding: 20rpx 30rpx;\n\tdisplay: flex;\n\tjustify-content: center;\n}\n\n.button-group {\n\tdisplay: flex;\n\tflex-direction: row;\n\twidth: 100%;\n\tjustify-content: space-between;\n}\n\n.action-button {\n\theight: 90rpx;\n\tborder-radius: 45rpx;\n\tjustify-content: center;\n\talign-items: center;\n\tdisplay: flex;\n\tpadding: 0 40rpx;\n}\n\n.action-button text {\n\tcolor: #ffffff;\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n}\n\n.action-button.edit {\n\tbackground-color: #8B0000; /* 深红色 */\n\tbox-shadow: 0 4rpx 10rpx rgba(139, 0, 0, 0.3);\n}\n\n.action-button.bind {\n\tbackground-color: #8B0000; /* 深红色 */\n\tbox-shadow: 0 4rpx 10rpx rgba(139, 0, 0, 0.3);\n}\n\n.action-button.unbind {\n\tbackground-color: #666;\n\tbox-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);\n}\n\n.action-button.delete {\n\tbackground-color: #ff6b6b;\n\tbox-shadow: 0 4rpx 10rpx rgba(255, 107, 107, 0.3);\n}\n</style>", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751873716794\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}