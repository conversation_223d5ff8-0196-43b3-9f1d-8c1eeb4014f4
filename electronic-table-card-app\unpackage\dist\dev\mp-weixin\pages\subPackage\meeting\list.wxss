@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  /* Add padding to the bottom to prevent content from being hidden by the fixed button */
  padding-bottom: 160rpx;
  /* Increased padding to accommodate the button and safe area */
}
.content {
  padding: 20rpx;
  flex: 1;
}
/* 会议室列表样式 */
.meeting-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
}
.meeting-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.header-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}
.meeting-name-header {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
}
.meeting-body {
  display: flex;
  flex-direction: row;
  padding: 30rpx;
  align-items: center;
}
.meeting-icon-container {
  position: relative;
  margin-right: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.status-dot-outer {
  width: 30rpx;
  height: 30rpx;
  border-radius: 15rpx;
  background-color: #e0e0e0;
  position: absolute;
  top: -5rpx;
  left: -15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.status-dot-outer.active-outer {
  background-color: #8B0000;
}
.status-dot-inner {
  width: 16rpx;
  height: 16rpx;
  border-radius: 8rpx;
  background-color: #8B0000;
}
.status-dot-inner.active {
  background-color: #8B0000;
}
.status-dot-inner:not(.active) {
  background-color: #FFFFFF;
}
.main-icon-bg {
  width: 100rpx;
  height: 100rpx;
  border-radius: 12rpx;
  background-color: #8B0000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.main-icon {
  width: 60rpx;
  height: 60rpx;
}
.meeting-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.spec-row {
  display: flex;
  flex-direction: row;
  margin-bottom: 8rpx;
  align-items: center;
}
.spec-label {
  font-size: 26rpx;
  color: #555555;
  width: 90rpx;
}
.spec-value {
  font-size: 26rpx;
  color: #333333;
  flex: 1;
}
/* 操作按钮样式 */
.meeting-actions {
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
}
.action-button {
  padding: 0 25rpx;
  border-radius: 8rpx;
  margin-left: 15rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}
.action-button text {
  font-weight: 500;
}
.action-button.unbind {
  background-color: #FFFFFF;
  border: 1rpx solid #cccccc;
  display: flex;
  align-items: center;
}
.action-button.unbind text {
  color: #555555;
}
.action-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}
.action-button.bind {
  background-color: #8B0000;
  color: #FFFFFF;
}
.action-button.bind text {
  color: #FFFFFF;
}
.action-button.delete {
  background-color: #FFFFFF;
  border: 1rpx solid #8B0000;
}
.action-button.delete text {
  color: #8B0000;
}
.action-button.edit {
  background-color: #8B0000;
  color: #FFFFFF;
}
.action-button.edit text {
  color: #FFFFFF;
}
/* 添加按钮样式 */
.add-button {
  position: fixed;
  right: 40rpx;
  bottom: 200rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  background-color: #8B0000;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6rpx 15rpx rgba(139, 0, 0, 0.35);
  z-index: 1000;
}
.add-icon {
  font-size: 50rpx;
  color: #ffffff;
  font-weight: bold;
}
/* New styles for the fixed bottom button */
.confirm-button-container {
  position: fixed;
  bottom: 0;
  /* Stick to the very bottom */
  left: 0;
  right: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0 env(safe-area-inset-bottom);
  /* Add padding and account for safe area */
  z-index: 1000;
  /* Ensure it's above other content */
}
.confirm-button-wrapper {
  position: relative;
  width: 80%;
  /* Adjust as needed */
  max-width: 600rpx;
}
.confirm-button-bg {
  width: 100%;
  height: 88rpx;
  /* Adjust based on your image aspect ratio or desired height */
  display: block;
  border-radius: 44rpx;
  /* Optional: if you want rounded corners for the image itself */
}
.confirm-button {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}
.add-button {
  position: fixed;
  right: 40rpx;
  /* Adjust bottom to be above the new confirm button height + padding + safe area */
  bottom: calc(128rpx + env(safe-area-inset-bottom) + 30rpx);
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  background-color: #8B0000;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6rpx 15rpx rgba(139, 0, 0, 0.35);
  z-index: 1001;
  /* Ensure it's above the confirm button container */
}
.add-icon {
  font-size: 50rpx;
  line-height: 50rpx;
}
/* Ensure the main content area has enough padding at the bottom 
   so it doesn't get obscured by the fixed button container */
.meeting-list {
  /* padding-bottom: 120rpx; */
  /* Moved to .container for overall page structure */
}
/* 绑定弹窗样式 */
.bind-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}
.bind-modal {
  width: 100%;
  max-width: 750rpx;
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  -webkit-transform: translateY(100%);
          transform: translateY(100%);
  transition: -webkit-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
  transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
  max-height: 80vh;
  overflow-y: auto;
}
.bind-modal.show {
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
.bind-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.bind-modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}
.bind-modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 30rpx;
  background-color: #f5f5f5;
}
.bind-modal-close text {
  font-size: 40rpx;
  color: #666666;
  line-height: 1;
}
.bind-modal-content {
  display: flex;
  flex-direction: column;
}
.bind-info {
  text-align: center;
  margin-bottom: 30rpx;
}
.bind-info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 15rpx;
  display: block;
}
.bind-info-desc {
  font-size: 28rpx;
  color: #666666;
  display: block;
}
/* 网关列表样式 */
.gateway-list {
  margin-bottom: 40rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  overflow: hidden;
  max-height: 400rpx;
  overflow-y: auto;
}
.gateway-header {
  display: flex;
  background-color: #f8f9fa;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e0e0e0;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 10;
}
.gateway-item {
  display: flex;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
}
.gateway-item:last-child {
  border-bottom: none;
}
.gateway-item:active {
  background-color: #f8f9fa;
}
.header-cell {
  font-size: 28rpx;
  font-weight: bold;
  color: #666666;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header-cell:first-child {
  flex: 0 0 120rpx;
}
.header-cell:nth-child(2) {
  flex: 1;
}
.header-cell:nth-child(3) {
  flex: 1;
}
.gateway-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #333333;
}
.radio-cell {
  flex: 0 0 120rpx;
}
.name-cell {
  flex: 1;
  text-align: center;
}
.mac-cell {
  flex: 1;
  text-align: center;
  font-family: monospace;
}
.bind-actions {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}
.bind-action-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: bold;
  transition: all 0.2s ease;
}
.bind-action-btn.cancel {
  background-color: #f5f5f5;
  color: #666666;
  border: 1rpx solid #e0e0e0;
}
.bind-action-btn.cancel:active {
  background-color: #e8e8e8;
}
.bind-action-btn.confirm {
  background-color: #8B0000;
  color: #ffffff;
}
.bind-action-btn.confirm:active {
  background-color: #6B0000;
}
.bind-action-btn text {
  font-size: 30rpx;
  font-weight: bold;
}
