.vol-tree-container.data-v-0c772d04 {
  position: fixed;
  /* 	top: 0rpx; */
  right: 0rpx;
  bottom: 0rpx;
  left: 0rpx;
  z-index: 9999;
  top: 360rpx;
  /* min-height: 500rpx; */
  transition: all 0.3s ease;
  -webkit-transform: translateY(100%);
          transform: translateY(100%);
  background: #ffff;
}
.vol-tree-container .vol-tree-item.data-v-0c772d04 {
  font-size: 28rpx;
  color: #585858;
  height: 0;
  opacity: 0;
  position: relative;
  overflow: hidden;
  padding-left: 20rpx !important;
}
.vol-tree-container .vol-tree-item.checked.data-v-0c772d04 {
  background: #f4f9ff;
}
.vol-tree-container .vol-tree-item.show.data-v-0c772d04 {
  padding: 16rpx;
  height: 40rpx;
  opacity: 1;
}
.vol-tree-container .vol-tree-item-child.data-v-0c772d04 {
  display: flex;
}
.vol-tree-container .vol-tree-item-child .vol-tree-item-child-label.data-v-0c772d04 {
  flex: 1;
}
.vol-tree-container .tree-left-icon.data-v-0c772d04 {
  width: 26rpx;
  height: 26rpx;
  margin-right: 8rpx;
}
.vol-tree-container .vol-tree-item-child-check.data-v-0c772d04 {
  padding-left: 0 20rpx;
}
.vol-tree-container.show.data-v-0c772d04 {
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
.vol-tree-mask.data-v-0c772d04 {
  position: fixed;
  top: 0rpx;
  right: 0rpx;
  bottom: 0rpx;
  left: 0rpx;
  z-index: 9998;
  background-color: rgba(0, 0, 0, 0.6);
  opacity: 0;
  transition: all 0.3s ease;
  visibility: hidden;
}
.vol-tree-mask.show.data-v-0c772d04 {
  visibility: visible;
  opacity: 1;
}
.vol-tree-header.data-v-0c772d04 {
  text-align: center;
  position: relative;
  border-bottom: 1px solid rgb(233 233 233);
}
.vol-tree-header .vol-tree-header-title.data-v-0c772d04 {
  padding: 24rpx;
}
.vol-tree-header .vol-tree-header-confirm.data-v-0c772d04 {
  position: absolute;
  right: 30rpx;
  height: 100%;
  top: 8rpx;
  color: #3495ff;
  padding-top: 20rpx;
  bottom: 0;
  font-size: 28rpx;
}

