{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/edit.vue?9a4e", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/edit.vue?6108", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/edit.vue?54e3", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/edit.vue?89c9", "uni-app:///pages/subPackage/template/edit.vue", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/edit.vue?9940", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/edit.vue?15d5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "templateName", "canvasContext", "canvasWidth", "canvasHeight", "screenInfo", "canvasElements", "selectedElement", "insertType", "backgroundType", "dragging", "lastTouchX", "lastTouchY", "currentFontSize", "currentTextColor", "showEditTextPopup", "editingText", "computed", "canvasContainerStyle", "height", "backgroundColor", "display", "alignItems", "justifyContent", "toolbarContainerStyle", "overflowY", "canvasStyle", "displayHeight", "displayWidth", "width", "border", "borderRadius", "insertTools", "type", "icon", "label", "backgroundTools", "colorOptions", "hasElements", "canSave", "onLoad", "onReady", "methods", "getSystemInfo", "uni", "success", "initCanvas", "query", "fields", "node", "size", "exec", "resolve", "canvas", "console", "title", "draw<PERSON><PERSON>vas", "ctx", "drawBackground", "drawElement", "drawE<PERSON><PERSON><PERSON><PERSON>", "getElementBounds", "x", "y", "handleTouchStart", "handleTouchMove", "handleTouchEnd", "handleCanvasTap", "getTouchPosition", "getElementAtPosition", "isPointInElement", "selectElement", "deselectElement", "moveElement", "element", "addElementAtPosition", "id", "text", "color", "fontSize", "duration", "setInsertType", "setBackgroundType", "adjustFontSize", "updateFontSize", "selectColor", "editElementText", "confirmEditText", "closeEditTextPopup", "duplicateElement", "deleteElement", "centerAllElements", "clearAllElements", "content", "openPreview", "generatePreviewImage", "canvasId", "destWidth", "destHeight", "fileType", "quality", "urls", "current", "fail", "saveTemplate", "name", "elements", "createTime", "savedTemplates", "setTimeout", "goBack"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACqC;;;AAGxF;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAotB,CAAgB,orBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2LxuB;EACAC;IACA;MACA;MACAC;MAEA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MAEA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;MACA;MAEA;QACAL;QACAM;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;MACA;QACAC;QACAC;MACA;MAEA;QACAC;QACAV;QACAW;QACAC;QACAX;MACA;IACA;IAEA;IACAY;MACA,QACA;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,EACA;IACA;IAEA;IACAC;MACA,QACA;QAAAH;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,EACA;IACA;IAEA;IACAE;MACA,QACA,uDACA,sDACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MACAC;QACAC;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;gBAAA;gBAAA,OACA;kBACAA,gCACAC;oBAAAC;oBAAAC;kBAAA,GACAC;oBACA;sBACAC;oBACA;kBACA;gBACA;cAAA;gBARAC;gBAUA;kBACA;;kBAEA;kBACAA;kBACAA;;kBAEA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACAV;kBACAW;kBACArB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAsB;MAAA;MACA;MAEA;;MAEA;MACAC;;MAEA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;UACAD;UACAA;UACA;QACA;UACA;UACA;QACA;UACAA;UACAA;MAAA;IAEA;IAEA;IACAE;MACA;MAEA;;MAEA;MACAF;MACAA;MACAA;MACAA;;MAEA;MACAA;IACA;IAEA;IACAG;MACA;MAEA;MACA;;MAEA;MACAH;MACAA;MACAA;MACAA;MACAA;IACA;IAEA;IACAI;MACA;QACA;QACA;QACA;QACA;QAEA;UACAC;UACAC;UACAlC;UACAV;QACA;MACA;MACA;QAAA2C;QAAAC;QAAAlC;QAAAV;MAAA;IACA;IAEA;IACA6C;MACA;MACA;MAEA;MACA;;MAEA;MACA;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MAEA;MACA;MAEA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;MAEA;MACA;MAEA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;MACA;MAEA;QACAN;QACAC;MACA;IACA;IAEA;IACAM;MACA;MACA;QACA;QACA;UACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA,wDACAP;MACA;MACA;IACA;IAEA;IACAQ;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACAC;MACAA;;MAEA;MACAA;MACAA;IACA;IAEA;IACAC;MACA;QACA;UACAC;UACA3C;UACA4C;UACAf;UACAC;UACAe;UACAC;QACA;QAEA;QACA;QACA;;QAEAnC;UACAW;UACArB;UACA8C;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA,iDACA;UACAb;UACAd;UACAC;QAAA,EACA;QAEA;QACA;QAEAnB;UACAW;UACArB;QACA;MACA;IACA;IAEA;IACAwD;MAAA;MACA;QACA;UAAA;QAAA;QACA;UACA;UACA;UACA;UAEA9C;YACAW;YACArB;UACA;QACA;MACA;IACA;IAEA;IACAyD;MAAA;MACA;QACAjB;QACAA;MACA;MACA;MAEA9B;QACAW;QACArB;MACA;IACA;IAEA;IACA0D;MAAA;MACAhD;QACAW;QACAsC;QACAhD;UACA;YACA;YACA;YACA;YAEAD;cACAW;cACArB;YACA;UACA;QACA;MACA;IACA;IAEA;IACA4D;MACA;QACAlD;UACAW;UACArB;QACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACA6D;MACA;MAEA;QACA;QACAnD;UACAoD;UACAnE;UACAV;UACA8E;UACAC;UACAC;UACAC;UACAvD;YACAD;cACAyD;cACAC;YACA;UACA;UACAC;YACAjD;YACAV;cACAW;cACArB;YACA;UACA;QACA;MACA;QACAoB;MACA;IACA;IAEA;IACAkD;MAAA;MACA;QACA5D;UACAW;UACArB;QACA;QACA;MACA;;MAEA;MACA;QACAuE;QACAC;QACAjG;QACAN;QACAC;QACAuG;MACA;;MAEA;MACA;QACA;QACAC;QACAhE;QAEAA;UACAW;UACArB;QACA;;QAEA;QACA2E;UACA;QACA;MACA;QACAvD;QACAV;UACAW;UACArB;QACA;MACA;IACA;IAEA;IACA4E;MACAlE;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5zBA;AAAA;AAAA;AAAA;AAA6iC,CAAgB,69BAAG,EAAC,C;;;;;;;;;;;ACAjkC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/subPackage/template/edit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/subPackage/template/edit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./edit.vue?vue&type=template&id=6c6302e2&scoped=true&\"\nvar renderjs\nimport script from \"./edit.vue?vue&type=script&lang=js&\"\nexport * from \"./edit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./edit.vue?vue&type=style&index=0&id=6c6302e2&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6c6302e2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/subPackage/template/edit.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=template&id=6c6302e2&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"template-editor\">\n\t\t<!-- 顶部工具栏 -->\n\t\t<view class=\"toolbar\">\n\t\t\t<view class=\"toolbar-left\">\n\t\t\t\t<view class=\"back-btn\" @click=\"goBack\">\n\t\t\t\t\t<text class=\"icon\">←</text>\n\t\t\t\t</view>\n\t\t\t\t<input\n\t\t\t\t\tclass=\"template-name-input\"\n\t\t\t\t\tv-model=\"templateName\"\n\t\t\t\t\tplaceholder=\"输入模板名称\"\n\t\t\t\t\tmaxlength=\"20\"\n\t\t\t\t/>\n\t\t\t</view>\n\t\t\t<view class=\"toolbar-right\">\n\t\t\t\t<view class=\"tool-btn\" @click=\"openPreview\" :disabled=\"!hasElements\">\n\t\t\t\t\t<text class=\"icon\">👁</text>\n\t\t\t\t\t<text class=\"text\">预览</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"tool-btn primary\" @click=\"saveTemplate\" :disabled=\"!canSave\">\n\t\t\t\t\t<text class=\"icon\">💾</text>\n\t\t\t\t\t<text class=\"text\">保存</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- Canvas画布区域 (占屏幕高度1/3) -->\n\t\t<view class=\"canvas-container\" :style=\"canvasContainerStyle\">\n\t\t\t<view class=\"canvas-wrapper\">\n\t\t\t\t<canvas\n\t\t\t\t\ttype=\"2d\"\n\t\t\t\t\tclass=\"skyline-canvas\"\n\t\t\t\t\tcanvas-id=\"skylineCanvas\"\n\t\t\t\t\t:style=\"canvasStyle\"\n\t\t\t\t\t@touchstart=\"handleTouchStart\"\n\t\t\t\t\t@touchmove=\"handleTouchMove\"\n\t\t\t\t\t@touchend=\"handleTouchEnd\"\n\t\t\t\t\t@tap=\"handleCanvasTap\"\n\t\t\t\t></canvas>\n\n\t\t\t\t<!-- 画布工具提示 -->\n\t\t\t\t<view class=\"canvas-hint\" v-if=\"!hasElements && !insertType\">\n\t\t\t\t\t<text class=\"hint-text\">选择下方工具开始创建模板</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 工具栏区域 (占屏幕高度2/3) -->\n\t\t<view class=\"toolbar-container\" :style=\"toolbarContainerStyle\">\n\t\t\t<!-- 插入工具 -->\n\t\t\t<view class=\"tool-section\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text class=\"title\">插入元素</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"tool-buttons\">\n\t\t\t\t\t<view\n\t\t\t\t\t\tv-for=\"tool in insertTools\"\n\t\t\t\t\t\t:key=\"tool.type\"\n\t\t\t\t\t\tclass=\"tool-button\"\n\t\t\t\t\t\t:class=\"{ active: insertType === tool.type }\"\n\t\t\t\t\t\t@click=\"setInsertType(tool.type)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<text class=\"tool-icon\">{{ tool.icon }}</text>\n\t\t\t\t\t\t<text class=\"tool-label\">{{ tool.label }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 背景工具 -->\n\t\t\t<view class=\"tool-section\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text class=\"title\">背景设置</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"tool-buttons\">\n\t\t\t\t\t<view\n\t\t\t\t\t\tv-for=\"bg in backgroundTools\"\n\t\t\t\t\t\t:key=\"bg.type\"\n\t\t\t\t\t\tclass=\"tool-button small\"\n\t\t\t\t\t\t:class=\"{ active: backgroundType === bg.type }\"\n\t\t\t\t\t\t@click=\"setBackgroundType(bg.type)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<text class=\"tool-icon\">{{ bg.icon }}</text>\n\t\t\t\t\t\t<text class=\"tool-label\">{{ bg.label }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 属性设置 -->\n\t\t\t<view class=\"tool-section\" v-if=\"selectedElement\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text class=\"title\">属性设置</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"property-controls\">\n\t\t\t\t\t<!-- 字体大小 -->\n\t\t\t\t\t<view class=\"property-item\">\n\t\t\t\t\t\t<text class=\"label\">字号</text>\n\t\t\t\t\t\t<view class=\"number-control\">\n\t\t\t\t\t\t\t<view class=\"control-btn\" @click=\"adjustFontSize(-2)\">-</view>\n\t\t\t\t\t\t\t<input class=\"number-input\" v-model.number=\"currentFontSize\" type=\"number\" @input=\"updateFontSize\" />\n\t\t\t\t\t\t\t<view class=\"control-btn\" @click=\"adjustFontSize(2)\">+</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 颜色选择 -->\n\t\t\t\t\t<view class=\"property-item\">\n\t\t\t\t\t\t<text class=\"label\">颜色</text>\n\t\t\t\t\t\t<view class=\"color-options\">\n\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\tv-for=\"color in colorOptions\"\n\t\t\t\t\t\t\t\t:key=\"color\"\n\t\t\t\t\t\t\t\tclass=\"color-item\"\n\t\t\t\t\t\t\t\t:class=\"{ active: currentTextColor === color }\"\n\t\t\t\t\t\t\t\t:style=\"{ backgroundColor: color }\"\n\t\t\t\t\t\t\t\t@click=\"selectColor(color)\"\n\t\t\t\t\t\t\t></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 操作按钮 -->\n\t\t\t\t\t<view class=\"element-actions\">\n\t\t\t\t\t\t<view class=\"action-btn\" @click=\"editElementText\" v-if=\"selectedElement.type === 'text'\">\n\t\t\t\t\t\t\t<text class=\"icon\">✏️</text>\n\t\t\t\t\t\t\t<text class=\"text\">编辑</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"action-btn\" @click=\"duplicateElement\">\n\t\t\t\t\t\t\t<text class=\"icon\">📋</text>\n\t\t\t\t\t\t\t<text class=\"text\">复制</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"action-btn danger\" @click=\"deleteElement\">\n\t\t\t\t\t\t\t<text class=\"icon\">🗑️</text>\n\t\t\t\t\t\t\t<text class=\"text\">删除</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 快捷操作 -->\n\t\t\t<view class=\"tool-section\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text class=\"title\">快捷操作</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"quick-actions\">\n\t\t\t\t\t<view class=\"action-btn\" @click=\"centerAllElements\" :disabled=\"!hasElements\">\n\t\t\t\t\t\t<text class=\"icon\">⚡</text>\n\t\t\t\t\t\t<text class=\"text\">居中对齐</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-btn danger\" @click=\"clearAllElements\" :disabled=\"!hasElements\">\n\t\t\t\t\t\t<text class=\"icon\">🗑</text>\n\t\t\t\t\t\t<text class=\"text\">清空画布</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 文字编辑弹窗 -->\n\t\t<u-popup\n\t\t\t:show=\"showEditTextPopup\"\n\t\t\tmode=\"center\"\n\t\t\t@close=\"closeEditTextPopup\"\n\t\t\t:round=\"16\"\n\t\t\tcloseable\n\t\t>\n\t\t\t<view class=\"popup-content\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<text class=\"popup-title\">编辑文字内容</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup-body\">\n\t\t\t\t\t<textarea\n\t\t\t\t\t\tv-model=\"editingText\"\n\t\t\t\t\t\tclass=\"text-input\"\n\t\t\t\t\t\tplaceholder=\"请输入文字内容\"\n\t\t\t\t\t\tmaxlength=\"100\"\n\t\t\t\t\t\tauto-height\n\t\t\t\t\t></textarea>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup-footer\">\n\t\t\t\t\t<view class=\"popup-btn secondary\" @click=\"closeEditTextPopup\">取消</view>\n\t\t\t\t\t<view class=\"popup-btn primary\" @click=\"confirmEditText\">确认</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</u-popup>\n\t</view>\n</template>\n\n<script> \n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\t// 模板基本信息\n\t\t\ttemplateName: '新增模板',\n\t\t\t\n\t\t\t// 画布相关\n\t\t\tcanvasContext: null,\n\t\t\tcanvasWidth: 800,  // 桌面牌宽度\n\t\t\tcanvasHeight: 480, // 桌面牌高度\n\t\t\tscreenInfo: null,\n\t\t\t\n\t\t\t// 元素管理\n\t\t\tcanvasElements: [],\n\t\t\tselectedElement: null,\n\t\t\tinsertType: null,\n\t\t\tbackgroundType: 'solid',\n\t\t\t\n\t\t\t// 触摸交互\n\t\t\tdragging: false,\n\t\t\tlastTouchX: 0,\n\t\t\tlastTouchY: 0,\n\t\t\t\n\t\t\t// 文字属性\n\t\t\tcurrentFontSize: 24,\n\t\t\tcurrentTextColor: '#000000',\n\t\t\t\n\t\t\t// 弹窗状态\n\t\t\tshowEditTextPopup: false,\n\t\t\teditingText: ''\n\t\t}\n\t},\n\t\n\tcomputed: {\n\t\t// 画布容器样式 (占屏幕高度1/3)\n\t\tcanvasContainerStyle() {\n\t\t\tconst screenHeight = this.screenInfo?.windowHeight || 800\n\t\t\tconst toolbarHeight = 60 // 顶部工具栏高度\n\t\t\tconst availableHeight = screenHeight - toolbarHeight\n\t\t\tconst canvasContainerHeight = Math.floor(availableHeight / 3)\n\t\t\t\n\t\t\treturn {\n\t\t\t\theight: `${canvasContainerHeight}px`,\n\t\t\t\tbackgroundColor: '#f5f5f5',\n\t\t\t\tdisplay: 'flex',\n\t\t\t\talignItems: 'center',\n\t\t\t\tjustifyContent: 'center'\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 工具栏容器样式 (占屏幕高度2/3)\n\t\ttoolbarContainerStyle() {\n\t\t\tconst screenHeight = this.screenInfo?.windowHeight || 800\n\t\t\tconst toolbarHeight = 60 // 顶部工具栏高度\n\t\t\tconst availableHeight = screenHeight - toolbarHeight\n\t\t\tconst toolbarContainerHeight = Math.floor(availableHeight * 2 / 3)\n\t\t\t\n\t\t\treturn {\n\t\t\t\theight: `${toolbarContainerHeight}px`,\n\t\t\t\toverflowY: 'auto'\n\t\t\t}\n\t\t},\n\t\t\n\t\t// Canvas样式 (保持800:480比例)\n\t\tcanvasStyle() {\n\t\t\tconst screenWidth = this.screenInfo?.windowWidth || 375\n\t\t\tconst maxWidth = screenWidth * 0.9 // 留出边距\n\t\t\t\n\t\t\t// 计算适合的尺寸，保持800:480比例\n\t\t\tconst aspectRatio = 800 / 480\n\t\t\tlet displayWidth = maxWidth\n\t\t\tlet displayHeight = displayWidth / aspectRatio\n\t\t\t\n\t\t\t// 确保高度不超过容器\n\t\t\tconst maxHeight = (this.screenInfo?.windowHeight || 800) / 3 - 40\n\t\t\tif (displayHeight > maxHeight) {\n\t\t\t\tdisplayHeight = maxHeight\n\t\t\t\tdisplayWidth = displayHeight * aspectRatio\n\t\t\t}\n\t\t\t\n\t\t\treturn {\n\t\t\t\twidth: `${displayWidth}px`,\n\t\t\t\theight: `${displayHeight}px`,\n\t\t\t\tborder: '2px solid #ddd',\n\t\t\t\tborderRadius: '8px',\n\t\t\t\tbackgroundColor: '#fff'\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 插入工具\n\t\tinsertTools() {\n\t\t\treturn [\n\t\t\t\t{ type: 'text', icon: '📝', label: '文字' },\n\t\t\t\t{ type: 'image', icon: '🖼️', label: '图片' }\n\t\t\t]\n\t\t},\n\t\t\n\t\t// 背景工具\n\t\tbackgroundTools() {\n\t\t\treturn [\n\t\t\t\t{ type: 'solid', icon: '🎨', label: '纯色' },\n\t\t\t\t{ type: 'clear', icon: '🗑️', label: '透明' }\n\t\t\t]\n\t\t},\n\t\t\n\t\t// 颜色选项\n\t\tcolorOptions() {\n\t\t\treturn [\n\t\t\t\t'#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF',\n\t\t\t\t'#FFFF00', '#FF00FF', '#00FFFF', '#FFA500', '#800080'\n\t\t\t]\n\t\t},\n\t\t\n\t\t// 是否有元素\n\t\thasElements() {\n\t\t\treturn this.canvasElements.length > 0\n\t\t},\n\t\t\n\t\t// 是否可以保存\n\t\tcanSave() {\n\t\t\treturn this.templateName.trim() && this.hasElements\n\t\t}\n\t},\n\t\n\tonLoad() {\n\t\tthis.initCanvas()\n\t},\n\t\n\tonReady() {\n\t\tthis.getSystemInfo()\n\t},\n\t\n\tmethods: {\n\t\t// 获取系统信息\n\t\tgetSystemInfo() {\n\t\t\tuni.getSystemInfo({\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tthis.screenInfo = res\n\t\t\t\t\tthis.$forceUpdate()\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\t\t\n\t\t// 初始化Canvas\n\t\tasync initCanvas() {\n\t\t\ttry {\n\t\t\t\t// 使用skyline渲染的2d context\n\t\t\t\tconst query = uni.createSelectorQuery().in(this)\n\t\t\t\tconst canvas = await new Promise((resolve) => {\n\t\t\t\t\tquery.select('.skyline-canvas')\n\t\t\t\t\t\t.fields({ node: true, size: true })\n\t\t\t\t\t\t.exec((res) => {\n\t\t\t\t\t\t\tif (res[0]) {\n\t\t\t\t\t\t\t\tresolve(res[0].node)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t\t\n\t\t\t\tif (canvas) {\n\t\t\t\t\tthis.canvasContext = canvas.getContext('2d')\n\t\t\t\t\t\n\t\t\t\t\t// 设置Canvas实际尺寸为桌面牌分辨率\n\t\t\t\t\tcanvas.width = this.canvasWidth\n\t\t\t\t\tcanvas.height = this.canvasHeight\n\t\t\t\t\t\n\t\t\t\t\t// 初始绘制\n\t\t\t\t\tthis.drawCanvas()\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('Canvas初始化失败:', error)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: 'Canvas初始化失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 绘制Canvas\n\t\tdrawCanvas() {\n\t\t\tif (!this.canvasContext) return\n\t\t\t\n\t\t\tconst ctx = this.canvasContext\n\t\t\t\n\t\t\t// 清空画布\n\t\t\tctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)\n\t\t\t\n\t\t\t// 绘制背景\n\t\t\tthis.drawBackground()\n\t\t\t\n\t\t\t// 绘制所有元素\n\t\t\tthis.canvasElements.forEach(element => {\n\t\t\t\tthis.drawElement(element)\n\t\t\t})\n\t\t\t\n\t\t\t// 绘制选中元素的高亮\n\t\t\tif (this.selectedElement) {\n\t\t\t\tthis.drawElementHighlight(this.selectedElement)\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 绘制背景\n\t\tdrawBackground() {\n\t\t\tconst ctx = this.canvasContext\n\t\t\t\n\t\t\tswitch (this.backgroundType) {\n\t\t\t\tcase 'solid':\n\t\t\t\t\tctx.fillStyle = '#FFFFFF'\n\t\t\t\t\tctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)\n\t\t\t\t\tbreak\n\t\t\t\tcase 'clear':\n\t\t\t\t\t// 透明背景，不绘制\n\t\t\t\t\tbreak\n\t\t\t\tdefault:\n\t\t\t\t\tctx.fillStyle = '#FFFFFF'\n\t\t\t\t\tctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 绘制元素\n\t\tdrawElement(element) {\n\t\t\tif (element.type !== 'text') return\n\t\t\t\n\t\t\tconst ctx = this.canvasContext\n\t\t\t\n\t\t\t// 设置文字样式\n\t\t\tctx.fillStyle = element.color || '#000000'\n\t\t\tctx.font = `${element.fontSize || 24}px Arial`\n\t\t\tctx.textAlign = 'center'\n\t\t\tctx.textBaseline = 'middle'\n\t\t\t\n\t\t\t// 绘制文字\n\t\t\tctx.fillText(element.text || '', element.x, element.y)\n\t\t},\n\t\t\n\t\t// 绘制元素高亮\n\t\tdrawElementHighlight(element) {\n\t\t\tif (element.type !== 'text') return\n\t\t\t\n\t\t\tconst ctx = this.canvasContext\n\t\t\tconst bounds = this.getElementBounds(element)\n\t\t\t\n\t\t\t// 绘制选中边框\n\t\t\tctx.strokeStyle = '#007AFF'\n\t\t\tctx.lineWidth = 2\n\t\t\tctx.setLineDash([5, 5])\n\t\t\tctx.strokeRect(bounds.x, bounds.y, bounds.width, bounds.height)\n\t\t\tctx.setLineDash([])\n\t\t},\n\t\t\n\t\t// 获取元素边界\n\t\tgetElementBounds(element) {\n\t\t\tif (element.type === 'text') {\n\t\t\t\tconst fontSize = element.fontSize || 24\n\t\t\t\tconst text = element.text || ''\n\t\t\t\tconst textWidth = text.length * fontSize * 0.6 // 估算文字宽度\n\t\t\t\tconst textHeight = fontSize\n\t\t\t\t\n\t\t\t\treturn {\n\t\t\t\t\tx: element.x - textWidth / 2 - 5,\n\t\t\t\t\ty: element.y - textHeight / 2 - 5,\n\t\t\t\t\twidth: textWidth + 10,\n\t\t\t\t\theight: textHeight + 10\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn { x: 0, y: 0, width: 0, height: 0 }\n\t\t},\n\t\t\n\t\t// 触摸事件处理\n\t\thandleTouchStart(e) {\n\t\t\tconst touch = this.getTouchPosition(e)\n\t\t\tif (!touch) return\n\t\t\t\n\t\t\tthis.lastTouchX = touch.x\n\t\t\tthis.lastTouchY = touch.y\n\t\t\t\n\t\t\t// 检查是否点击到元素\n\t\t\tconst element = this.getElementAtPosition(touch.x, touch.y)\n\t\t\tif (element) {\n\t\t\t\tthis.selectElement(element)\n\t\t\t\tthis.dragging = true\n\t\t\t} else {\n\t\t\t\tthis.deselectElement()\n\t\t\t}\n\t\t},\n\t\t\n\t\thandleTouchMove(e) {\n\t\t\tif (!this.dragging || !this.selectedElement) return\n\t\t\t\n\t\t\tconst touch = this.getTouchPosition(e)\n\t\t\tif (!touch) return\n\t\t\t\n\t\t\tconst deltaX = touch.x - this.lastTouchX\n\t\t\tconst deltaY = touch.y - this.lastTouchY\n\t\t\t\n\t\t\t// 移动元素\n\t\t\tthis.moveElement(this.selectedElement, deltaX, deltaY)\n\t\t\t\n\t\t\t// 更新触摸位置\n\t\t\tthis.lastTouchX = touch.x\n\t\t\tthis.lastTouchY = touch.y\n\t\t\t\n\t\t\t// 重绘画布\n\t\t\tthis.drawCanvas()\n\t\t},\n\t\t\n\t\thandleTouchEnd() {\n\t\t\tthis.dragging = false\n\t\t},\n\t\t\n\t\thandleCanvasTap(e) {\n\t\t\tif (this.dragging) return\n\t\t\t\n\t\t\tconst touch = this.getTouchPosition(e)\n\t\t\tif (!touch) return\n\t\t\t\n\t\t\tconst element = this.getElementAtPosition(touch.x, touch.y)\n\t\t\tif (element) {\n\t\t\t\tthis.selectElement(element)\n\t\t\t} else if (this.insertType) {\n\t\t\t\tthis.addElementAtPosition(touch.x, touch.y)\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 获取触摸位置（转换为Canvas坐标）\n\t\tgetTouchPosition(e) {\n\t\t\tconst touch = e.detail || e.touches?.[0] || e\n\t\t\tif (!touch) return null\n\t\t\t\n\t\t\t// 获取Canvas显示尺寸\n\t\t\tconst canvasStyle = this.canvasStyle\n\t\t\tconst displayWidth = parseFloat(canvasStyle.width)\n\t\t\tconst displayHeight = parseFloat(canvasStyle.height)\n\t\t\t\n\t\t\t// 转换为Canvas实际坐标\n\t\t\tconst scaleX = this.canvasWidth / displayWidth\n\t\t\tconst scaleY = this.canvasHeight / displayHeight\n\t\t\t\n\t\t\treturn {\n\t\t\t\tx: touch.x * scaleX,\n\t\t\t\ty: touch.y * scaleY\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 获取指定位置的元素\n\t\tgetElementAtPosition(x, y) {\n\t\t\t// 从后往前遍历（后添加的元素在上层）\n\t\t\tfor (let i = this.canvasElements.length - 1; i >= 0; i--) {\n\t\t\t\tconst element = this.canvasElements[i]\n\t\t\t\tif (this.isPointInElement(x, y, element)) {\n\t\t\t\t\treturn element\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn null\n\t\t},\n\t\t\n\t\t// 检查点是否在元素内\n\t\tisPointInElement(x, y, element) {\n\t\t\tif (element.type === 'text') {\n\t\t\t\tconst bounds = this.getElementBounds(element)\n\t\t\t\treturn x >= bounds.x && x <= bounds.x + bounds.width &&\n\t\t\t\t\t   y >= bounds.y && y <= bounds.y + bounds.height\n\t\t\t}\n\t\t\treturn false\n\t\t},\n\t\t\n\t\t// 选中元素\n\t\tselectElement(element) {\n\t\t\tthis.selectedElement = element\n\t\t\tthis.currentFontSize = element.fontSize || 24\n\t\t\tthis.currentTextColor = element.color || '#000000'\n\t\t\tthis.drawCanvas()\n\t\t},\n\t\t\n\t\t// 取消选中\n\t\tdeselectElement() {\n\t\t\tthis.selectedElement = null\n\t\t\tthis.drawCanvas()\n\t\t},\n\t\t\n\t\t// 移动元素\n\t\tmoveElement(element, deltaX, deltaY) {\n\t\t\telement.x += deltaX\n\t\t\telement.y += deltaY\n\t\t\t\n\t\t\t// 边界限制\n\t\t\telement.x = Math.max(20, Math.min(element.x, this.canvasWidth - 20))\n\t\t\telement.y = Math.max(20, Math.min(element.y, this.canvasHeight - 20))\n\t\t},\n\t\t\n\t\t// 在指定位置添加元素\n\t\taddElementAtPosition(x, y) {\n\t\t\tif (this.insertType === 'text') {\n\t\t\t\tconst newElement = {\n\t\t\t\t\tid: Date.now(),\n\t\t\t\t\ttype: 'text',\n\t\t\t\t\ttext: '新文字',\n\t\t\t\t\tx: x,\n\t\t\t\t\ty: y,\n\t\t\t\t\tcolor: this.currentTextColor,\n\t\t\t\t\tfontSize: this.currentFontSize\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.canvasElements.push(newElement)\n\t\t\t\tthis.selectElement(newElement)\n\t\t\t\tthis.insertType = null // 清除插入模式\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '元素已添加',\n\t\t\t\t\ticon: 'success',\n\t\t\t\t\tduration: 1000\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 设置插入类型\n\t\tsetInsertType(type) {\n\t\t\tthis.insertType = this.insertType === type ? null : type\n\t\t},\n\t\t\n\t\t// 设置背景类型\n\t\tsetBackgroundType(type) {\n\t\t\tthis.backgroundType = type\n\t\t\tthis.drawCanvas()\n\t\t},\n\t\t\n\t\t// 调整字体大小\n\t\tadjustFontSize(delta) {\n\t\t\tif (this.selectedElement) {\n\t\t\t\tthis.selectedElement.fontSize = Math.max(12, Math.min(100, (this.selectedElement.fontSize || 24) + delta))\n\t\t\t\tthis.currentFontSize = this.selectedElement.fontSize\n\t\t\t\tthis.drawCanvas()\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 更新字体大小\n\t\tupdateFontSize() {\n\t\t\tif (this.selectedElement) {\n\t\t\t\tthis.selectedElement.fontSize = this.currentFontSize\n\t\t\t\tthis.drawCanvas()\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 选择颜色\n\t\tselectColor(color) {\n\t\t\tthis.currentTextColor = color\n\t\t\tif (this.selectedElement) {\n\t\t\t\tthis.selectedElement.color = color\n\t\t\t\tthis.drawCanvas()\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 编辑元素文字\n\t\teditElementText() {\n\t\t\tif (this.selectedElement && this.selectedElement.type === 'text') {\n\t\t\t\tthis.editingText = this.selectedElement.text\n\t\t\t\tthis.showEditTextPopup = true\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 确认编辑文字\n\t\tconfirmEditText() {\n\t\t\tif (this.selectedElement && this.editingText.trim()) {\n\t\t\t\tthis.selectedElement.text = this.editingText.trim()\n\t\t\t\tthis.drawCanvas()\n\t\t\t}\n\t\t\tthis.closeEditTextPopup()\n\t\t},\n\t\t\n\t\t// 关闭编辑文字弹窗\n\t\tcloseEditTextPopup() {\n\t\t\tthis.showEditTextPopup = false\n\t\t\tthis.editingText = ''\n\t\t},\n\t\t\n\t\t// 复制元素\n\t\tduplicateElement() {\n\t\t\tif (this.selectedElement) {\n\t\t\t\tconst newElement = {\n\t\t\t\t\t...this.selectedElement,\n\t\t\t\t\tid: Date.now(),\n\t\t\t\t\tx: this.selectedElement.x + 20,\n\t\t\t\t\ty: this.selectedElement.y + 20\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.canvasElements.push(newElement)\n\t\t\t\tthis.selectElement(newElement)\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '元素已复制',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 删除元素\n\t\tdeleteElement() {\n\t\t\tif (this.selectedElement) {\n\t\t\t\tconst index = this.canvasElements.findIndex(el => el.id === this.selectedElement.id)\n\t\t\t\tif (index > -1) {\n\t\t\t\t\tthis.canvasElements.splice(index, 1)\n\t\t\t\t\tthis.selectedElement = null\n\t\t\t\t\tthis.drawCanvas()\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '元素已删除',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 居中对齐所有元素\n\t\tcenterAllElements() {\n\t\t\tthis.canvasElements.forEach(element => {\n\t\t\t\telement.x = this.canvasWidth / 2\n\t\t\t\telement.y = this.canvasHeight / 2\n\t\t\t})\n\t\t\tthis.drawCanvas()\n\t\t\t\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '已居中对齐',\n\t\t\t\ticon: 'success'\n\t\t\t})\n\t\t},\n\t\t\n\t\t// 清空所有元素\n\t\tclearAllElements() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '确认清空',\n\t\t\t\tcontent: '确定要清空画布上的所有元素吗？此操作不可撤销。',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tthis.canvasElements = []\n\t\t\t\t\t\tthis.selectedElement = null\n\t\t\t\t\t\tthis.drawCanvas()\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '画布已清空',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\t\t\n\t\t// 打开预览\n\t\topenPreview() {\n\t\t\tif (!this.hasElements) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先添加元素',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t\treturn\n\t\t\t}\n\t\t\t\n\t\t\t// 生成预览图片\n\t\t\tthis.generatePreviewImage()\n\t\t},\n\t\t\n\t\t// 生成预览图片\n\t\tgeneratePreviewImage() {\n\t\t\tif (!this.canvasContext) return\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 使用Canvas生成图片\n\t\t\t\tuni.canvasToTempFilePath({\n\t\t\t\t\tcanvasId: 'skylineCanvas',\n\t\t\t\t\twidth: this.canvasWidth,\n\t\t\t\t\theight: this.canvasHeight,\n\t\t\t\t\tdestWidth: this.canvasWidth,\n\t\t\t\t\tdestHeight: this.canvasHeight,\n\t\t\t\t\tfileType: 'png',\n\t\t\t\t\tquality: 1,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tuni.previewImage({\n\t\t\t\t\t\t\turls: [res.tempFilePath],\n\t\t\t\t\t\t\tcurrent: 0\n\t\t\t\t\t\t})\n\t\t\t\t\t},\n\t\t\t\t\tfail: (error) => {\n\t\t\t\t\t\tconsole.error('生成预览图片失败:', error)\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '预览生成失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}, this)\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('预览失败:', error)\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 保存模板\n\t\tsaveTemplate() {\n\t\t\tif (!this.canSave) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请完善模板信息',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t\treturn\n\t\t\t}\n\t\t\t\n\t\t\t// 生成模板数据\n\t\t\tconst templateData = {\n\t\t\t\tname: this.templateName,\n\t\t\t\telements: this.canvasElements,\n\t\t\t\tbackgroundType: this.backgroundType,\n\t\t\t\tcanvasWidth: this.canvasWidth,\n\t\t\t\tcanvasHeight: this.canvasHeight,\n\t\t\t\tcreateTime: new Date().toISOString()\n\t\t\t}\n\t\t\t\n\t\t\t// 保存到本地存储\n\t\t\ttry {\n\t\t\t\tconst savedTemplates = uni.getStorageSync('savedTemplates') || []\n\t\t\t\tsavedTemplates.push(templateData)\n\t\t\t\tuni.setStorageSync('savedTemplates', savedTemplates)\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '模板已保存',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t})\n\t\t\t\t\n\t\t\t\t// 延迟返回上一页\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.goBack()\n\t\t\t\t}, 1500)\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('保存失败:', error)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '保存失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 返回上一页\n\t\tgoBack() {\n\t\t\tuni.navigateBack()\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.template-editor {\n\tdisplay: flex;\n\tflex-direction: column;\n\theight: 100vh;\n\tbackground-color: #f8f9fa;\n}\n\n/* 顶部工具栏 */\n.toolbar {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\theight: 60px;\n\tpadding: 0 16px;\n\tbackground-color: #fff;\n\tborder-bottom: 1px solid #e5e5e5;\n\tbox-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.toolbar-left {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 12px;\n}\n\n.back-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\twidth: 40px;\n\theight: 40px;\n\tborder-radius: 8px;\n\tbackground-color: #f5f5f5;\n\tcursor: pointer;\n\ttransition: background-color 0.2s;\n}\n\n.back-btn:hover {\n\tbackground-color: #e9ecef;\n}\n\n.back-btn .icon {\n\tfont-size: 18px;\n\tcolor: #333;\n}\n\n.template-name-input {\n\tpadding: 8px 12px;\n\tborder: 1px solid #ddd;\n\tborder-radius: 6px;\n\tfont-size: 14px;\n\tmin-width: 150px;\n\toutline: none;\n}\n\n.template-name-input:focus {\n\tborder-color: #007AFF;\n}\n\n.toolbar-right {\n\tdisplay: flex;\n\tgap: 8px;\n}\n\n.tool-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 4px;\n\tpadding: 8px 12px;\n\tborder-radius: 6px;\n\tbackground-color: #f5f5f5;\n\tcursor: pointer;\n\ttransition: all 0.2s;\n\tfont-size: 14px;\n}\n\n.tool-btn:hover {\n\tbackground-color: #e9ecef;\n}\n\n.tool-btn.primary {\n\tbackground-color: #007AFF;\n\tcolor: white;\n}\n\n.tool-btn.primary:hover {\n\tbackground-color: #0056b3;\n}\n\n.tool-btn:disabled {\n\topacity: 0.5;\n\tcursor: not-allowed;\n}\n\n/* Canvas画布区域 */\n.canvas-container {\n\tposition: relative;\n\tborder-bottom: 1px solid #e5e5e5;\n}\n\n.canvas-wrapper {\n\tposition: relative;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.skyline-canvas {\n\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.canvas-hint {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n\tpointer-events: none;\n}\n\n.hint-text {\n\tcolor: #999;\n\tfont-size: 16px;\n\ttext-align: center;\n}\n\n/* 工具栏区域 */\n.toolbar-container {\n\tpadding: 16px;\n\tbackground-color: #fff;\n}\n\n.tool-section {\n\tmargin-bottom: 20px;\n}\n\n.section-title {\n\tmargin-bottom: 12px;\n}\n\n.section-title .title {\n\tfont-size: 16px;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n\n.tool-buttons {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 8px;\n}\n\n.tool-button {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 4px;\n\tpadding: 12px 8px;\n\tborder: 1px solid #ddd;\n\tborder-radius: 8px;\n\tbackground-color: #fff;\n\tcursor: pointer;\n\ttransition: all 0.2s;\n\tmin-width: 60px;\n}\n\n.tool-button:hover {\n\tborder-color: #007AFF;\n\tbackground-color: #f8f9ff;\n}\n\n.tool-button.active {\n\tborder-color: #007AFF;\n\tbackground-color: #007AFF;\n\tcolor: white;\n}\n\n.tool-button.small {\n\tmin-width: 50px;\n\tpadding: 8px 6px;\n}\n\n.tool-icon {\n\tfont-size: 20px;\n}\n\n.tool-label {\n\tfont-size: 12px;\n\ttext-align: center;\n}\n\n/* 属性控制 */\n.property-controls {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 16px;\n}\n\n.property-item {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tgap: 12px;\n}\n\n.property-item .label {\n\tfont-size: 14px;\n\tcolor: #333;\n\tmin-width: 40px;\n}\n\n.number-control {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8px;\n}\n\n.control-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\twidth: 32px;\n\theight: 32px;\n\tborder: 1px solid #ddd;\n\tborder-radius: 4px;\n\tbackground-color: #fff;\n\tcursor: pointer;\n\tfont-size: 16px;\n\ttransition: all 0.2s;\n}\n\n.control-btn:hover {\n\tborder-color: #007AFF;\n\tbackground-color: #f8f9ff;\n}\n\n.number-input {\n\twidth: 60px;\n\tpadding: 6px 8px;\n\tborder: 1px solid #ddd;\n\tborder-radius: 4px;\n\ttext-align: center;\n\tfont-size: 14px;\n\toutline: none;\n}\n\n.number-input:focus {\n\tborder-color: #007AFF;\n}\n\n.color-options {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 6px;\n}\n\n.color-item {\n\twidth: 24px;\n\theight: 24px;\n\tborder: 2px solid #ddd;\n\tborder-radius: 4px;\n\tcursor: pointer;\n\ttransition: all 0.2s;\n}\n\n.color-item:hover {\n\ttransform: scale(1.1);\n}\n\n.color-item.active {\n\tborder-color: #007AFF;\n\tborder-width: 3px;\n}\n\n/* 元素操作按钮 */\n.element-actions {\n\tdisplay: flex;\n\tgap: 8px;\n\tmargin-top: 12px;\n}\n\n.action-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 4px;\n\tpadding: 8px 12px;\n\tborder: 1px solid #ddd;\n\tborder-radius: 6px;\n\tbackground-color: #fff;\n\tcursor: pointer;\n\ttransition: all 0.2s;\n\tfont-size: 12px;\n}\n\n.action-btn:hover {\n\tborder-color: #007AFF;\n\tbackground-color: #f8f9ff;\n}\n\n.action-btn.danger {\n\tborder-color: #dc3545;\n\tcolor: #dc3545;\n}\n\n.action-btn.danger:hover {\n\tbackground-color: #dc3545;\n\tcolor: white;\n}\n\n.action-btn:disabled {\n\topacity: 0.5;\n\tcursor: not-allowed;\n}\n\n/* 快捷操作 */\n.quick-actions {\n\tdisplay: flex;\n\tgap: 12px;\n}\n\n/* 弹窗样式 */\n.popup-content {\n\twidth: 320px;\n\tmax-width: 90vw;\n\tbackground-color: #fff;\n\tborder-radius: 12px;\n\toverflow: hidden;\n}\n\n.popup-header {\n\tpadding: 16px;\n\tborder-bottom: 1px solid #e5e5e5;\n\ttext-align: center;\n}\n\n.popup-title {\n\tfont-size: 16px;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n\n.popup-body {\n\tpadding: 16px;\n}\n\n.text-input {\n\twidth: 100%;\n\tmin-height: 80px;\n\tpadding: 12px;\n\tborder: 1px solid #ddd;\n\tborder-radius: 6px;\n\tfont-size: 14px;\n\tresize: vertical;\n\toutline: none;\n}\n\n.text-input:focus {\n\tborder-color: #007AFF;\n}\n\n.popup-footer {\n\tdisplay: flex;\n\tgap: 12px;\n\tpadding: 16px;\n\tborder-top: 1px solid #e5e5e5;\n}\n\n.popup-btn {\n\tflex: 1;\n\tpadding: 12px;\n\tborder: none;\n\tborder-radius: 6px;\n\tfont-size: 14px;\n\tcursor: pointer;\n\ttransition: all 0.2s;\n\ttext-align: center;\n}\n\n.popup-btn.secondary {\n\tbackground-color: #f5f5f5;\n\tcolor: #333;\n}\n\n.popup-btn.secondary:hover {\n\tbackground-color: #e9ecef;\n}\n\n.popup-btn.primary {\n\tbackground-color: #007AFF;\n\tcolor: white;\n}\n\n.popup-btn.primary:hover {\n\tbackground-color: #0056b3;\n}\n\n.popup-btn:disabled {\n\topacity: 0.5;\n\tcursor: not-allowed;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n\t.toolbar {\n\t\tpadding: 0 12px;\n\t}\n\t\n\t.template-name-input {\n\t\tmin-width: 120px;\n\t\tfont-size: 13px;\n\t}\n\t\n\t.tool-btn {\n\t\tpadding: 6px 8px;\n\t\tfont-size: 12px;\n\t}\n\t\n\t.toolbar-container {\n\t\tpadding: 12px;\n\t}\n\t\n\t.tool-buttons {\n\t\tgap: 6px;\n\t}\n\t\n\t.tool-button {\n\t\tmin-width: 50px;\n\t\tpadding: 8px 6px;\n\t}\n\t\n\t.tool-icon {\n\t\tfont-size: 16px;\n\t}\n\t\n\t.tool-label {\n\t\tfont-size: 11px;\n\t}\n\t\n\t.popup-content {\n\t\twidth: 280px;\n\t}\n}\n</style>\n", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&id=6c6302e2&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&id=6c6302e2&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751873713557\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}