{"version": 3, "sources": ["webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/components/vol-tree/vol-tree.vue?d922", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/components/vol-tree/vol-tree.vue?f9dd", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/components/vol-tree/vol-tree.vue?18a8", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/components/vol-tree/vol-tree.vue?3f6e", "uni-app:///components/vol-tree/vol-tree.vue", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/components/vol-tree/vol-tree.vue?8317", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/components/vol-tree/vol-tree.vue?73f4"], "names": ["props", "data", "type", "default", "checkStrictly", "title", "closeIcon", "openIcon", "defaultIcon", "inited", "treeList", "showTree", "parentIds", "value", "methods", "confirm", "getIcon", "dataKeyIsString", "getAllParentId", "id", "index", "node", "ids", "show", "maskClick", "treeItemClick", "x", "item", "itemChildren", "console", "toggleVisible", "children", "getTreeAllChildren", "_parentIds", "nodes", "getList", "key", "name", "parentId", "checked", "lv", "open", "clicked", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initRootTreeData", "initNode", "i", "watch", "mounted"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACuL;AACvL,gBAAgB,2LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnDA;AAAA;AAAA;AAAA;AAAysB,CAAgB,wrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC6B7tB;EACAA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MAAA;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MAAA;MACAL;MACAC;IACA;IACAK;MAAA;MACAN;MACAC;IACA;EACA;EACAF;IACA;MACAQ;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;MACA;MACA;QACAC;MACA;QACAA;MACA;MACA;MAAA,2BACAC;QACAC;UACA;QACA;QACA;UACA;YAAA;UAAA;QACA;QACA;UACA;QACA;UACAC;QACA;MAAA;MAXA;QAAA,IACAD;QAAA,iBADAD;QAAA;MAYA;MACA;IACA;IACAG;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MAEA;QACA;UACAC;QACA;QACA;MACA;;MACA;QACA;QACAC;MACA;QACA;QACAA;MACA;QACAA;MACA;MACA;QACA;MACA;MAEA;QACA;QACA;QACA;MACA;MAEAA;MACA;MACA;QACA;MACA;MACAC;MACA;MACA;MACA;MACA;MACAC;IACA;IACAC;MACA;MACAC;QACAL;QACA;UACAA;QACA;MACA;MACAC;IACA;IACAK;MACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;MAAA,6BACAZ;QACAnB;UACA;YACAgC;YACAC;UACA;QACA;MAAA;MANA;QAAA;MAOA;MACA;IACA;IACAC;MACA;QACA;UACA;QACA;QACA;UACAhB;UACAiB;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAnB;UAAA;UACAoB;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;MACA;MACA;QACA;UACA;QACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MAEA;QACA;UACAnB;QACA;QACAA;QACAA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MAAA,6BAEAoB;QACA;QAEA;UACA;YACAnB;YACA;UACA;QACA;MAAA;MARA;QAAA;MASA;IACA;EACA;EACAoB;IACA9C;MACA;AACA;MACA;MACA;MACA;IACA;EACA;EACA+C;IACA;EAAA;AAEA;AAAA,4B;;;;;;;;;;;;AC7RA;AAAA;AAAA;AAAA;AAA4zC,CAAgB,wtCAAG,EAAC,C;;;;;;;;;;;ACAh1C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/vol-tree/vol-tree.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./vol-tree.vue?vue&type=template&id=0c772d04&scoped=true&\"\nvar renderjs\nimport script from \"./vol-tree.vue?vue&type=script&lang=js&\"\nexport * from \"./vol-tree.vue?vue&type=script&lang=js&\"\nimport style0 from \"./vol-tree.vue?vue&type=style&index=0&id=0c772d04&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0c772d04\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/vol-tree/vol-tree.vue\"\nexport default component.exports", "export * from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vol-tree.vue?vue&type=template&id=0c772d04&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.inited\n    ? _vm.__map(_vm.treeList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.getIcon(item)\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vol-tree.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vol-tree.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<view class=\"vol-tree-mask\" :class=\"{'show':showTree}\" @tap=\"maskClick\"></view>\n\t\t<view class=\"vol-tree-container\" :class=\"{'show':showTree}\">\n\t\t\t<view style=\"display: flex;flex-direction: column;height: 100%;\" v-if=\"inited\">\n\t\t\t\t<view class=\"vol-tree-header\">\n\t\t\t\t\t<view class=\"vol-tree-header-title\">{{title}}</view>\n\t\t\t\t\t<view class=\"vol-tree-header-confirm\" hover-class=\"hover-c\" @click=\"confirm\">\n\t\t\t\t\t\t确定</view>\n\t\t\t\t</view>\n\t\t\t\t<view style=\"flex: 1;height: 0;overflow-x: scroll;\">\n\t\t\t\t\t<view class=\"vol-tree-item\" :class=\"{show: item.show||item.lv===0,checked:item.checked}\"\n\t\t\t\t\t\tv-for=\"(item,index) in treeList\" :key=\"index\">\n\t\t\t\t\t\t<view class=\"vol-tree-item-child\" @tap.stop=\"treeItemClick(item,index)\">\n\t\t\t\t\t\t\t<view :style=\"{'margin-left':(item.lv*30)+'rpx'}\" class=\"vol-tree-item-child-label\">\n\t\t\t\t\t\t\t\t<image class=\"tree-left-icon\" :src=\"getIcon(item)\">\n\t\t\t\t\t\t\t\t</image> {{item.name}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"vol-tree-item-child-check\">\n\t\t\t\t\t\t\t\t<u-icon v-if=\"item.checked\" name=\"checkbox-mark\" size=\"16\" color=\"#2d92ff\"></u-icon>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n<script>\n\texport default {\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: Array,\n\t\t\t\tdefault: () => {\n\t\t\t\t\treturn []\n\t\t\t\t}\n\t\t\t},\n\t\t\tcheckStrictly: { //是否只能选择最后一个节点\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\ttitle: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"标题\"\n\t\t\t},\n\t\t\tcloseIcon: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'data:image/png;base64,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'\n\t\t\t},\n\t\t\topenIcon: { // 折叠时候的ic\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'data:image/png;base64,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'\n\t\t\t},\n\t\t\tdefaultIcon: { // 没有子集的ic\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'data:image/png;base64,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'\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tinited: false,\n\t\t\t\ttreeList: [],\n\t\t\t\tshowTree: false,\n\t\t\t\tparentIds: [],\n\t\t\t\tvalue:null\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tconfirm() {\n\t\t\t\tthis.showTree = false;\n\t\t\t\tlet obj = this.treeList.find(x => {\n\t\t\t\t\treturn x.checked\n\t\t\t\t}) || {};\n\t\t\t\tthis.$emit('confirm', obj.id, obj);\n\t\t\t},\n\t\t\tgetIcon(item) {\n\t\t\t\tif (item.hasChildren) {\n\t\t\t\t\treturn item.open ? this.openIcon : this.closeIcon;\n\t\t\t\t}\n\t\t\t\treturn this.defaultIcon;\n\t\t\t},\n\t\t\tdataKeyIsString() {\n\t\t\t\treturn this.data.some((x) => {\n\t\t\t\t\treturn typeof(x.id) == 'string'\n\t\t\t\t});\n\t\t\t},\n\t\t\tgetAllParentId(id, data) {\n\t\t\t\tif (id === null || id === '' || id === undefined) {\n\t\t\t\t\treturn []\n\t\t\t\t}\n\t\t\t\tif (this.dataKeyIsString()) {\n\t\t\t\t\tid = id + '';\n\t\t\t\t} else {\n\t\t\t\t\tid = id * 1;\n\t\t\t\t}\n\t\t\t\tvar ids = [id]; // [parentNode.parentId];\n\t\t\t\tfor (let index = 0; index < ids.length; index++) {\n\t\t\t\t\tvar node = this.data.find((x) => {\n\t\t\t\t\t\treturn x.id === ids[index]\n\t\t\t\t\t});\n\t\t\t\t\tif (!node || (node.parentId === null && node.parentId === undefined)) {\n\t\t\t\t\t\treturn ids;\n\t\t\t\t\t}\n\t\t\t\t\tif (this.data.some(x => {\n\t\t\t\t\t\t\treturn x.id === node.parentId\n\t\t\t\t\t\t})) {\n\t\t\t\t\t\tids.push(node.parentId);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn ids;\n\t\t\t},\n\t\t\tshow(value) {\n           this.value=value;\n\t\t\t\t// let parentIds = this.getAllParentId(value).reverse();\n\n\t\t\t\t// this.treeList.forEach(x => {\n\t\t\t\t// \tif (x.lv > 0) {\n\t\t\t\t// \t\tx.show = false;\n\t\t\t\t// \t}\n\t\t\t\t// \tx.open = false; // parentIds.indexOf(x.id) == -1 ? false : true;\n\t\t\t\t// \tx.checked = false;\n\t\t\t\t// })\n\t\t\t\t// if (!this.data.length) {\n\t\t\t\t// \treturn;\n\t\t\t\t// }\n\t\t\t\t// if (!this.inited) {\n\t\t\t\t// \tthis.initRootTreeData(this.data);\n\t\t\t\t// }\n\n\t\t\t\t// for (let i = 0; i < parentIds.length; i++) {\n\t\t\t\t// \tlet parentId = parentIds[i];\n\n\t\t\t\t// \tthis.treeList.forEach((item, index) => {\n\t\t\t\t// \t\tif (item.id === parentId) {\n\t\t\t\t// \t\t\titem.open = false;\n\t\t\t\t// \t\t\tthis.treeItemClick(item, index)\n\t\t\t\t// \t\t}\n\t\t\t\t// \t})\n\t\t\t\t// }\n\t\t\t\tthis.inited = true;\n\t\t\t\tthis.showTree = true;\n\t\t\t},\n\t\t\tmaskClick() {\n\t\t\t\tthis.showTree = false;\n\t\t\t},\n\t\t\ttreeItemClick(item, index) {\n\n\t\t\t\tthis.treeList.forEach(x => {\n\t\t\t\t\tif (item.id !== x.id) {\n\t\t\t\t\t\tx.checked = false;\n\t\t\t\t\t}\n\t\t\t\t\t//x.checked =  item.id === x.id;\n\t\t\t\t})\n\t\t\t\tif (!item.hasChildren) {\n\t\t\t\t\t//最后一级可以任意取消\n\t\t\t\t\titem.checked = !item.checked;\n\t\t\t\t} else if (!this.checkStrictly || (this.checkStrictly && !item.hasChildren)) {\n\t\t\t\t\t//限制只能选择最后一个节点,不包括子节点时才可以选择\n\t\t\t\t\titem.checked = true;\n\t\t\t\t} else {\n\t\t\t\t\titem.checked = false;\n\t\t\t\t}\n\t\t\t\tif (!item.hasChildren) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (item.clicked) {\n\t\t\t\t\tthis.toggleVisible(item);\n\t\t\t\t\t// /item.show = !item.show;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\titem.clicked = true;\n\t\t\t\t//获取子节点\n\t\t\t\tlet itemChildren = this.data.filter(x => {\n\t\t\t\t\treturn x.parentId == item.id\n\t\t\t\t})\n\t\t\t\titemChildren = this.getList(itemChildren, this.data, item.lv + 1)\n\t\t\t\tthis.treeList.splice(index + 1, 0, ...itemChildren);\n\t\t\t\t//\tsetTimeout(() => {\n\t\t\t\tthis.toggleVisible(item, true)\n\t\t\t\t//}, 50)\n\t\t\t\tconsole.log(JSON.stringify(this.treeList))\n\t\t\t},\n\t\t\ttoggleVisible(item) {\n\t\t\t\tconst children = this.getTreeAllChildren(item.id, this.treeList);\n\t\t\t\tchildren.forEach(x => {\n\t\t\t\t\tx.show = !item.open; // show; // !item.show;\n\t\t\t\t\tif (x.clicked) {\n\t\t\t\t\t\tx.open = x.show;\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\titem.open = !item.open;\n\t\t\t},\n\t\t\tgetTreeAllChildren(id, data) {\n\t\t\t\t//递归获取某个节点的所有子节点信息\n\t\t\t\tvar _children = data.filter((x) => {\n\t\t\t\t\treturn x.parentId === id;\n\t\t\t\t});\n\t\t\t\tconst nodes = [..._children];\n\t\t\t\tconst _parentIds = _children.map(x => {\n\t\t\t\t\treturn x.id\n\t\t\t\t});\n\t\t\t\tfor (let index = 0; index < _parentIds.length; index++) {\n\t\t\t\t\tdata.forEach((_node) => {\n\t\t\t\t\t\tif (_node.parentId === _parentIds[index]) {\n\t\t\t\t\t\t\t_parentIds.push(_node.id);\n\t\t\t\t\t\t\tnodes.unshift(_node);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn nodes;\n\t\t\t},\n\t\t\tgetList(list, data, lv) {\n\t\t\t\treturn list.map(x => {\n\t\t\t\t\tlet hasChildren = data.some(c => {\n\t\t\t\t\t\treturn c.parentId === x.id && c.parentId != undefined\n\t\t\t\t\t})\n\t\t\t\t\treturn {\n\t\t\t\t\t\tid: x.id,\n\t\t\t\t\t\tkey: x.key,\n\t\t\t\t\t\tname:x.name|| x.value,\n\t\t\t\t\t\tparentId: x.parentId,\n\t\t\t\t\t\tchecked: false,\n\t\t\t\t\t\tlv: lv,\n\t\t\t\t\t\topen: false,\n\t\t\t\t\t\tclicked: false,\n\t\t\t\t\t\tshow: false, // lv > 0 ? true : false,\n\t\t\t\t\t\thasChildren: hasChildren\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tinitRootTreeData(list) { //获取一级节点\n\t\t\t\tif (!list.length) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tlet _list = list.filter(x => {\n\t\t\t\t\treturn !x.parentId || x.parentId === x.id || !list.some(c => {\n\t\t\t\t\t\treturn x.parentId === c.id\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t\tthis.treeList = this.getList(_list, list, 0);\n\t\t\t\tthis.initNode(this.value);\n\t\t\t},\n\t\t\tinitNode(value){\n\t\t\t\tlet parentIds = this.getAllParentId(value).reverse();\n\t\t\t\t\n\t\t\t\tthis.treeList.forEach(x => {\n\t\t\t\t\tif (x.lv > 0) {\n\t\t\t\t\t\tx.show = false;\n\t\t\t\t\t}\n\t\t\t\t\tx.open = false; // parentIds.indexOf(x.id) == -1 ? false : true;\n\t\t\t\t\tx.checked = false;\n\t\t\t\t})\n\t\t\t\tif (!this.data.length) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (!this.inited) {\n\t\t\t\t\tthis.initRootTreeData(this.data);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tfor (let i = 0; i < parentIds.length; i++) {\n\t\t\t\t\tlet parentId = parentIds[i];\n\t\t\t\t\n\t\t\t\t\tthis.treeList.forEach((item, index) => {\n\t\t\t\t\t\tif (item.id === parentId) {\n\t\t\t\t\t\t\titem.open = false;\n\t\t\t\t\t\t\tthis.treeItemClick(item, index)\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tdata(list) {\n\t\t\t/* \tconsole.log(list);\n\t\t\t\tconsole.log('data') */\n\t\t\t\t//if (this.inited) {\n\t\t\t\t  this.initRootTreeData(list);\n\t\t\t\t//}\n\t\t\t}\n\t\t},\n\t\tmounted() {\n\t\t\t//this.initRootTreeData(this.data)\n\t\t}\n\t}\n</script>\n<style lang=\"less\" scoped>\n\t.vol-tree-container {\n\t\tposition: fixed;\n\t\t/* \ttop: 0rpx; */\n\t\tright: 0rpx;\n\t\tbottom: 0rpx;\n\t\tleft: 0rpx;\n\t\tz-index: 9999;\n\t\ttop: 360rpx;\n\t\t/* min-height: 500rpx; */\n\t\ttransition: all 0.3s ease;\n\t\ttransform: translateY(100%);\n\t\tbackground: #ffff;\n\n\n\t\t.vol-tree-item {\n\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #585858;\n\t\t\theight: 0;\n\t\t\topacity: 0;\n\t\t\t//transition: 0.2s;\n\t\t\tposition: relative;\n\t\t\toverflow: hidden;\n\t\t\tpadding-left: 20rpx !important;\n\t\t}\n\n\t\t.vol-tree-item.checked {\n\t\t\tbackground: #f4f9ff;\n\t\t}\n\n\t\t.vol-tree-item.show {\n\t\t\tpadding: 16rpx;\n\t\t\t//padding: 16rpx;\n\t\t\theight: 40rpx;\n\t\t\topacity: 1;\n\t\t}\n\n\t\t.vol-tree-item-child {\n\t\t\tdisplay: flex;\n\n\t\t\t.vol-tree-item-child-label {\n\t\t\t\tflex: 1;\n\t\t\t}\n\t\t}\n\n\t\t.tree-left-icon {\n\t\t\twidth: 26rpx;\n\t\t\theight: 26rpx;\n\t\t\tmargin-right: 8rpx;\n\t\t}\n\n\t\t.vol-tree-item-child-check {\n\t\t\tpadding-left: 0 20rpx;\n\t\t}\n\t}\n\n\t.vol-tree-container.show {\n\t\ttransform: translateY(0);\n\t}\n\n\t.vol-tree-mask {\n\t\tposition: fixed;\n\t\ttop: 0rpx;\n\t\tright: 0rpx;\n\t\tbottom: 0rpx;\n\t\tleft: 0rpx;\n\t\tz-index: 9998;\n\t\tbackground-color: rgba(0, 0, 0, 0.6);\n\t\topacity: 0;\n\t\ttransition: all 0.3s ease;\n\t\tvisibility: hidden;\n\t}\n\n\t.vol-tree-mask.show {\n\t\tvisibility: visible;\n\t\topacity: 1;\n\t}\n\n\t.vol-tree-header {\n\t\t// display: flex;\n\t\t// text-align: center;\n\t\t// position: relative;\n\t\t// padding: 0 26rpx;\n\t\t// border-bottom: 1px solid #f1f1f1;\n\t\t// padding: 24rpx;\n\t\ttext-align: center;\n\t\tposition: relative;\n\t\tborder-bottom: 1px solid rgb(233 233 233);\n\n\t\t.vol-tree-header-title {\n\t\t\t// font-weight: bolder;\n\t\t\t// flex: 1;\n\t\t\t// padding: 30rpx 0;\n\t\t\tpadding: 24rpx;\n\t\t}\n\n\t\t.vol-tree-header-confirm {\n\t\t\tposition: absolute;\n\t\t\tright: 30rpx;\n\t\t\theight: 100%;\n\t\t\ttop: 8rpx;\n\t\t\tcolor: #3495ff;\n\t\t\tpadding-top: 20rpx;\n\t\t\tbottom: 0;\n\t\t\tfont-size: 28rpx;\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vol-tree.vue?vue&type=style&index=0&id=0c772d04&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vol-tree.vue?vue&type=style&index=0&id=0c772d04&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751873715893\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}