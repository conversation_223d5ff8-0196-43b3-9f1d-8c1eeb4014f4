<view class="container"><view class="content"><view class="screen-mode-tabs"><view class="tab-row"><view data-event-opts="{{[['tap',[['switchScreenMode',['same']]]]]}}" class="{{['tab-item',(screenMode==='same')?'active':'']}}" style="width:50%;" bindtap="__e"><text>前后屏相同</text></view><view data-event-opts="{{[['tap',[['switchScreenMode',['different']]]]]}}" class="{{['tab-item',(screenMode==='different')?'active':'']}}" style="width:50%;" bindtap="__e"><text>前后屏不同</text></view></view></view><view class="preview-area"><block wx:if="{{screenMode==='same'}}"><view class="preview-card"><view class="template-preview"><view class="chinese-style-template front-screen" style="{{'background-image:'+(currentTemplate&&currentTemplate.background?'url('+currentTemplate.background+')':'url(/static/images/public_template/33882e8f90e990a4f1f140011e7a58ce.png)')+';'}}"><view class="screen-tag">前</view></view><view class="operation-buttons"><view class="op-button-row"><view data-event-opts="{{[['tap',[['showTemplateSelector',['$event']]]]]}}" class="op-button" style="width:50%;" bindtap="__e"><image class="op-icon" src="/static/images/template-icon.svg" mode="aspectFit"></image><text class="op-text">选择模板</text></view><view data-event-opts="{{[['tap',[['showContentEditor',['$event']]]]]}}" class="op-button" style="width:50%;" bindtap="__e"><image class="op-icon" src="/static/images/edit-icon.svg" mode="aspectFit"></image><text class="op-text">编辑内容</text></view></view><view class="bottom-gradient-line"></view></view></view></view></block><block wx:if="{{screenMode==='different'}}"><view class="preview-cards"><view><view class="preview-card"><view class="template-preview"><view class="chinese-style-template front-screen" style="{{'background-image:'+(currentTemplate&&currentTemplate.background?'url('+currentTemplate.background+')':'url(/static/images/public_template/33882e8f90e990a4f1f140011e7a58ce.png)')+';'}}"><view class="screen-tag">前</view></view></view></view><view class="operation-buttons"><view class="op-button-row"><view data-event-opts="{{[['tap',[['showTemplateSelector',['$event']]]]]}}" class="op-button" style="width:50%;" bindtap="__e"><image class="op-icon" src="/static/images/template-icon.svg" mode="aspectFit"></image><text class="op-text">选择模板</text></view><view data-event-opts="{{[['tap',[['showContentEditor',['$event']]]]]}}" class="op-button" style="width:50%;" bindtap="__e"><image class="op-icon" src="/static/images/edit-icon.svg" mode="aspectFit"></image><text class="op-text">编辑内容</text></view></view><view class="bottom-gradient-line"></view></view></view><view><view class="preview-card"><view class="template-preview"><view class="chinese-style-template back-screen" style="{{'background-image:'+(currentTemplate&&currentTemplate.background?'url('+currentTemplate.background+')':'url(/static/images/public_template/f2e05295636f1b849b791a21b5548e30.png)')+';'}}"><view class="screen-tag">后</view></view></view></view><view class="operation-buttons"><view class="op-button-row"><view data-event-opts="{{[['tap',[['showTemplateSelector',['$event']]]]]}}" class="op-button" style="width:50%;" bindtap="__e"><image class="op-icon" src="/static/images/template-icon.svg" mode="aspectFit"></image><text class="op-text">选择模板</text></view><view data-event-opts="{{[['tap',[['showContentEditor',['$event']]]]]}}" class="op-button" style="width:50%;" bindtap="__e"><image class="op-icon" src="/static/images/edit-icon.svg" mode="aspectFit"></image><text class="op-text">编辑内容</text></view></view><view class="bottom-gradient-line"></view></view></view></view></block></view></view><view class="footer"><view data-event-opts="{{[['tap',[['confirmScreen',['$event']]]]]}}" class="confirm-button" bindtap="__e"><text>确认无误，下一步</text></view></view><u-popup vue-id="047e0f5e-1" show="{{showTemplates}}" mode="bottom" round="10" closeable="{{true}}" data-event-opts="{{[['^close',[['closeTemplateSelector']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="template-popup"><view class="popup-title"><text>选择模板</text></view><scroll-view class="template-scroll" scroll-x="true" show-scrollbar="false"><view class="template-list"><block wx:for="{{templates}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectTemplate',['$0'],[[['templates','',index]]]]]]]}}" class="{{['template-item',(selectedTemplate===item.id)?'active':'']}}" bindtap="__e"><image class="template-image" src="{{item.image}}" mode="aspectFill"></image><text class="template-name">{{item.name}}</text></view></block></view></scroll-view></view></u-popup><u-popup vue-id="047e0f5e-2" show="{{showEditor}}" mode="bottom" round="10" closeable="{{true}}" data-event-opts="{{[['^close',[['closeContentEditor']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="editor-popup"><view class="popup-title"><text>编辑内容</text></view><view class="edit-form"><block wx:for="{{editFields}}" wx:for-item="field" wx:for-index="index" wx:key="index"><view class="form-item"><text class="form-label">{{field.label}}</text><input class="form-input" placeholder="{{field.placeholder}}" data-event-opts="{{[['input',[['__set_model',['$0','value','$event',[]],[[['editFields','',index]]]]]]]}}" value="{{field.value}}" bindinput="__e"/></view></block></view><view data-event-opts="{{[['tap',[['saveContent',['$event']]]]]}}" class="popup-button" bindtap="__e"><text>保存</text></view></view></u-popup></view>