
.container.data-v-56ce4180 {
	display: flex;
	flex-direction: column;
	align-items: center;
	min-height: 100vh;
	background-image: url('http://14.103.146.84:8890/i/2025/06/12/login-bg.png');
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;
	padding: 40px 20px;
	padding-bottom: calc(20px + env(safe-area-inset-bottom));
	box-sizing: border-box;
}
.header.data-v-56ce4180 {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	margin-bottom: 30px;
}
.visitor-title.data-v-56ce4180 {
	width: 300px;
	height: 80px;
}
.content.data-v-56ce4180 {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 100%;
	max-width: 400px;
	padding: 0 20px;
	margin-bottom: 20px;
}
.description.data-v-56ce4180 {
	margin-bottom: 15px;
	text-align: center;
	line-height: 1.6;
}
.desc-text.data-v-56ce4180 {
	font-size: 16px;
	color: #333;
	line-height: 1.8;
}
.features.data-v-56ce4180 {
	display: flex;
	justify-content: space-around;
	width: 100%;
	margin-top: 40px;
	gap: 20px;
}
.feature-item.data-v-56ce4180 {
	display: flex;
	flex-direction: column;
	align-items: center;
}
.feature-circle.data-v-56ce4180 {
	width: 80px;
	height: 80px;
	border: 2px solid #8B4513;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	background-color: rgba(255, 255, 255, 0.8);
}
.feature-text.data-v-56ce4180 {
	font-size: 14px;
	color: #8B4513;
	font-weight: bold;
	line-height: 1.2;
}
.footer.data-v-56ce4180 {
	width: 100%;
	max-width: 300px;
	padding: 20px 0;
	margin-top: auto;
}
.login-button.data-v-56ce4180 {
	width: 100%;
	height: 48px;
	line-height: 48px;
	border-radius: 8px;
	font-size: 16px;
	text-align: center;
	color: #fff;
	border: none;
	background-color: #8B4513;
}

