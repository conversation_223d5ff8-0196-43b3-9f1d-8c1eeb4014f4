
.template-editor.data-v-4aed2b70 {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
}

/* 预览区域 */
.preview-container.data-v-4aed2b70 {
	position: relative;
	margin: 10px;
	padding: 8px;
	background-color: #ffffff;
	border-radius: 8px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
	border: 2px solid #e0e0e0;
	overflow: visible; /* 确保按钮不被裁剪 */
}
.preview-canvas.data-v-4aed2b70 {
	display: block;
	width: 100%;
	height: 100%;
	z-index: 1000;
}

/* 元素操作按钮 */
.element-buttons.data-v-4aed2b70 {
	position: absolute;
	display: flex;
	gap: 8px;
	z-index: 99999999;
	pointer-events: auto;
}
.element-btn.data-v-4aed2b70 {
	width: 32px;
	height: 32px;
	border-radius: 16px;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
	cursor: pointer;
	transition: all 0.3s ease;
	border: 2px solid #fff;
	pointer-events: auto;
	user-select: none;
	-webkit-user-select: none;
	touch-action: manipulation;
	-webkit-touch-callout: none;
	-webkit-tap-highlight-color: transparent;
	position: relative;
	z-index: 99999999;
}
.edit-btn.data-v-4aed2b70 {
	background-color: #007AFF;
}
.zoom-in-btn.data-v-4aed2b70 {
	background-color: #007AFF;
}
.zoom-out-btn.data-v-4aed2b70 {
	background-color: #FF9500;
}
.rotate-btn.data-v-4aed2b70 {
	background-color: #FF9500;
}
.delete-btn.data-v-4aed2b70 {
	background-color: #FF3B30;
}
.element-btn.data-v-4aed2b70:hover {
	-webkit-transform: scale(1.1);
	        transform: scale(1.1);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}
.element-btn.data-v-4aed2b70:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
}
.btn-icon.data-v-4aed2b70 {
	font-size: 14px;
	color: #fff;
	line-height: 1;
}

/* 设置区域 */
.template-settings.data-v-4aed2b70, .background-settings.data-v-4aed2b70 {
	padding: 15px;
	margin: 0 10px 10px;
	background-color: #ffffff;
	border-radius: 8px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 批量操作区域 */
.control-group.data-v-4aed2b70 {
	padding: 15px;
	margin: 0 10px 10px;
	background-color: #ffffff;
	border-radius: 8px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}
.group-title.data-v-4aed2b70 {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 12px;
	display: block;
}
.button-row.data-v-4aed2b70 {
	display: flex;
	gap: 8px;
	margin-bottom: 8px;
}
.button-row.data-v-4aed2b70:last-child {
	margin-bottom: 0;
}
.control-btn.data-v-4aed2b70 {
	flex: 1;
	height: 36px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid #ddd;
	border-radius: 6px;
	background-color: #fff;
	font-size: 13px;
	color: #666;
	transition: all 0.3s ease;
	cursor: pointer;
}
.control-btn.data-v-4aed2b70:hover {
	background-color: #f8f9fa;
	border-color: #007AFF;
	color: #007AFF;
	-webkit-transform: translateY(-1px);
	        transform: translateY(-1px);
	box-shadow: 0 2px 8px rgba(0, 122, 255, 0.15);
}
.control-btn.data-v-4aed2b70:active {
	-webkit-transform: translateY(0);
	        transform: translateY(0);
	box-shadow: 0 1px 4px rgba(0, 122, 255, 0.2);
}

/* 危险操作按钮 */
.danger-btn.data-v-4aed2b70 {
	border-color: #ff4757;
	color: #ff4757;
}
.danger-btn.data-v-4aed2b70:hover {
	background-color: #ff4757;
	color: #fff;
	border-color: #ff4757;
	box-shadow: 0 2px 8px rgba(255, 71, 87, 0.25);
}
.danger-btn.data-v-4aed2b70:active {
	background-color: #ff3742;
	border-color: #ff3742;
}
.setting-row.data-v-4aed2b70 {
	display: flex;
	align-items: center;
	margin-bottom: 15px;
	position: relative;
	z-index: 2; /* 提高层级 */
	pointer-events: auto; /* 确保可点击 */
}
.setting-row.data-v-4aed2b70:last-child {
	margin-bottom: 0;
}
.setting-label.data-v-4aed2b70 {
	font-size: 14px;
	color: #333;
	width: 80px;
	flex-shrink: 0;
}
.template-name-input.data-v-4aed2b70 {
	flex: 1;
	height: 35px;
	padding: 0 10px;
	border: 1px solid #ddd;
	border-radius: 4px;
	font-size: 14px;
}

/* 按钮组 */
.insert-options.data-v-4aed2b70, .background-options.data-v-4aed2b70 {
	display: flex;
	flex: 1;
	gap: 8px;
	position: relative;
	z-index: 2; /* 提高层级 */
}
.insert-btn.data-v-4aed2b70, .bg-btn.data-v-4aed2b70 {
	flex: 1;
	height: 35px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid #ddd;
	border-radius: 4px;
	background-color: #fff;
	font-size: 12px;
	color: #666;
	transition: all 0.3s;
	position: relative; /* 确保定位正确 */
	z-index: 1; /* 提高层级 */
	pointer-events: auto; /* 确保可点击 */
}
.insert-btn.active.data-v-4aed2b70, .bg-btn.active.data-v-4aed2b70 {
	background-color: #d32f2f;
	color: #fff;
	border-color: #d32f2f;
}

/* 属性面板 */
.property-panel.data-v-4aed2b70 {
	/* flex: 1; */
	padding: 15px;
	margin: 0 10px;
	background-color: #ffffff;
	border-radius: 8px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
	height: 40vh;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

/* 属性内容容器 */
.property-container.data-v-4aed2b70 {
	/* flex: 1; */
	overflow-y: auto;
	-webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
	scrollbar-width: thin; /* Firefox细滚动条 */
	scrollbar-color: #c1c1c1 transparent; /* Firefox滚动条颜色 */
}

/* 自定义滚动条样式 - Webkit内核 */
.property-container.data-v-4aed2b70::-webkit-scrollbar {
	width: 6px;
}
.property-container.data-v-4aed2b70::-webkit-scrollbar-track {
	background: transparent;
	border-radius: 3px;
}
.property-container.data-v-4aed2b70::-webkit-scrollbar-thumb {
	background: #c1c1c1;
	border-radius: 3px;
	-webkit-transition: background 0.3s ease;
	transition: background 0.3s ease;
}
.property-container.data-v-4aed2b70::-webkit-scrollbar-thumb:hover {
	background: #a8a8a8;
}

/* 响应式设计 - 小屏幕优化 */
@media (max-height: 600px) {
.property-panel.data-v-4aed2b70 {
		height: 40vh; /* 小屏幕增加高度比例 */
}
}
@media (max-height: 500px) {
.property-panel.data-v-4aed2b70 {
		height: 45vh; /* 更小屏幕进一步增加高度比例 */
}
}

/* 移动端触摸优化 */
@media (max-width: 768px) {
.property-panel.data-v-4aed2b70 {
		padding: 12px; /* 移动端减少内边距 */
		margin: 0 5px; /* 减少外边距 */
}
.property-container.data-v-4aed2b70::-webkit-scrollbar {
		width: 4px; /* 移动端更细的滚动条 */
}
}

/* 面板头部 - 固定定位 */
.panel-header.data-v-4aed2b70 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15px;
	position: -webkit-sticky;
	position: sticky;
	top: 0;
	z-index: 10;
	background-color: #ffffff;
	padding: 10px 0;
	border-bottom: 1px solid #f0f0f0;
	margin: -15px -15px 15px -15px;
	padding-left: 15px;
	padding-right: 15px;
}
.panel-title.data-v-4aed2b70 {
	font-size: 16px;
	font-weight: bold;
	color: #333;
}
.property-group.data-v-4aed2b70 {
	margin-bottom: 20px;
}
.property-row.data-v-4aed2b70 {
	display: flex;
	align-items: center;
	margin-top:9px ;
	margin-bottom: 9px;
}
.property-label.data-v-4aed2b70 {
	font-size: 13px;
	color: #333;
	width: 70px;
	flex-shrink: 0;
}

/* 对齐按钮 */
.align-options.data-v-4aed2b70, .style-options.data-v-4aed2b70 {
	display: flex;
	flex: 1;
	gap: 4px;
}
.align-btn.data-v-4aed2b70, .style-btn.data-v-4aed2b70 {
	width: 32px;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid #ddd;
	border-radius: 4px;
	background-color: #fff;
	font-size: 14px;
	color: #666;
	transition: all 0.3s;
}
.align-btn.active.data-v-4aed2b70, .style-btn.active.data-v-4aed2b70 {
	background-color: #d32f2f;
	color: #fff;
	border-color: #d32f2f;
}

/* 保存按钮容器 */
.save-container.data-v-4aed2b70 {
	padding: 15px;
	display: flex;
	justify-content: center;
	gap: 15px;
	flex-wrap: wrap;
}

/* 预览按钮 */
.preview-btn.data-v-4aed2b70 {
	width: 140px;
	height: 45px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #4A90E2 0%, #357ABD 50%, #4A90E2 100%);
	border-radius: 25px;
	position: relative;
	overflow: hidden;
	box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
	transition: all 0.3s ease;
}
.preview-btn.data-v-4aed2b70:hover {
	-webkit-transform: translateY(-2px);
	        transform: translateY(-2px);
	box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
}
.preview-btn.data-v-4aed2b70::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
	transition: left 0.5s;
}
.preview-btn.data-v-4aed2b70:active::before {
	left: 100%;
}
.preview-btn.data-v-4aed2b70:active {
	-webkit-transform: translateY(0);
	        transform: translateY(0);
}

/* 保存按钮 */
.save-btn.data-v-4aed2b70 {
	width: 140px;
	height: 45px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #8B0000 0%, #DC143C 50%, #8B0000 100%);
	border-radius: 25px;
	position: relative;
	overflow: hidden;
	box-shadow: 0 4px 15px rgba(139, 0, 0, 0.3);
	transition: all 0.3s ease;
}
.save-btn.data-v-4aed2b70:hover {
	-webkit-transform: translateY(-2px);
	        transform: translateY(-2px);
	box-shadow: 0 6px 20px rgba(139, 0, 0, 0.4);
}
.save-btn.data-v-4aed2b70::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
	transition: left 0.5s;
}
.save-btn.data-v-4aed2b70:active::before {
	left: 100%;
}
.save-btn.data-v-4aed2b70:active {
	-webkit-transform: translateY(0);
	        transform: translateY(0);
}

/* 按钮图标样式 */
.btn-icon.data-v-4aed2b70 {
	color: #fff;
	font-size: 18px;
	margin-right: 6px;
	z-index: 1;
	pointer-events: none;
	-webkit-filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.3));
	        filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.3));
}

/* 按钮文字样式 */
.btn-text.data-v-4aed2b70 {
	color: #fff;
	font-size: 16px;
	font-weight: bold;
	text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
	z-index: 1;
	pointer-events: none;
}

/* 响应式设计 - 保持并排显示 */
@media (max-width: 480px) {
.save-container.data-v-4aed2b70 {
		padding: 10px;
		gap: 8px;
		justify-content: center;
}
.preview-btn.data-v-4aed2b70,
	.save-btn.data-v-4aed2b70 {
		width: 110px;
		height: 40px;
}
.btn-icon.data-v-4aed2b70 {
		font-size: 16px;
		margin-right: 4px;
}
.btn-text.data-v-4aed2b70 {
		font-size: 14px;
}
}

/* 超小屏幕优化 - 保持并排显示 */
@media (max-width: 360px) {
.save-container.data-v-4aed2b70 {
		gap: 6px;
		padding: 8px;
}
.preview-btn.data-v-4aed2b70,
	.save-btn.data-v-4aed2b70 {
		width: 90px;
		height: 36px;
}
.btn-icon.data-v-4aed2b70 {
		font-size: 14px;
		margin-right: 2px;
}
.btn-text.data-v-4aed2b70 {
		font-size: 12px;
}
}

/* 模板字段选择器弹窗 */
.field-picker-modal.data-v-4aed2b70 {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: flex-end;
	justify-content: center;
	z-index: 1000;
	-webkit-animation: fadeIn-data-v-4aed2b70 0.3s ease-out;
	        animation: fadeIn-data-v-4aed2b70 0.3s ease-out;
}
.field-picker-content.data-v-4aed2b70 {
	background-color: #fff;
	border-radius: 12px 12px 0 0;
	padding: 0;
	max-width: 90%;
	max-height: 80%;
	width: 400px;
	box-shadow: 0 -5px 30px rgba(0, 0, 0, 0.3);
	overflow: hidden;
	-webkit-animation: slideUp-data-v-4aed2b70 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	        animation: slideUp-data-v-4aed2b70 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	-webkit-transform-origin: bottom;
	        transform-origin: bottom;
}

/* 弹窗动画 */
@-webkit-keyframes fadeIn-data-v-4aed2b70 {
from {
		opacity: 0;
}
to {
		opacity: 1;
}
}
@keyframes fadeIn-data-v-4aed2b70 {
from {
		opacity: 0;
}
to {
		opacity: 1;
}
}
@-webkit-keyframes slideUp-data-v-4aed2b70 {
from {
		-webkit-transform: translateY(100%);
		        transform: translateY(100%);
		opacity: 0.8;
}
to {
		-webkit-transform: translateY(0);
		        transform: translateY(0);
		opacity: 1;
}
}
@keyframes slideUp-data-v-4aed2b70 {
from {
		-webkit-transform: translateY(100%);
		        transform: translateY(100%);
		opacity: 0.8;
}
to {
		-webkit-transform: translateY(0);
		        transform: translateY(0);
		opacity: 1;
}
}

/* 退出动画 */
.field-picker-modal.fade-out.data-v-4aed2b70 {
	-webkit-animation: fadeOut-data-v-4aed2b70 0.25s ease-in;
	        animation: fadeOut-data-v-4aed2b70 0.25s ease-in;
}
.field-picker-modal.fade-out .field-picker-content.data-v-4aed2b70 {
	-webkit-animation: slideDown-data-v-4aed2b70 0.3s cubic-bezier(0.55, 0.06, 0.68, 0.19);
	        animation: slideDown-data-v-4aed2b70 0.3s cubic-bezier(0.55, 0.06, 0.68, 0.19);
}
@-webkit-keyframes fadeOut-data-v-4aed2b70 {
from {
		opacity: 1;
}
to {
		opacity: 0;
}
}
@keyframes fadeOut-data-v-4aed2b70 {
from {
		opacity: 1;
}
to {
		opacity: 0;
}
}
@-webkit-keyframes slideDown-data-v-4aed2b70 {
from {
		-webkit-transform: translateY(0);
		        transform: translateY(0);
		opacity: 1;
}
to {
		-webkit-transform: translateY(100%);
		        transform: translateY(100%);
		opacity: 0.8;
}
}
@keyframes slideDown-data-v-4aed2b70 {
from {
		-webkit-transform: translateY(0);
		        transform: translateY(0);
		opacity: 1;
}
to {
		-webkit-transform: translateY(100%);
		        transform: translateY(100%);
		opacity: 0.8;
}
}
.field-picker-header.data-v-4aed2b70 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px;
	background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
	color: white;
}
.field-picker-title.data-v-4aed2b70 {
	font-size: 18px;
	font-weight: bold;
}
.field-picker-close.data-v-4aed2b70 {
	width: 30px;
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background-color: rgba(255, 255, 255, 0.2);
	font-size: 20px;
	cursor: pointer;
	transition: background-color 0.3s;
}
.field-picker-close.data-v-4aed2b70:hover {
	background-color: rgba(255, 255, 255, 0.3);
}
.field-grid.data-v-4aed2b70 {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 15px;
	padding: 20px;
	max-height: 400px;
	overflow-y: auto;
}
.field-item.data-v-4aed2b70 {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 15px;
	border: 2px solid #e0e0e0;
	border-radius: 8px;
	background-color: #fafafa;
	cursor: pointer;
	transition: all 0.3s;
	text-align: center;
}
.field-item.data-v-4aed2b70:hover {
	border-color: #d32f2f;
	background-color: #fff;
	-webkit-transform: translateY(-2px);
	        transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(211, 47, 47, 0.15);
}
.field-icon.data-v-4aed2b70 {
	font-size: 24px;
	margin-bottom: 8px;
}
.field-name.data-v-4aed2b70 {
	font-size: 14px;
	font-weight: bold;
	color: #333;
	margin-bottom: 4px;
}
.field-desc.data-v-4aed2b70 {
	font-size: 12px;
	color: #666;
	line-height: 1.3;
}

/* 位置信息显示 */
.position-info.data-v-4aed2b70 {
	flex: 1;
	font-size: 12px;
	color: #666;
	background-color: #f8f9fa;
	padding: 6px 10px;
	border-radius: 4px;
	border: 1px solid #e9ecef;
	font-family: 'Courier New', monospace;
}

/* 预览容器增强 */
.preview-container.data-v-4aed2b70 {
	position: relative;
	background-color: #ffffff;
	margin: 10px;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	overflow: visible; /* 改为visible避免裁剪 */ 
	display: flex;
	align-items: flex-start; /* 改为flex-start避免压缩 */
	justify-content: center;
	padding: 8px;
	box-sizing: border-box;
	touch-action: none; /* 防止默认触摸行为 */
}

/* 画布样式 */
.preview-canvas.data-v-4aed2b70 {
	display: block;
	border: 2px solid #e0e0e0;
	border-radius: 4px;
	background-color: #fafafa;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
	/* 移除max-width和max-height限制，让画布按计算尺寸显示 */
	transition: box-shadow 0.3s ease;
}
.preview-canvas.data-v-4aed2b70:hover {
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* 弹窗样式 */
.template-popup-content.data-v-4aed2b70, .preview-popup-content.data-v-4aed2b70, .edit-text-popup-content.data-v-4aed2b70, .property-popup-content.data-v-4aed2b70 {
	padding: 20px;
	background-color: #fff;
	border-radius: 16px;
	min-width: 300px;
	max-width: 90vw;
}

/* 属性编辑弹窗特定样式 */
.property-popup-content.data-v-4aed2b70 {
	max-width: 100vw;
	max-height: 70vh;
	display: flex;
	flex-direction: column;
	padding: 0;
	border-radius: 16px 16px 0 0;
}
.property-title.data-v-4aed2b70 {
	font-size: 18px;
	font-weight: bold;
	color: #333;
	text-align: center;
	padding: 16px 0;
	border-bottom: 1px solid #f0f0f0;
	background-color: #fff;
	flex-shrink: 0;
	border-radius: 16px 16px 0 0;
}
.popup-header.data-v-4aed2b70 {
	padding: 20px 20px 0 20px;
	flex-shrink: 0;
}
.popup-scroll-content.data-v-4aed2b70 {
	flex: 1;
	padding: 8px 16px;
	overflow-y: auto;
}
.style-buttons.data-v-4aed2b70 {
	display: flex;
	gap: 8px;
	flex: 1;
	margin-left: 12px;
}
.style-btn.data-v-4aed2b70 {
	width: 32px;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid #e0e0e0;
	border-radius: 4px;
	background-color: #fff;
	cursor: pointer;
	transition: all 0.2s;
	-webkit-user-select: none;
	        user-select: none;
}
.style-btn.data-v-4aed2b70:hover {
	background-color: #f0f0f0;
	border-color: #007aff;
}
.style-btn.active.data-v-4aed2b70 {
	background-color: #007aff;
	border-color: #007aff;
	color: #fff;
}
.popup-title.data-v-4aed2b70 {
	font-size: 18px;
	font-weight: bold;
	color: #333;
	text-align: center;
	margin-bottom: 20px;
}
.template-field.data-v-4aed2b70 {
	margin-bottom: 15px;
}
.field-label.data-v-4aed2b70 {
	font-size: 14px;
	color: #333;
	margin-bottom: 8px;
	display: block;
}
.field-input.data-v-4aed2b70 {
	width: 100%;
	height: 40px;
	padding: 0 12px;
	border: 1px solid #ddd;
	border-radius: 6px;
	font-size: 14px;
	box-sizing: border-box;
}
.field-input.data-v-4aed2b70:focus {
	border-color: #d32f2f;
	outline: none;
}

/* 弹窗按钮样式 */
.popup-btns.data-v-4aed2b70 {
	display: flex;
	gap: 12px;
	margin-top: 20px;
	padding: 0 20px 20px 20px;
}
.popup-btn.data-v-4aed2b70 {
	flex: 1;
	height: 44px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 8px;
	font-size: 16px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.2s;
}
.popup-btn.cancel.data-v-4aed2b70 {
	background-color: #f8f9fa;
	color: #6c757d;
	border: 1px solid #dee2e6;
}
.popup-btn.cancel.data-v-4aed2b70:hover {
	background-color: #e9ecef;
}
.popup-btn.confirm.data-v-4aed2b70 {
	background-color: #007aff;
	color: #fff;
	border: 1px solid #007aff;
}
.popup-btn.confirm.data-v-4aed2b70:hover {
	background-color: #0056b3;
}

/* 文字编辑弹窗专用样式 */
.edit-form-container.data-v-4aed2b70 {
	padding: 0 16px 16px 16px;
	max-width: 500px;
	margin: 0 auto;
}
.element-actions.data-v-4aed2b70 {
	display: flex;
	justify-content: center;
	gap: 16px;
	margin-bottom: 24px;
	padding: 16px;
	background-color: #f8f9fa;
	border-radius: 12px;
	border: 1px solid #e9ecef;
}
.action-btn.data-v-4aed2b70 {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 12px 20px;
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.3s ease;
	min-width: 80px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.edit-btn.data-v-4aed2b70 {
	background: linear-gradient(135deg, #007aff 0%, #0056b3 100%);
	color: white;
}
.edit-btn.data-v-4aed2b70:hover {
	background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
	-webkit-transform: translateY(-2px);
	        transform: translateY(-2px);
	box-shadow: 0 4px 8px rgba(0, 122, 255, 0.3);
}
.delete-btn.data-v-4aed2b70 {
	background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
	color: white;
}
.delete-btn.data-v-4aed2b70:hover {
	background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
	-webkit-transform: translateY(-2px);
	        transform: translateY(-2px);
	box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}
.action-icon.data-v-4aed2b70 {
	font-size: 20px;
	margin-bottom: 4px;
}
.action-text.data-v-4aed2b70 {
	font-size: 12px;
	font-weight: 500;
	text-align: center;
}
.edit-form.data-v-4aed2b70 {
	padding: 0 16px;
}
.property-row.data-v-4aed2b70 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 12px 0;
	border-bottom: 1px solid #f5f5f5;
}
.property-row.data-v-4aed2b70:last-child {
	border-bottom: none;
}
.property-label.data-v-4aed2b70 {
	font-size: 14px;
	color: #333;
	font-weight: 500;
	min-width: 80px;
}
.property-picker.data-v-4aed2b70 {
	flex: 1;
	margin-left: 12px;
	cursor: pointer;
}
.text-input.data-v-4aed2b70 {
	flex: 1;
	margin-left: 12px;
	padding: 8px 12px;
	border: 1px solid #e0e0e0;
	border-radius: 6px;
	background-color: #fff;
	font-size: 14px;
	color: #333;
	outline: none;
}
.text-input.data-v-4aed2b70:focus {
	border-color: #007aff;
	box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);
}
.picker-value.data-v-4aed2b70 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 8px 12px;
	border: 1px solid #e0e0e0;
	border-radius: 6px;
	background-color: #fafafa;
	font-size: 14px;
	color: #666;
}
.picker-arrow.data-v-4aed2b70 {
	font-size: 12px;
	color: #999;
}
.color-preview-small.data-v-4aed2b70 {
	width: 20px;
	height: 20px;
	border-radius: 4px;
	border: 1px solid #ddd;
	flex-shrink: 0;
}
.color-name.data-v-4aed2b70 {
	flex: 1;
	font-size: 14px;
	color: #666;
	margin-left: 8px;
}
.slider-container.data-v-4aed2b70 {
	flex: 1;
	margin-left: 12px;
	padding: 0 8px;
}
.style-text.data-v-4aed2b70 {
	font-size: 14px;
	font-weight: bold;
	color: inherit;
}
.preview-image-container.data-v-4aed2b70 {
	width: 300px;
	height: 200px;
	border: 1px solid #ddd;
	border-radius: 6px;
	overflow: hidden;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 20px;
}
.preview-image.data-v-4aed2b70 {
	width: 100%;
	height: 100%;
}
.preview-loading.data-v-4aed2b70, .preview-error.data-v-4aed2b70 {
	color: #666;
	font-size: 14px;
}



