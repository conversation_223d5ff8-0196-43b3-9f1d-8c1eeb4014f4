{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/home/<USER>", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/home/<USER>", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/home/<USER>", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/home/<USER>", "uni-app:///pages/home/<USER>", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/home/<USER>", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/home/<USER>"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "statusBarHeight", "greeting", "weatherText", "todayScreens", "connectedDevices", "activeMeetings", "recentItems", "icon", "title", "time", "onLoad", "methods", "initPage", "getSystemInfo", "console", "updateGreeting", "loadStatistics", "checkLoginStatus", "uni", "url", "goToSingleScreen", "goToBatchScreen", "goToTemplate", "goToMeeting", "viewMore", "openRecentItem"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AACuL;AACvL,gBAAgB,2LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAqsB,CAAgB,orBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCuHztB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,cACA;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA;IAEA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAEA;QACA;QACA;MACA;QACA;QACAC;QACA;QACA;MACA;IAMA;IAEA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IAAA,CACA;IAEA;IACAC;MACA;MACA;QACAC;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACAF;QACAC;MACA;IACA;IAEA;IACAE;MACAH;QACAC;MACA;IACA;IAEA;IACAG;MACA;MACAJ;QACAC;QACA;MACA;IACA;IAEA;IACAI;MACAL;QACAC;MACA;IACA;IAEA;IACAK;MACAN;QACAV;QACAD;MACA;IACA;IAEA;IACAkB;MACAP;QACAV;QACAD;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7PA;AAAA;AAAA;AAAA;AAAg0C,CAAgB,2tCAAG,EAAC,C;;;;;;;;;;;ACAp1C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/home/<USER>", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/home/<USER>'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./home.vue?vue&type=template&id=92bb8f34&\"\nvar renderjs\nimport script from \"./home.vue?vue&type=script&lang=js&\"\nexport * from \"./home.vue?vue&type=script&lang=js&\"\nimport style0 from \"./home.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/home/<USER>\"\nexport default component.exports", "export * from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./home.vue?vue&type=template&id=92bb8f34&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./home.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./home.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 状态栏占位 -->\r\n\t\t<view class=\"status-bar\" :style=\"{height: statusBarHeight + 'px'}\"></view>\r\n\r\n\t\t<!-- 自定义导航栏 -->\r\n\t\t<!-- <view class=\"custom-navbar\">\r\n\t\t\t<view class=\"navbar-content\">\r\n\t\t\t\t<text class=\"navbar-title\">智能芯桌牌</text>\r\n\t\t\t\t<view class=\"navbar-right\">\r\n\t\t\t\t\t<view class=\"weather-info\">\r\n\t\t\t\t\t\t<text class=\"weather-text\">{{ weatherText }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\r\n\t\t<!-- 主要内容区域 -->\r\n\t\t<view class=\"content\">\r\n\t\t\t<!-- 欢迎卡片 -->\r\n\t\t\t<view class=\"welcome-card\">\r\n\t\t\t\t<view class=\"welcome-content\">\r\n\t\t\t\t\t<view class=\"welcome-text\">\r\n\t\t\t\t\t\t<text class=\"greeting\">{{ greeting }}</text>\r\n\t\t\t\t\t\t<text class=\"welcome-desc\">欢迎使用智能芯桌牌系统</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"welcome-icon\">\r\n\t\t\t\t\t\t<text class=\"icon-bluetooth\">📶</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-decoration\"></view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 快速功能区 -->\r\n\t\t\t<view class=\"quick-actions\">\r\n\t\t\t\t<text class=\"section-title\">快速功能</text>\r\n\t\t\t\t<view class=\"action-grid\">\r\n\t\t\t\t\t<view class=\"action-item\" @click=\"goToSingleScreen\">\r\n\t\t\t\t\t\t<view class=\"action-icon single\">\r\n\t\t\t\t\t\t\t<text class=\"icon\">📱</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"action-text\">单一投屏</text>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"action-item\" @click=\"goToBatchScreen\">\r\n\t\t\t\t\t\t<view class=\"action-icon batch\">\r\n\t\t\t\t\t\t\t<text class=\"icon\">📺</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"action-text\">批量投屏</text>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"action-item\" @click=\"goToTemplate\">\r\n\t\t\t\t\t\t<view class=\"action-icon template\">\r\n\t\t\t\t\t\t\t<text class=\"icon\">🎨</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"action-text\">模板管理</text>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"action-item\" @click=\"goToMeeting\">\r\n\t\t\t\t\t\t<view class=\"action-icon meeting\">\r\n\t\t\t\t\t\t\t<text class=\"icon\">📅</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"action-text\">会议管理</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 统计信息卡片 -->\r\n\t\t\t<view class=\"stats-card\">\r\n\t\t\t\t<text class=\"card-title\">今日统计</text>\r\n\t\t\t\t<view class=\"stats-content\">\r\n\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t<text class=\"stat-number\">{{ todayScreens }}</text>\r\n\t\t\t\t\t\t<text class=\"stat-label\">投屏次数</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"stat-divider\"></view>\r\n\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t<text class=\"stat-number\">{{ connectedDevices }}</text>\r\n\t\t\t\t\t\t<text class=\"stat-label\">连接设备</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"stat-divider\"></view>\r\n\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t<text class=\"stat-number\">{{ activeMeetings }}</text>\r\n\t\t\t\t\t\t<text class=\"stat-label\">活跃会议</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 最近使用 -->\r\n\t\t\t<view class=\"recent-section\">\r\n\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t<text class=\"section-title\">最近使用</text>\r\n\t\t\t\t\t<text class=\"more-text\" @click=\"viewMore\">查看更多</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"recent-list\">\r\n\t\t\t\t\t<view class=\"recent-item\" v-for=\"(item, index) in recentItems\" :key=\"index\" @click=\"openRecentItem(item)\">\r\n\t\t\t\t\t\t<view class=\"recent-icon\">\r\n\t\t\t\t\t\t\t<text class=\"icon\">{{ item.icon }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"recent-info\">\r\n\t\t\t\t\t\t\t<text class=\"recent-title\">{{ item.title }}</text>\r\n\t\t\t\t\t\t\t<text class=\"recent-time\">{{ item.time }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"arrow\">›</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 背景装饰 -->\r\n\t\t<view class=\"background-decoration\">\r\n\t\t\t<view class=\"bg-circle circle1\"></view>\r\n\t\t\t<view class=\"bg-circle circle2\"></view>\r\n\t\t\t<view class=\"bg-circle circle3\"></view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tstatusBarHeight: 0,\r\n\t\t\t\tgreeting: '早上好',\r\n\t\t\t\tweatherText: '晴 22°C',\r\n\t\t\t\ttodayScreens: 12,\r\n\t\t\t\tconnectedDevices: 8,\r\n\t\t\t\tactiveMeetings: 3,\r\n\t\t\t\trecentItems: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ticon: '📱',\r\n\t\t\t\t\t\ttitle: '会议室A - 投屏',\r\n\t\t\t\t\t\ttime: '2分钟前'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ticon: '🎨',\r\n\t\t\t\t\t\ttitle: '新建模板',\r\n\t\t\t\t\t\ttime: '10分钟前'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ticon: '📅',\r\n\t\t\t\t\t\ttitle: '周例会',\r\n\t\t\t\t\t\ttime: '1小时前'\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// 页面加载时的处理逻辑\r\n\t\t\tthis.initPage();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 初始化页面\r\n\t\t\tinitPage() {\r\n\t\t\t\tthis.getSystemInfo();\r\n\t\t\t\tthis.checkLoginStatus();\r\n\t\t\t\tthis.updateGreeting();\r\n\t\t\t\tthis.loadStatistics();\r\n\t\t\t},\r\n\r\n\t\t\t// 获取系统信息\r\n\t\t\tgetSystemInfo() {\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst windowInfo = uni.getWindowInfo();\r\n\t\t\t\t\tthis.statusBarHeight = windowInfo.statusBarHeight || 0;\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\t// 兼容旧版本\r\n\t\t\t\t\tconsole.warn('使用新API失败，回退到旧API:', e);\r\n\t\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\r\n\t\t\t\t\tthis.statusBarHeight = systemInfo.statusBarHeight || 0;\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\r\n\t\t\t\tthis.statusBarHeight = systemInfo.statusBarHeight || 0;\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\r\n\t\t\t// 更新问候语\r\n\t\t\tupdateGreeting() {\r\n\t\t\t\tconst hour = new Date().getHours();\r\n\t\t\t\tif (hour < 12) {\r\n\t\t\t\t\tthis.greeting = '早上好';\r\n\t\t\t\t} else if (hour < 18) {\r\n\t\t\t\t\tthis.greeting = '下午好';\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.greeting = '晚上好';\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 加载统计数据\r\n\t\t\tloadStatistics() {\r\n\t\t\t\t// 这里可以调用API获取真实数据\r\n\t\t\t\t// 暂时使用模拟数据\r\n\t\t\t},\r\n\r\n\t\t\t// 检查登录状态\r\n\t\t\tcheckLoginStatus() {\r\n\t\t\t\tconst token = uni.getStorageSync('token');\r\n\t\t\t\tif (!token) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 导航到单一投屏页面\r\n\t\t\tgoToSingleScreen() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/subPackage/public/single-screen'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 导航到批量投屏页面\r\n\t\t\tgoToBatchScreen() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/subPackage/public/batch-screen'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 导航到模板管理\r\n\t\t\tgoToTemplate() {\r\n\t\t\t\t//console.log(\"点击了\")\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/subPackage/template/edit_new'\r\n\t\t\t\t\t//url: '/pages/subPackage/template/edit'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 导航到会议管理\r\n\t\t\tgoToMeeting() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/subPackage/meeting/list'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 查看更多\r\n\t\t\tviewMore() {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '功能开发中',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 打开最近使用项目\r\n\t\t\topenRecentItem(item) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: `打开 ${item.title}`,\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n\r\n<style lang=\"scss\">\r\n.container {\r\n\tposition: relative;\r\n\tmin-height: 100vh;\r\n\tbackground-image: url('http://*************:8890/i/2025/06/12/login-bg.png');\r\n\tbackground-size: cover;\r\n\tbackground-repeat: no-repeat;\r\n\tbackground-position: center;\r\n\toverflow-x: hidden;\r\n}\r\n\r\n.status-bar {\r\n\twidth: 100%;\r\n\tbackground: transparent;\r\n}\r\n\r\n.custom-navbar {\r\n\tposition: relative;\r\n\tz-index: 100;\r\n\tbackground: rgba(255, 255, 255, 0.1);\r\n\tbackdrop-filter: blur(10px);\r\n\tborder-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.navbar-content {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 20rpx 40rpx;\r\n\theight: 88rpx;\r\n}\r\n\r\n.navbar-title {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #FFFFFF;\r\n}\r\n\r\n.navbar-right {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.weather-info {\r\n\tbackground: rgba(255, 255, 255, 0.2);\r\n\tborder-radius: 20rpx;\r\n\tpadding: 8rpx 16rpx;\r\n}\r\n\r\n.weather-text {\r\n\tfont-size: 24rpx;\r\n\tcolor: #FFFFFF;\r\n}\r\n\r\n.content {\r\n\tpadding: 40rpx 30rpx;\r\n\tpadding-bottom: 120rpx;\r\n}\r\n\r\n/* 欢迎卡片 */\r\n.welcome-card {\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tborder-radius: 24rpx;\r\n\tmargin-bottom: 40rpx;\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.welcome-content {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 40rpx;\r\n}\r\n\r\n.welcome-text {\r\n\tflex: 1;\r\n}\r\n\r\n.greeting {\r\n\tfont-size: 48rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #2C3E50;\r\n\tdisplay: block;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.welcome-desc {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tdisplay: block;\r\n}\r\n\r\n.welcome-icon {\r\n\twidth: 80rpx;\r\n\theight: 80rpx;\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tborder-radius: 40rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.icon-bluetooth {\r\n\tfont-size: 40rpx;\r\n}\r\n\r\n.card-decoration {\r\n\tposition: absolute;\r\n\tright: -20rpx;\r\n\ttop: -20rpx;\r\n\twidth: 100rpx;\r\n\theight: 100rpx;\r\n\tbackground: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);\r\n\tborder-radius: 50rpx;\r\n}\r\n\r\n/* 快速功能区 */\r\n.quick-actions {\r\n\tmargin-bottom: 40rpx;\r\n}\r\n\r\n.section-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #FFFFFF;\r\n\tmargin-bottom: 24rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.action-grid {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: 1fr 1fr;\r\n\tgap: 20rpx;\r\n}\r\n\r\n.action-item {\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tborder-radius: 20rpx;\r\n\tpadding: 30rpx 20rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\ttransition: all 0.3s ease;\r\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.action-item:active {\r\n\ttransform: scale(0.95);\r\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.action-icon {\r\n\twidth: 80rpx;\r\n\theight: 80rpx;\r\n\tborder-radius: 40rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tmargin-bottom: 16rpx;\r\n}\r\n\r\n.action-icon.single {\r\n\tbackground: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n}\r\n\r\n.action-icon.batch {\r\n\tbackground: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\r\n}\r\n\r\n.action-icon.template {\r\n\tbackground: linear-gradient(135deg, #fa709a 0%, #fee140 100%);\r\n}\r\n\r\n.action-icon.meeting {\r\n\tbackground: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);\r\n}\r\n\r\n.action-icon .icon {\r\n\tfont-size: 36rpx;\r\n}\r\n\r\n.action-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 500;\r\n}\r\n\r\n/* 统计信息卡片 */\r\n.stats-card {\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tborder-radius: 20rpx;\r\n\tpadding: 30rpx;\r\n\tmargin-bottom: 40rpx;\r\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.card-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 24rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.stats-content {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n}\r\n\r\n.stat-item {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tflex: 1;\r\n}\r\n\r\n.stat-number {\r\n\tfont-size: 48rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #667eea;\r\n\tmargin-bottom: 8rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.stat-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tdisplay: block;\r\n}\r\n\r\n.stat-divider {\r\n\twidth: 2rpx;\r\n\theight: 60rpx;\r\n\tbackground: #eee;\r\n\tmargin: 0 20rpx;\r\n}\r\n\r\n/* 最近使用 */\r\n.recent-section {\r\n\tmargin-bottom: 40rpx;\r\n}\r\n\r\n.section-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 24rpx;\r\n}\r\n\r\n.more-text {\r\n\tfont-size: 26rpx;\r\n\tcolor: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.recent-list {\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tborder-radius: 20rpx;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.recent-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 24rpx 30rpx;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n\ttransition: background-color 0.2s;\r\n}\r\n\r\n.recent-item:last-child {\r\n\tborder-bottom: none;\r\n}\r\n\r\n.recent-item:active {\r\n\tbackground-color: #f8f9fa;\r\n}\r\n\r\n.recent-icon {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tborder-radius: 30rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tmargin-right: 24rpx;\r\n}\r\n\r\n.recent-icon .icon {\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.recent-info {\r\n\tflex: 1;\r\n}\r\n\r\n.recent-title {\r\n\tfont-size: 30rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 500;\r\n\tmargin-bottom: 4rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.recent-time {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tdisplay: block;\r\n}\r\n\r\n.arrow {\r\n\tfont-size: 32rpx;\r\n\tcolor: #ccc;\r\n}\r\n\r\n/* 背景装饰 */\r\n.background-decoration {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tpointer-events: none;\r\n\tz-index: -1;\r\n}\r\n\r\n.bg-circle {\r\n\tposition: absolute;\r\n\tborder-radius: 50%;\r\n\tbackground: rgba(255, 255, 255, 0.05);\r\n}\r\n\r\n.circle1 {\r\n\twidth: 300rpx;\r\n\theight: 300rpx;\r\n\ttop: 20%;\r\n\tright: -100rpx;\r\n\tanimation: float1 20s infinite ease-in-out;\r\n}\r\n\r\n.circle2 {\r\n\twidth: 200rpx;\r\n\theight: 200rpx;\r\n\tbottom: 30%;\r\n\tleft: -50rpx;\r\n\tanimation: float2 15s infinite ease-in-out;\r\n}\r\n\r\n.circle3 {\r\n\twidth: 150rpx;\r\n\theight: 150rpx;\r\n\ttop: 60%;\r\n\tright: 20%;\r\n\tanimation: float3 25s infinite ease-in-out;\r\n}\r\n\r\n@keyframes float1 {\r\n\t0%, 100% { transform: translateY(0px) rotate(0deg); }\r\n\t50% { transform: translateY(-30rpx) rotate(180deg); }\r\n}\r\n\r\n@keyframes float2 {\r\n\t0%, 100% { transform: translateX(0px) rotate(0deg); }\r\n\t50% { transform: translateX(20rpx) rotate(-180deg); }\r\n}\r\n\r\n@keyframes float3 {\r\n\t0%, 100% { transform: translate(0px, 0px) rotate(0deg); }\r\n\t33% { transform: translate(20rpx, -20rpx) rotate(120deg); }\r\n\t66% { transform: translate(-20rpx, 10rpx) rotate(240deg); }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./home.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./home.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751873716557\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}