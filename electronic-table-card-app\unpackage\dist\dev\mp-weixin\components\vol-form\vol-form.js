(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["components/vol-form/vol-form"],{

/***/ 297:
/*!*********************************************************************************************!*\
  !*** D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/components/vol-form/vol-form.vue ***!
  \*********************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _vol_form_vue_vue_type_template_id_6726b7c4_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./vol-form.vue?vue&type=template&id=6726b7c4&scoped=true& */ 298);
/* harmony import */ var _vol_form_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./vol-form.vue?vue&type=script&lang=js& */ 300);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _vol_form_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _vol_form_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _vol_form_vue_vue_type_style_index_0_id_6726b7c4_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./vol-form.vue?vue&type=style&index=0&id=6726b7c4&lang=less&scoped=true& */ 302);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 32);

var renderjs





/* normalize component */

var component = Object(_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _vol_form_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _vol_form_vue_vue_type_template_id_6726b7c4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _vol_form_vue_vue_type_template_id_6726b7c4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "6726b7c4",
  null,
  false,
  _vol_form_vue_vue_type_template_id_6726b7c4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "components/vol-form/vol-form.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 298:
/*!****************************************************************************************************************************************!*\
  !*** D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/components/vol-form/vol-form.vue?vue&type=template&id=6726b7c4&scoped=true& ***!
  \****************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_vol_form_vue_vue_type_template_id_6726b7c4_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vol-form.vue?vue&type=template&id=6726b7c4&scoped=true& */ 299);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_vol_form_vue_vue_type_template_id_6726b7c4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_vol_form_vue_vue_type_template_id_6726b7c4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_vol_form_vue_vue_type_template_id_6726b7c4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_vol_form_vue_vue_type_template_id_6726b7c4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 299:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/components/vol-form/vol-form.vue?vue&type=template&id=6726b7c4&scoped=true& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uToast: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uview-ui/components/u-toast/u-toast */ "uni_modules/uview-ui/components/u-toast/u-toast").then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-toast/u-toast.vue */ 365))
    },
    uParse: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-parse/u-parse */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-parse/u-parse")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-parse/u-parse.vue */ 372))
    },
    uIcon: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-icon/u-icon */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-icon/u-icon")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-icon/u-icon.vue */ 304))
    },
    uRadioGroup: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-radio-group/u-radio-group */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-radio-group/u-radio-group")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-radio-group/u-radio-group.vue */ 381))
    },
    uRadio: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-radio/u-radio */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-radio/u-radio")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-radio/u-radio.vue */ 389))
    },
    uUpload: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-upload/u-upload */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-upload/u-upload")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-upload/u-upload.vue */ 331))
    },
    uDatetimePicker: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-datetime-picker/u-datetime-picker */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-datetime-picker/u-datetime-picker")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-datetime-picker/u-datetime-picker.vue */ 397))
    },
    uPopup: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-popup/u-popup */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-popup/u-popup")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-popup/u-popup.vue */ 323))
    },
    uSearch: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-search/u-search */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-search/u-search")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-search/u-search.vue */ 406))
    },
    volTree: function () {
      return __webpack_require__.e(/*! import() | components/vol-tree/vol-tree */ "components/vol-tree/vol-tree").then(__webpack_require__.bind(null, /*! @/components/vol-tree/vol-tree.vue */ 414))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var l2 = _vm.__map(_vm.formOptions, function (item, index) {
    var $orig = _vm.__get_orig(item)
    var l0 =
      (item.readonly || item.disabled) && item.type == "img"
        ? _vm.getImgSrcs(item)
        : null
    var m0 =
      (item.readonly || item.disabled) && !(item.type == "img")
        ? _vm.formatReadonlyValue(item)
        : null
    var g0 =
      !(item.readonly || item.disabled) &&
      !(item.type == "editor") &&
      item.type == "city"
        ? _vm.base.isEmpty(_vm.inFormFields[item.field], true)
        : null
    var g1 =
      !(item.readonly || item.disabled) &&
      !(item.type == "editor") &&
      item.type == "city"
        ? _vm.base.isEmpty(_vm.inFormFields[item.field], true)
        : null
    var g2 =
      !(item.readonly || item.disabled) &&
      !(item.type == "editor") &&
      item.type == "city"
        ? _vm.inFormFields[item.field].replace(/,/g, "")
        : null
    var g3 =
      !(item.readonly || item.disabled) &&
      !(item.type == "editor") &&
      !(item.type == "city") &&
      (item.type == "date" ||
        item.type == "datetime" ||
        item.type == "month") &&
      item.range &&
      item.type == "date"
        ? (_vm.inFormFields[item.field][0] || "").substr(0, 10)
        : null
    var g4 =
      !(item.readonly || item.disabled) &&
      !(item.type == "editor") &&
      !(item.type == "city") &&
      (item.type == "date" ||
        item.type == "datetime" ||
        item.type == "month") &&
      item.range &&
      item.type == "date"
        ? (_vm.inFormFields[item.field][1] || "").substr(0, 10)
        : null
    var g5 =
      !(item.readonly || item.disabled) &&
      !(item.type == "editor") &&
      !(item.type == "city") &&
      (item.type == "date" ||
        item.type == "datetime" ||
        item.type == "month") &&
      !item.range &&
      item.type == "date"
        ? (_vm.inFormFields[item.field] || "").substr(0, 10)
        : null
    var g6 =
      !(item.readonly || item.disabled) &&
      !(item.type == "editor") &&
      !(item.type == "city") &&
      (item.type == "date" ||
        item.type == "datetime" ||
        item.type == "month") &&
      !item.range &&
      !(item.type == "date") &&
      item.type == "month"
        ? (_vm.inFormFields[item.field] || "").substr(0, 7)
        : null
    var g7 =
      !(item.readonly || item.disabled) &&
      !(item.type == "editor") &&
      !(item.type == "city") &&
      !(
        item.type == "date" ||
        item.type == "datetime" ||
        item.type == "month"
      ) &&
      !item.range
        ? ["select", "selectList", "checkbox", "cascader"].indexOf(item.type)
        : null
    var g8 =
      !(item.readonly || item.disabled) &&
      !(item.type == "editor") &&
      !(item.type == "city") &&
      !(
        item.type == "date" ||
        item.type == "datetime" ||
        item.type == "month"
      ) &&
      !item.range &&
      g7 != -1
        ? _vm.base.isEmpty(_vm.inFormFields[item.field], true)
        : null
    var g9 =
      !(item.readonly || item.disabled) &&
      !(item.type == "editor") &&
      !(item.type == "city") &&
      !(
        item.type == "date" ||
        item.type == "datetime" ||
        item.type == "month"
      ) &&
      !item.range &&
      g7 != -1
        ? _vm.base.isEmpty(_vm.inFormFields[item.field], true)
        : null
    var m1 =
      !(item.readonly || item.disabled) &&
      !(item.type == "editor") &&
      !(item.type == "city") &&
      !(
        item.type == "date" ||
        item.type == "datetime" ||
        item.type == "month"
      ) &&
      !item.range &&
      g7 != -1
        ? _vm.formatDicValue(item)
        : null
    var l1 =
      !(item.readonly || item.disabled) &&
      !(item.type == "editor") &&
      !(item.type == "city") &&
      !(
        item.type == "date" ||
        item.type == "datetime" ||
        item.type == "month"
      ) &&
      !item.range &&
      !(g7 != -1) &&
      !(item.type == "group") &&
      !(item.type == "number") &&
      !(item.type == "decimal") &&
      item.type == "radio"
        ? _vm.__map(item.data, function (option, opIndex) {
            var $orig = _vm.__get_orig(option)
            var a0 = {
              "margin-bottom": item.placement == "column" ? "30rpx" : 0,
              "margin-right": item.placement == "column" ? "0" : "30rpx",
            }
            return {
              $orig: $orig,
              a0: a0,
            }
          })
        : null
    var a1 =
      !(item.readonly || item.disabled) &&
      !(item.type == "editor") &&
      !(item.type == "city") &&
      !(
        item.type == "date" ||
        item.type == "datetime" ||
        item.type == "month"
      ) &&
      !item.range &&
      !(g7 != -1) &&
      !(item.type == "group") &&
      !(item.type == "number") &&
      !(item.type == "decimal") &&
      !(item.type == "radio") &&
      item.type == "switch"
        ? {
            "margin-right": "40rpx",
          }
        : null
    return {
      $orig: $orig,
      l0: l0,
      m0: m0,
      g0: g0,
      g1: g1,
      g2: g2,
      g3: g3,
      g4: g4,
      g5: g5,
      g6: g6,
      g7: g7,
      g8: g8,
      g9: g9,
      m1: m1,
      l1: l1,
      a1: a1,
    }
  })
  var l3 = _vm.__map(_vm.actionSheetCurrentItem.data, function (item, index) {
    var $orig = _vm.__get_orig(item)
    var g10 =
      !item.hidden &&
      (!_vm.searchText || item.value.indexOf(_vm.searchText) != -1)
    var m2 = _vm.actionSheetModel && _vm.isActionSelected(item)
    return {
      $orig: $orig,
      g10: g10,
      m2: m2,
    }
  })
  if (!_vm._isMounted) {
    _vm.e0 = function ($event, item, imgIndex) {
      var _temp = arguments[arguments.length - 1].currentTarget.dataset,
        _temp2 = _temp.eventParams || _temp["event-params"],
        item = _temp2.item,
        imgIndex = _temp2.imgIndex
      var _temp, _temp2
      return _vm.previewImage(item, imgIndex)
    }
    _vm.e1 = function ($event) {
      _vm.actionSheetModel = false
    }
    _vm.e2 = function ($event) {
      _vm.searchText = ""
    }
    _vm.e3 = _vm.actionSascaderCurrentItem.cancel
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        l2: l2,
        l3: l3,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 300:
/*!**********************************************************************************************************************!*\
  !*** D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/components/vol-form/vol-form.vue?vue&type=script&lang=js& ***!
  \**********************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_vol_form_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vol-form.vue?vue&type=script&lang=js& */ 301);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_vol_form_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_vol_form_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_vol_form_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_vol_form_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_vol_form_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 301:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/components/vol-form/vol-form.vue?vue&type=script&lang=js& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 55));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 57));
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var lotusAddress = function lotusAddress() {
  Promise.all(/*! require.ensure | components/Winglau14-lotusAddress/Winglau14-lotusAddress */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/Winglau14-lotusAddress/Winglau14-lotusAddress")]).then((function () {
    return resolve(__webpack_require__(/*! ./../Winglau14-lotusAddress/Winglau14-lotusAddress.vue */ 421));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default2 = {
  components: {
    lotusAddress: lotusAddress
  },
  props: {
    formOptions: {
      type: Array,
      default: function _default() {
        return [];
      }
    },
    formFields: {
      type: Object,
      default: function _default() {
        return {};
      }
    },
    padding: {
      type: Number,
      default: 30
    },
    labelWidth: {
      type: Number,
      default: 150
    },
    labelPosition: {
      type: String,
      default: 'left'
    },
    loadKey: {
      type: Boolean,
      default: true
    },
    uploadBefore: {
      type: Function,
      default: function _default(files) {
        return true;
      }
    }
  },
  name: "vol-form",
  data: function data() {
    return {
      lotusAddressData: {
        visible: false,
        provinceName: '',
        cityName: '',
        townName: ''
      },
      cityItem: {
        field: ""
      },
      region: '',
      showFilter: false,
      searchText: '',
      //搜索的内容
      inFormFields: {},
      inFormOptions: [],
      maxHeight: 400,
      popupHeight: 0,
      pickerValue: '',
      pickerModel: false,
      //日期组件
      pickerCurrentItem: {},
      //当前选项
      pickerCurrentRangeIndex: 0,
      actionSheetModel: false,
      actionSascaderCurrentItem: {
        title: "",
        field: '',
        checkStrictly: false,
        //是否只能选择最后一个节点
        cancel: function cancel() {},
        confirm: function confirm() {},
        data: []
      },
      actionSheetCurrentItem: {
        min: 633715200000,
        max: 0
      },
      //当前选项
      actionSheetSelectValues: [],
      //当前选中的项
      numberModel: false,
      numberType: 'number',
      numberCurrentItem: {},
      imgFields: []
    };
  },
  created: function created() {
    var _this2 = this;
    //日期最小最大值转换
    this.formOptions.forEach(function (option) {
      if (option.type == 'date' || option.type == 'datetime' || option.type == 'month') {
        if (!option.min) {
          option.min = Number(new Date('1990/01/01 00:00:00')); //
        } else if (typeof option.min == 'string') {
          if (option.type == 'month' && option.min.length != 7) {
            option.min = option.min.substring(0, 7);
          }
          option.min = Number(new Date(option.min.replace(/-/g, "/")));
        }
        if (!option.max) {
          option.max = Number(new Date(new Date().getFullYear() + 10 + '/01/01 00:00:00')); //
        } else if (option.max && typeof option.max == 'string') {
          option.max = Number(new Date(option.max.replace(/-/g, "/")));
        }
        if (!_this2.pickerCurrentItem.max) {
          _this2.pickerCurrentItem.max = option.max;
        }
      }
      if (option.hasOwnProperty('focus')) {
        option.focus = false;
      }
    });
    this.inFormOptions = this.formOptions;
    this.inFormFields = this.formFields;
    this.imgFields = this.inFormOptions.filter(function (x) {
      return x.type == 'img';
    }).map(function (x) {
      return x.field;
    });
    if (this.imgFields.length) {
      this.convertImgArr(this.formFields);
    } else {
      this.imgFields = null;
    }
    if (!this.loadKey) {
      return;
    }
    var dicKeys = this.formOptions.filter(function (x) {
      return x.key || x.dataKey;
    }).map(function (m) {
      return m.key || m.dataKey;
    });
    if (!dicKeys.length) {
      return;
    }
    this.http.post('api/Sys_Dictionary/GetVueDictionary', dicKeys, true).then(function (result) {
      _this2.initDataSource(result);
    });
  },
  mounted: function mounted() {
    var _this = this;
    uni.getSystemInfo({
      success: function success(res) {
        _this.maxHeight = res.screenHeight * 0.85;
      }
    });
  },
  methods: {
    inputConfirm: function inputConfirm(field, e) {
      this.$emit('input-confirm', field, e);
    },
    convertImgArr: function convertImgArr(formFields) {
      var _this3 = this;
      if (!this.imgFields) {
        return;
      }
      for (var i = 0; i < this.imgFields.length; i++) {
        var field = this.imgFields[i];
        if (!Array.isArray(formFields[field])) {
          if (this.base.isEmpty(formFields[field])) {
            formFields[field] = [];
          } else {
            formFields[field] = formFields[field].split(',').map(function (x) {
              return {
                url: _this3.http.ipAddress + x,
                orginUrl: x //原图
              };
            });
          }
        }
      }
    },
    initDataSource: function initDataSource(result) {
      var _this4 = this;
      result.forEach(function (res) {
        _this4.inFormOptions.forEach(function (option) {
          if ((option.key || option.dataKey) == res.dicNo) {
            option.data = res.data;
          }
        });
      });
      this.$emit('dicInited', result);
    },
    cascaderConfirm: function cascaderConfirm(value, item) {
      this.inFormFields[this.actionSascaderCurrentItem.field] = value;
      this.$emit("onChange", this.actionSascaderCurrentItem.field, value, item);
    },
    showActionSheet: function showActionSheet(item) {
      if (item.type == 'cascader') {
        var _this$actionSascaderC;
        this.actionSascaderCurrentItem.field = item.field;
        this.actionSascaderCurrentItem.data.splice(0);
        this.actionSascaderCurrentItem.checkStrictly = item.checkStrictly || false; //是否只能选择最后一个节点
        (_this$actionSascaderC = this.actionSascaderCurrentItem.data).push.apply(_this$actionSascaderC, (0, _toConsumableArray2.default)(item.data));
        this.$refs.cascader.show(this.inFormFields[item.field]);
        //this.actionSascaderCurrentItem.cancel = item.cancel;
        //this.actionSascaderCurrentItem.confirm = item.confirm;
        return;
      }
      this.searchText = '';
      this.actionSheetSelectValues = [];
      this.actionSheetCurrentItem = item;
      var value = this.inFormFields[item.field];
      if (!this.base.isEmpty(value, true)) {
        if (Array.isArray(value)) {
          var _this$actionSheetSele;
          (_this$actionSheetSele = this.actionSheetSelectValues).push.apply(_this$actionSheetSele, (0, _toConsumableArray2.default)(value.map(function (x) {
            return x;
          })));
        } else if (this.isMultiSelect()) {
          this.actionSheetSelectValues = value.split(',');
        } else {
          this.actionSheetSelectValues.push(value);
        }
      }
      this.showFilter = item.data.length > 15;
      var height = (item.data.length + 1 + (this.showFilter ? 1 : 0)) * 50;
      this.popupHeight = height > this.maxHeight ? this.maxHeight : height;
      this.actionSheetModel = true;
    },
    actionClick: function actionClick(item) {
      //多选
      if (this.isMultiSelect()) {
        //已经选中过的再次点取消选选中
        if (this.isActionSelected(item)) {
          this.actionSheetSelectValues = this.actionSheetSelectValues.filter(function (x) {
            return x + '' !== item.key + '';
          });
        } else {
          this.actionSheetSelectValues.push(item.key);
        }
        return;
      }
      this.inFormFields[this.actionSheetCurrentItem.field] = item.key;
      this.actionSheetModel = false;
      this.$emit("onChange", this.actionSheetCurrentItem.field, this.inFormFields[this.actionSheetCurrentItem.field], item);
    },
    isMultiSelect: function isMultiSelect(item) {
      var type;
      if (item) {
        type = item.type;
      } else {
        type = this.actionSheetCurrentItem.type;
      }
      if (!type) {
        return false;
      }
      return ['checkbox', 'selectList'].indexOf(type) != -1;
    },
    actionConfirmClick: function actionConfirmClick(item) {
      //单选
      if (!this.isMultiSelect()) {
        this.actionSheetModel = false;
        //	return this.actionClick(item)
      }
      //多选
      if (Array.isArray(this.inFormFields[this.actionSheetCurrentItem.field])) {
        //深复制原来的数据
        this.inFormFields[this.actionSheetCurrentItem.field] = this.actionSheetSelectValues.map(function (x) {
          return x;
        });
      } else {
        this.inFormFields[this.actionSheetCurrentItem.field] = this.actionSheetSelectValues.join(',');
      }
      this.actionSheetModel = false;
    },
    isActionSelected: function isActionSelected(item) {
      var isSelect = this.actionSheetSelectValues.some(function (x) {
        return x + '' === item.key + '';
      });
      //this.formFields[item.field]
      return isSelect;
    },
    formatDicValueList: function formatDicValueList(item) {
      //多选
      var value = this.inFormFields[item.field];
      if (this.base.isEmpty(value)) {
        return '';
      }
      var _textArr = [];
      if (!Array.isArray(value)) {
        value = (value + '').split(',');
      }
      value.forEach(function (x) {
        var obj = item.data.find(function (c) {
          return x + '' === c.key + '';
        });
        if (obj) {
          _textArr.push(obj.value);
        } else {
          _textArr.push(x);
        }
      });
      return _textArr.join(",");
    },
    getAllParentId: function getAllParentId(id, data) {
      if (id === null || id === '' || id === undefined) {
        return [];
      }
      if (data.some(function (x) {
        return typeof x.id == 'string';
      })) {
        id = id + '';
      } else {
        id = id * 1;
      }
      var ids = [id];
      var _loop = function _loop(index) {
        node = data.find(function (x) {
          return x.id === ids[index];
        });
        if (!node || node.parentId === null && node.parentId === undefined) {
          return {
            v: ids
          };
        }
        if (data.some(function (x) {
          return x.id === node.parentId;
        })) {
          ids.push(node.parentId);
        }
      };
      for (var index = 0; index < ids.length; index++) {
        var node;
        var _ret = _loop(index);
        if ((0, _typeof2.default)(_ret) === "object") return _ret.v;
      }
      return ids.reverse();
    },
    getCascaderNames: function getCascaderNames(value, item) {
      var ids = this.getAllParentId(value, item.data);
      var names = [];
      var _loop2 = function _loop2(i) {
        var obj = item.data.find(function (x) {
          return x.id === ids[i];
        });
        if (obj) {
          names.push(obj.value || obj.name);
        } else {
          names.push(ids[i]);
        }
      };
      for (var i = 0; i < ids.length; i++) {
        _loop2(i);
      }
      return names.join('/');
    },
    formatDicValue: function formatDicValue(item) {
      var value = this.inFormFields[item.field];
      if (this.base.isEmpty(value)) {
        return '';
      }
      if (item.type == 'cascader') {
        return this.getCascaderNames(value, item);
      }
      if (this.isMultiSelect(item)) {
        return this.formatDicValueList(item);
      }
      var _kv = item.data.find(function (x) {
        return x.key + '' == value + '';
      });
      if (!_kv) {
        return value;
      }
      return _kv.value;
    },
    showPicker: function showPicker(item, index) {
      this.pickerCurrentItem = item;
      var val = this.inFormFields[this.pickerCurrentItem.field];
      if (item.range) {
        this.pickerCurrentRangeIndex = index;
        if (!Array.isArray(val)) {
          this.inFormFields[this.pickerCurrentItem.field] = ['', ''];
          val = ['', ''];
        }
        val = val[index];
      }
      if (!val) {
        if (item.type == 'date') {
          val = this.base.getDate();
        } else if (item.type == 'month') {
          val = this.base.getDate();
        } else {
          val = this.base.getDateTime().substring(0, 16);
        }
      }
      this.pickerValue = Number(new Date(val.replace(/-/g, "/")));
      this.pickerModel = true;
      this.hideKeyboard();
    },
    setPickerValue: function setPickerValue(value) {
      if (this.pickerCurrentItem.range) {
        this.inFormFields[this.pickerCurrentItem.field][this.pickerCurrentRangeIndex] = value;
      } else {
        this.inFormFields[this.pickerCurrentItem.field] = value;
      }
      this.$emit("onChange", this.pickerCurrentItem.field, value);
    },
    pickerConfirm: function pickerConfirm(e) {
      this.pickerModel = false;
      if (this.pickerCurrentItem.range && this.pickerCurrentRangeIndex == 1) {
        //判断结束时间大于开始时间
      }
      if (typeof e.value == 'number') {
        var timeFormat = this.pickerCurrentItem.type == 'date' ? 'yyyy-mm-dd' : 'yyyy-mm-dd hh:MM';
        this.setPickerValue(uni.$u.timeFormat(e.value, timeFormat));
      } else {
        this.setPickerValue(uni.$u.timeFormat(e.value));
      }
    },
    pickerClose: function pickerClose() {
      this.pickerModel = false;
    },
    hideKeyboard: function hideKeyboard() {
      uni.hideKeyboard();
    },
    reset: function reset(source) {
      var _this5 = this;
      var _loop3 = function _loop3(key) {
        if (source && source.hasOwnProperty(key)) {
          _this5.inFormFields[key] = source[key];
        } else {
          if (Array.isArray(_this5.inFormFields[key])) {
            _this5.inFormFields[key].splice(0);
            if (_this5.inFormOptions.some(function (x) {
              return x.field == key && x.range;
            })) {
              var _this5$inFormFields$k;
              (_this5$inFormFields$k = _this5.inFormFields[key]).push.apply(_this5$inFormFields$k, ['', '']);
            }
          } else {
            _this5.inFormFields[key] = "";
          }
        }
      };
      for (var key in this.inFormFields) {
        _loop3(key);
      }
    },
    validate: function validate() {
      var _this6 = this;
      var _option = this.inFormOptions.filter(function (c) {
        return c.require || c.required;
      }).find(function (x) {
        var val = _this6.inFormFields[x.field];
        if (Array.isArray(val)) {
          return !val.length;
        } else {
          return _this6.base.isEmpty(val);
        }
        return;
      });
      if (_option) {
        if (['date', 'datetime', 'month', 'checkbox', 'select', 'selectList', 'radio', 'switch'].indexOf(_option.type) != -1) {
          this.$toast('请选择' + _option.title);
        } else {
          this.$toast(_option.title + '不能为空');
        }
        return false;
      }
      return true;
    },
    showNumber: function showNumber(item) {
      this.numberCurrentItem = item;
      this.numberModel = true;
    },
    numberBackspace: function numberBackspace() {
      var value = this.inFormFields[this.numberCurrentItem.field];
      if (value) {
        value = value + '';
        this.inFormFields[this.numberCurrentItem.field] = value.substr(0, value - 1);
      }
    },
    numberChange: function numberChange(val) {
      var _val = this.inFormFields[this.numberCurrentItem.field];
      if (this.base.isEmpty(_val)) {
        _val = '';
      } else {
        _val = _val + '';
      }
      if (val == '.' && _val.indexOf('.') != -1) {
        return;
      }
      this.inFormFields[this.numberCurrentItem.field] = _val + val;
    },
    formatReadonlyValue: function formatReadonlyValue(item) {
      if (item.data) {
        return this.formatDicValue(item);
      }
      if (item.type == 'date') {
        return (this.inFormFields[item.field] || '').substr(0, 10);
      }
      return this.inFormFields[item.field] || '';
    },
    getImgSrcs: function getImgSrcs(item) {
      var imgs = this.inFormFields[item.field];
      if (!imgs) {
        return [];
      }
      if (Array.isArray(imgs)) {
        return imgs;
      }
      var imgArr = imgs.split(',');
      return imgArr.filter(function (x) {
        return x;
      }).map(function (m) {
        //return this.http.ipAddress+'m'
        return m;
      });
      //this.http.ipAddress
    },
    afterRead: function afterRead(option, event) {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var lists, _lists, fileListLen, i, result, item, fileName, lastIndex;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                if (option.url) {
                  _context.next = 2;
                  break;
                }
                return _context.abrupt("return", _this7.$toast('未配置好url'));
              case 2:
                // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
                lists = [];
                if (option.multiple) {
                  lists = [].concat(event.file);
                } else {
                  if (Array.isArray(event.file)) {
                    (_lists = lists).push.apply(_lists, (0, _toConsumableArray2.default)(event.file));
                  } else {
                    lists.push(event.file);
                  }
                }
                fileListLen = _this7.inFormFields[option.field].length;
                lists.map(function (item) {
                  _this7.inFormFields[option.field].push(_objectSpread(_objectSpread({}, item), {}, {
                    status: 'uploading',
                    message: '上传中'
                  }));
                });
                // if (!this.uploadBefore(lists, option)) {
                // 	return;
                // }
                //this.$emit('uploadBefore', lists, option, async () => {
                i = 0;
              case 7:
                if (!(i < lists.length)) {
                  _context.next = 19;
                  break;
                }
                _context.next = 10;
                return _this7.uploadFilePromise(lists[i].url, option.url);
              case 10:
                result = _context.sent;
                item = _this7.inFormFields[option.field][fileListLen];
                fileName = lists[i].name;
                if (!fileName && lists[i].thumb) {
                  lastIndex = lists[i].thumb.lastIndexOf('/') + 1; // let arr = lists[i].thumb.substr(0,lastIndex);
                  // let _obj = arr[0].split('/');
                  fileName = lists[i].thumb.substr(lastIndex);
                }
                _this7.inFormFields[option.field].splice(fileListLen, 1, Object.assign(item, {
                  status: 'success',
                  message: '',
                  url: _this7.http.ipAddress + result + fileName,
                  orginUrl: result + fileName
                }));
                fileListLen++;
              case 16:
                i++;
                _context.next = 7;
                break;
              case 19:
              case "end":
                return _context.stop();
            }
          }
        }, _callee);
      }))();
    },
    uploadFilePromise: function uploadFilePromise(url, apiUrl) {
      var _this8 = this;
      return new Promise(function (resolve, reject) {
        var a = uni.uploadFile({
          url: _this8.http.ipAddress + apiUrl,
          // 仅为示例，非真实的接口地址
          filePath: url,
          name: 'fileInput',
          header: {
            "uapp": 1,
            "Authorization": _this8.$store.getters.getToken()
          },
          formData: {},
          success: function success(res) {
            setTimeout(function () {
              resolve(JSON.parse(res.data).data);
            }, 500);
          },
          fail: function fail(res) {
            this.$toast('上传失败');
            //console.log(res)
          }
        });
      });
    },
    // 删除图片
    deletePic: function deletePic(item, event) {
      this.inFormFields[item.field].splice(event.index, 1);
    },
    extraClick: function extraClick(item, inFormFields) {
      this.$emit('extraClick', item, inFormFields);
    },
    showCascaderSheet: function showCascaderSheet(item) {
      this.$refs[item.field][0].show();
    },
    onCitySelect: function onCitySelect(res) {
      //res数据源包括已选省市区与省市区code
      console.log(res);
      this.lotusAddressData.visible = res.visible; //visible为显示与关闭组件标识true显示false隐藏
      //res.isChose = 1省市区已选 res.isChose = 0;未选
      if (res.isChose) {
        this.lotusAddressData.provinceName = res.province; //省
        this.lotusAddressData.cityName = res.city; //市
        this.lotusAddressData.townName = res.town; //区
        this.inFormFields[this.cityItem.field] = res.province + ',' + res.city + ',' + res.town;
        //this.region = `${res.province}${res.city}${res.town}`; //region为已选的省市区的值
      }
    },
    showCitySheet: function showCitySheet(item) {
      this.cityItem = item;
      var arr = (this.inFormFields[item.field] || '').split(',');
      this.lotusAddressData.provinceName = arr[0] || ''; //省
      this.lotusAddressData.cityName = arr[1] || ''; //市
      this.lotusAddressData.townName = arr[2] || ''; //区
      this.lotusAddressData.visible = true;
    },
    previewImage: function previewImage(item, imgIndex) {
      var imgs = this.getImgSrcs(item).map(function (x) {
        return x.url;
      });
      uni.previewImage({
        urls: imgs,
        current: imgIndex
      });
    },
    radioOnChange: function radioOnChange(value, item) {
      this.$emit("onChange", item.field, value, item, item.data);
      //@change="(val)=>{radioOnChange(val,item)}" :placement="item.placement"
    }
  },

  // 小程序不要使用循环生成表单,否则这里会死循环,与初始化的时候设置默认值有关,后面再处理
  watch: {
    inFormFields: {
      handler: function handler(val) {
        if (!val || !Object.keys(val).length) {
          return;
        }
        console.log('inFormFields');
        this.$emit('update:form-fields', val);
        //console.log("wc")
      },

      immediate: true,
      deep: true
    },
    formFields: {
      handler: function handler(val) {
        // console.log('formFields')
        this.convertImgArr(val);
        this.inFormFields = val;
      },
      immediate: true,
      deep: true
    },
    inFormOptions: {
      handler: function handler(newValue, oldValue) {
        if (!newValue || !newValue.length) {
          return;
        }
        this.convertImgArr(newValue);
        this.$emit('update:formOptions', newValue);
      },
      immediate: true,
      deep: true
    },
    formOptions: {
      handler: function handler(newValue, oldValue) {
        console.log('formOptions');
        this.inOptions = newValue;
      },
      immediate: true,
      deep: true
    }
  }
};
exports.default = _default2;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 302:
/*!*******************************************************************************************************************************************************!*\
  !*** D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/components/vol-form/vol-form.vue?vue&type=style&index=0&id=6726b7c4&lang=less&scoped=true& ***!
  \*******************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_vol_form_vue_vue_type_style_index_0_id_6726b7c4_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--10-oneOf-1-2!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--10-oneOf-1-3!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--10-oneOf-1-5!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vol-form.vue?vue&type=style&index=0&id=6726b7c4&lang=less&scoped=true& */ 303);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_vol_form_vue_vue_type_style_index_0_id_6726b7c4_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_vol_form_vue_vue_type_style_index_0_id_6726b7c4_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_vol_form_vue_vue_type_style_index_0_id_6726b7c4_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_vol_form_vue_vue_type_style_index_0_id_6726b7c4_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_vol_form_vue_vue_type_style_index_0_id_6726b7c4_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 303:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--10-oneOf-1-2!./node_modules/postcss-loader/src??ref--10-oneOf-1-3!./node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--10-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/components/vol-form/vol-form.vue?vue&type=style&index=0&id=6726b7c4&lang=less&scoped=true& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

}]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/vol-form/vol-form.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/vol-form/vol-form-create-component',
    {
        'components/vol-form/vol-form-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(297))
        })
    },
    [['components/vol-form/vol-form-create-component']]
]);
