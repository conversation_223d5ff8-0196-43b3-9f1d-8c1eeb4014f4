@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  flex: 1;
  background-color: #f5f5f5;
  background-image: url(/static/img/bg.6fc1f75f.svg);
  background-size: cover;
  background-position: center;
}
.content {
  padding: 30rpx;
  flex: 1;
  padding-bottom: 150rpx;
  /* 为底部按钮留出空间 */
}
/* 详情卡片样式 */
.detail-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
/* 会议室头部信息 */
.room-header {
  padding: 30rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}
.room-icon {
  width: 120rpx;
  height: 120rpx;
  position: relative;
  margin-right: 30rpx;
}
.status-dot {
  position: absolute;
  top: 0;
  left: 0;
  width: 20rpx;
  height: 20rpx;
  border-radius: 10rpx;
  background-color: #cccccc;
}
.status-dot.active {
  background-color: #8B0000;
}
.icon-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  background-color: #8B0000;
}
.room-title {
  flex: 1;
}
.room-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}
.room-status {
  font-size: 24rpx;
  color: #8B0000;
  background-color: rgba(139, 0, 0, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}
/* 信息部分样式 */
.info-section {
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}
.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.info-list {
  display: flex;
  flex-direction: column;
}
.info-item {
  display: flex;
  flex-direction: row;
  margin-bottom: 15rpx;
  align-items: center;
}
.info-label {
  width: 150rpx;
  font-size: 28rpx;
  color: #999;
}
.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
/* 绑定状态部分 */
.binding-section {
  padding: 30rpx;
}
.binding-status {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}
.binding-status.bound {
  background-color: rgba(139, 0, 0, 0.1);
}
.binding-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}
.binding-text {
  font-size: 28rpx;
  color: #666;
}
.binding-status.bound .binding-text {
  color: #8B0000;
}
/* 底部按钮样式 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: transparent;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: center;
}
.button-group {
  display: flex;
  flex-direction: row;
  width: 100%;
  justify-content: space-between;
}
.action-button {
  height: 90rpx;
  border-radius: 45rpx;
  justify-content: center;
  align-items: center;
  display: flex;
  padding: 0 40rpx;
}
.action-button text {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: bold;
}
.action-button.edit {
  background-color: #8B0000;
  /* 深红色 */
  box-shadow: 0 4rpx 10rpx rgba(139, 0, 0, 0.3);
}
.action-button.bind {
  background-color: #8B0000;
  /* 深红色 */
  box-shadow: 0 4rpx 10rpx rgba(139, 0, 0, 0.3);
}
.action-button.unbind {
  background-color: #666;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
}
.action-button.delete {
  background-color: #ff6b6b;
  box-shadow: 0 4rpx 10rpx rgba(255, 107, 107, 0.3);
}
