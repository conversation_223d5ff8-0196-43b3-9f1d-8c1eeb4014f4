@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  flex: 1;
  background-color: #f5f5f5;
  background-image: url(/static/img/bg.6fc1f75f.svg);
  background-size: cover;
  background-position: center;
}
.content {
  padding: 30rpx;
  flex: 1;
  padding-bottom: 150rpx;
  /* 为底部按钮留出空间 */
}
/* 编辑表单样式 */
.edit-form {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.form-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}
.form-item {
  margin-bottom: 30rpx;
}
.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}
.form-input {
  height: 80rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: #f9f9f9;
}
/* 状态选择器样式 */
.status-selector {
  display: flex;
  flex-direction: row;
}
.status-option {
  flex: 1;
  height: 80rpx;
  border: 1px solid #ddd;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f9f9f9;
}
.status-option:first-child {
  border-top-left-radius: 8rpx;
  border-bottom-left-radius: 8rpx;
}
.status-option:last-child {
  border-top-right-radius: 8rpx;
  border-bottom-right-radius: 8rpx;
  border-left: none;
}
.status-option.active {
  background-color: #8B0000;
}
.status-option.active text {
  color: #ffffff;
}
.status-option text {
  font-size: 28rpx;
  color: #333;
}
/* 底部按钮样式 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: transparent;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: center;
}
.button-group {
  display: flex;
  flex-direction: row;
  width: 100%;
}
.cancel-button, .save-button {
  height: 90rpx;
  border-radius: 45rpx;
  justify-content: center;
  align-items: center;
  display: flex;
  flex: 1;
  margin: 0 15rpx;
}
.cancel-button {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
}
.cancel-button text {
  color: #666;
  font-size: 32rpx;
}
.save-button {
  background-color: #8B0000;
  /* 深红色 */
  box-shadow: 0 4rpx 10rpx rgba(139, 0, 0, 0.3);
}
.save-button text {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
}
