<view class="container"><view class="content"><view class="detail-card"><view class="room-header"><view class="room-icon"><view class="{{['status-dot',(roomInfo.status==='active')?'active':'']}}"></view><image class="icon-image" src="/static/images/logo_icon.png" mode="aspectFit"></image></view><view class="room-title"><text class="room-name">{{roomInfo.name}}</text><text class="room-status">{{roomInfo.status==='active'?'已启用':'已禁用'}}</text></view></view><view class="info-section"><view class="info-title"><text>设备信息</text></view><view class="info-list"><view class="info-item"><text class="info-label">型号</text><text class="info-value">{{roomInfo.model||'未绑定'}}</text></view><view class="info-item"><text class="info-label">版本</text><text class="info-value">{{roomInfo.version||'未绑定'}}</text></view><view class="info-item"><text class="info-label">类型</text><text class="info-value">{{roomInfo.type||'未绑定'}}</text></view><view class="info-item"><text class="info-label">编号</text><text class="info-value">{{roomInfo.code||'未绑定'}}</text></view><view class="info-item"><text class="info-label">频道</text><text class="info-value">{{roomInfo.channel||'未绑定'}}</text></view><view class="info-item"><text class="info-label">创建时间</text><text class="info-value">{{roomInfo.createTime||'未知'}}</text></view><view class="info-item"><text class="info-label">最后更新</text><text class="info-value">{{roomInfo.updateTime||'未知'}}</text></view></view></view><view class="binding-section"><view class="{{['binding-status',(roomInfo.isBound)?'bound':'']}}"><image class="binding-icon" src="{{roomInfo.isBound?'/static/images/gateway_binding.png':'/static/images/gateway_unbinding.png'}}" mode="aspectFit"></image><text class="binding-text">{{roomInfo.isBound?'已绑定设备':'未绑定设备'}}</text></view></view></view></view><view class="footer"><view class="button-group"><view data-event-opts="{{[['tap',[['editRoom',['$event']]]]]}}" class="action-button edit" bindtap="__e"><text>编辑</text></view><block wx:if="{{!roomInfo.isBound}}"><view data-event-opts="{{[['tap',[['bindDevice',['$event']]]]]}}" class="action-button bind" bindtap="__e"><text>绑定设备</text></view></block><block wx:if="{{roomInfo.isBound}}"><view data-event-opts="{{[['tap',[['unbindDevice',['$event']]]]]}}" class="action-button unbind" bindtap="__e"><text>解绑设备</text></view></block><view data-event-opts="{{[['tap',[['deleteRoom',['$event']]]]]}}" class="action-button delete" bindtap="__e"><text>删除</text></view></view></view></view>