
.container.data-v-4e10ad7b {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-image: url('http://14.103.146.84:8890/i/2025/06/12/layout_ornament.png');
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;
	box-sizing: border-box;
	overflow: hidden;
}

/* 头部样式 */
.header.data-v-4e10ad7b {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	height: 60px;
	padding: 0 20px;
	box-sizing: border-box;
	position: relative;
	z-index: 10;
}
.back-btn.data-v-4e10ad7b {
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background-color: rgba(255, 255, 255, 0.2);
	border: 1px solid rgba(139, 69, 19, 0.3);
}
.back-icon.data-v-4e10ad7b {
	font-size: 20px;
	color: #2C1810;
	font-weight: bold;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}
.header-title.data-v-4e10ad7b {
	color: #2C1810;
	font-size: 20px;
	font-weight: 900;
	letter-spacing: 2px;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 1px 1px 2px rgba(139,69,19,0.4);
	text-align: center;
}
.header-placeholder.data-v-4e10ad7b {
	width: 40px;
}

/* Logo样式 */
.logo-container.data-v-4e10ad7b {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 10px;
	margin-bottom: 15px;
}
.logo.data-v-4e10ad7b {
	width: 60px;
	height: 60px;
}

/* 内容容器样式 */
.content-container.data-v-4e10ad7b {
	flex: 1;
	padding: 0 20px 20px 20px;
	box-sizing: border-box;
	overflow: hidden;
}
.agreement-scroll.data-v-4e10ad7b {
	height: 100%;
	width: 100%;
}
.agreement-content.data-v-4e10ad7b {
	padding: 20px;
	background-color: rgba(255, 255, 255, 0.95);
	border-radius: 15px;
	border: 2px solid rgba(139, 69, 19, 0.3);
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
	margin-bottom: 20px;
	-webkit-clip-path: polygon(
		2% 0%, 
		98% 0%, 
		100% 3%, 
		100% 97%, 
		98% 100%, 
		2% 100%, 
		0% 97%, 
		0% 3%
	);
	        clip-path: polygon(
		2% 0%, 
		98% 0%, 
		100% 3%, 
		100% 97%, 
		98% 100%, 
		2% 100%, 
		0% 97%, 
		0% 3%
	);
}

/* 协议标题样式 */
.agreement-title.data-v-4e10ad7b {
	text-align: center;
	margin-bottom: 25px;
	padding-bottom: 15px;
	border-bottom: 2px solid rgba(139, 69, 19, 0.3);
}
.title-text.data-v-4e10ad7b {
	color: #2C1810;
	font-size: 28px;
	font-weight: 900;
	letter-spacing: 4px;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 1px 1px 2px rgba(139,69,19,0.4);
}

/* 协议文本样式 */
.agreement-text.data-v-4e10ad7b {
	line-height: 1.8;
}
.content-text.data-v-4e10ad7b {
	color: #2C1810;
	font-size: 14px;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	line-height: 1.8;
	text-align: justify;
	word-break: break-word;
	white-space: pre-line;
}

/* 段落内容样式 */
.section-content.data-v-4e10ad7b {
	margin-bottom: 16px;
	padding: 12px 0;
}
.paragraph-text.data-v-4e10ad7b {
	color: #2C1810;
	font-size: 14px;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	line-height: 1.8;
	text-align: justify;
	word-break: break-word;
	display: block;
}

/* 章节样式 */
.chapter-section.data-v-4e10ad7b {
	margin-bottom: 24px;
	padding: 16px 0;
}
.chapter-title.data-v-4e10ad7b {
	margin-bottom: 16px;
	padding-bottom: 8px;
	border-bottom: 1px solid rgba(139, 69, 19, 0.2);
}
.chapter-text.data-v-4e10ad7b {
	color: #2C1810;
	font-size: 18px;
	font-weight: 800;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
	letter-spacing: 1px;
}

/* 条款项目样式 */
.article-item.data-v-4e10ad7b {
	margin-bottom: 12px;
	padding: 8px 0;
	padding-left: 16px;
	position: relative;
}
.article-item.data-v-4e10ad7b::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	-webkit-transform: translateY(-50%);
	        transform: translateY(-50%);
	width: 4px;
	height: 4px;
	background-color: rgba(139, 69, 19, 0.4);
	border-radius: 50%;
}
.article-text.data-v-4e10ad7b {
	color: #2C1810;
	font-size: 14px;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	line-height: 1.8;
	text-align: justify;
	word-break: break-word;
	display: block;
}

/* 滚动条样式 */
.data-v-4e10ad7b::-webkit-scrollbar {
	width: 6px;
}
.data-v-4e10ad7b::-webkit-scrollbar-track {
	background: rgba(139, 69, 19, 0.1);
	border-radius: 3px;
}
.data-v-4e10ad7b::-webkit-scrollbar-thumb {
	background: rgba(139, 69, 19, 0.4);
	border-radius: 3px;
}
.data-v-4e10ad7b::-webkit-scrollbar-thumb:hover {
	background: rgba(139, 69, 19, 0.6);
}

/* 回到顶部按钮样式 */
.back-to-top.data-v-4e10ad7b {
	position: fixed;
	bottom: 80px;
	right: 30px;
	width: 50px;
	height: 50px;
	background: linear-gradient(135deg, #8B4513, #A0522D);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4px 12px rgba(139, 69, 19, 0.4);
	z-index: 999;
	transition: all 0.3s ease;
	border: 2px solid rgba(255, 255, 255, 0.2);
}
.back-to-top.data-v-4e10ad7b:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
	box-shadow: 0 2px 8px rgba(139, 69, 19, 0.6);
}
.back-to-top-icon.data-v-4e10ad7b {
	color: #FFFFFF;
	font-size: 20px;
	font-weight: bold;
	text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

