/**
 * 模板编辑器工具函数
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-06-24
 */

import { VALIDATION_RULES, ERROR_TYPES, MESSAGES } from '@/constants/templateEditor.js';

/**
 * 验证模板名称
 * @param {string} name - 模板名称
 * @returns {Object} 验证结果 {valid: boolean, message: string}
 */
export function validateTemplateName(name) {
	if (!name || typeof name !== 'string') {
		return {
			valid: false,
			message: MESSAGES.ERROR.TEMPLATE_NAME_REQUIRED
		};
	}
	
	const trimmedName = name.trim();
	
	if (trimmedName.length < VALIDATION_RULES.TEMPLATE_NAME.MIN_LENGTH) {
		return {
			valid: false,
			message: MESSAGES.ERROR.TEMPLATE_NAME_REQUIRED
		};
	}
	
	if (trimmedName.length > VALIDATION_RULES.TEMPLATE_NAME.MAX_LENGTH) {
		return {
			valid: false,
			message: `模板名称不能超过${VALIDATION_RULES.TEMPLATE_NAME.MAX_LENGTH}个字符`
		};
	}
	
	if (!VALIDATION_RULES.TEMPLATE_NAME.PATTERN.test(trimmedName)) {
		return {
			valid: false,
			message: '模板名称只能包含中文、英文、数字和空格'
		};
	}
	
	return {
		valid: true,
		message: ''
	};
}

/**
 * 验证颜色格式
 * @param {string} color - 颜色值
 * @returns {boolean} 是否有效
 */
export function validateColor(color) {
	return typeof color === 'string' && VALIDATION_RULES.COLOR.PATTERN.test(color);
}

/**
 * 验证字体大小
 * @param {number} fontSize - 字体大小
 * @returns {boolean} 是否有效
 */
export function validateFontSize(fontSize) {
	const size = Number(fontSize);
	return !isNaN(size) && 
		   size >= VALIDATION_RULES.FONT_SIZE.MIN && 
		   size <= VALIDATION_RULES.FONT_SIZE.MAX;
}

/**
 * 验证高度拉伸值
 * @param {number} heightScale - 高度拉伸值
 * @returns {boolean} 是否有效
 */
export function validateHeightScale(heightScale) {
	const scale = Number(heightScale);
	return !isNaN(scale) && 
		   scale >= VALIDATION_RULES.HEIGHT_SCALE.MIN && 
		   scale <= VALIDATION_RULES.HEIGHT_SCALE.MAX;
}

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
export function generateUniqueId() {
	return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * 深拷贝对象
 * @param {*} obj - 要拷贝的对象
 * @returns {*} 拷贝后的对象
 */
export function deepClone(obj) {
	if (obj === null || typeof obj !== 'object') {
		return obj;
	}
	
	if (obj instanceof Date) {
		return new Date(obj.getTime());
	}
	
	if (obj instanceof Array) {
		return obj.map(item => deepClone(item));
	}
	
	if (typeof obj === 'object') {
		const cloned = {};
		for (const key in obj) {
			if (obj.hasOwnProperty(key)) {
				cloned[key] = deepClone(obj[key]);
			}
		}
		return cloned;
	}
	
	return obj;
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay) {
	let timeoutId;
	return function (...args) {
		clearTimeout(timeoutId);
		timeoutId = setTimeout(() => func.apply(this, args), delay);
	};
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay) {
	let lastCall = 0;
	return function (...args) {
		const now = Date.now();
		if (now - lastCall >= delay) {
			lastCall = now;
			return func.apply(this, args);
		}
	};
}

/**
 * 计算两点之间的距离
 * @param {number} x1 - 第一个点的x坐标
 * @param {number} y1 - 第一个点的y坐标
 * @param {number} x2 - 第二个点的x坐标
 * @param {number} y2 - 第二个点的y坐标
 * @returns {number} 距离
 */
export function calculateDistance(x1, y1, x2, y2) {
	const dx = x2 - x1;
	const dy = y2 - y1;
	return Math.sqrt(dx * dx + dy * dy);
}

/**
 * 判断点是否在矩形内
 * @param {number} x - 点的x坐标
 * @param {number} y - 点的y坐标
 * @param {Object} rect - 矩形对象 {x, y, width, height}
 * @returns {boolean} 是否在矩形内
 */
export function isPointInRect(x, y, rect) {
	return x >= rect.x && 
		   x <= rect.x + rect.width && 
		   y >= rect.y && 
		   y <= rect.y + rect.height;
}

/**
 * 判断点是否在圆内
 * @param {number} x - 点的x坐标
 * @param {number} y - 点的y坐标
 * @param {Object} circle - 圆对象 {x, y, radius}
 * @returns {boolean} 是否在圆内
 */
export function isPointInCircle(x, y, circle) {
	const distance = calculateDistance(x, y, circle.x, circle.y);
	return distance <= circle.radius;
}

/**
 * 约束数值在指定范围内
 * @param {number} value - 要约束的值
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {number} 约束后的值
 */
export function clamp(value, min, max) {
	return Math.min(Math.max(value, min), max);
}

/**
 * 格式化错误信息
 * @param {Error} error - 错误对象
 * @param {string} context - 错误上下文
 * @returns {Object} 格式化后的错误信息
 */
export function formatError(error, context = '') {
	return {
		type: ERROR_TYPES.VALIDATION_ERROR,
		message: error.message || '未知错误',
		context,
		timestamp: new Date().toISOString(),
		stack: error.stack
	};
}

/**
 * 安全执行函数
 * @param {Function} func - 要执行的函数
 * @param {*} defaultValue - 默认返回值
 * @param {string} context - 执行上下文
 * @returns {*} 执行结果或默认值
 */
export function safeExecute(func, defaultValue = null, context = '') {
	try {
		return func();
	} catch (error) {
		console.error(`安全执行失败 [${context}]:`, error);
		return defaultValue;
	}
}

/**
 * 获取设备信息
 * @returns {Object} 设备信息
 */
export function getDeviceInfo() {
	return safeExecute(() => {
		// 优先使用新API
		if (uni.getWindowInfo) {
			return uni.getWindowInfo();
		}
		// 降级到旧API
		return uni.getSystemInfoSync();
	}, {
		windowWidth: 375,
		windowHeight: 667,
		platform: 'unknown'
	}, '获取设备信息');
}

/**
 * 计算Canvas尺寸
 * @param {number} screenWidth - 屏幕宽度
 * @param {number} widthRatio - 宽度比例
 * @param {number} aspectRatio - 宽高比
 * @returns {Object} Canvas尺寸 {width, height}
 */
export function calculateCanvasSize(screenWidth, widthRatio = 0.9, aspectRatio = 0.6) {
	const width = Math.floor(screenWidth * widthRatio);
	const height = Math.floor(width * aspectRatio);
	return { width, height };
}
