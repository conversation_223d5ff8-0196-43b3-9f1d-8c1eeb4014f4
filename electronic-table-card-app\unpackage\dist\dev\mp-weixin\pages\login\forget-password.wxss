
.container.data-v-35706aac {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: space-between;
	height: 100vh;
	background-image: url('http://14.103.146.84:8890/i/2025/06/12/layout_ornament.png');
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;
	padding: 0 20px;
	box-sizing: border-box;
	overflow: hidden;
}
.logo-container.data-v-35706aac {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 50px;
	margin-bottom: 40px;
}
.logo.data-v-35706aac {
	width: 100px;
	height: 100px;
}
.form-container.data-v-35706aac {
	flex: 1;
	width: 100%;
	max-width: 350px;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.input-group.data-v-35706aac {
	width: 100%;
	margin-bottom: 20px;
}
.title-container.data-v-35706aac {
	margin-bottom: 30px;
}
.title-text.data-v-35706aac {
	color: #2C1810;
	font-size: 28px;
	font-weight: 900;
	letter-spacing: 4px;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 1px 1px 2px rgba(139,69,19,0.4);
	text-align: center;
}
.verification-group.data-v-35706aac {
	width: 100%;
	margin-bottom: 30px;
	display: flex;
	flex-direction: row;
	align-items: flex-end;
	gap: 12px;
}
.verification-input.data-v-35706aac {
	flex: 1;
}
.get-code-btn.data-v-35706aac {
	width: 110px;
	height: 48px;
	background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(139, 69, 19, 0.2);
	border: 1px solid rgba(139, 69, 19, 0.3);
	transition: all 0.3s ease;
}
.get-code-btn.data-v-35706aac:active {
	-webkit-transform: translateY(1px);
	        transform: translateY(1px);
	box-shadow: 0 1px 4px rgba(139, 69, 19, 0.3);
}
.get-code-btn.disabled.data-v-35706aac {
	background: rgba(139, 69, 19, 0.3);
	color: rgba(255, 255, 255, 0.6);
	cursor: not-allowed;
}
.get-code-text.data-v-35706aac {
	color: #FFFFFF;
	font-size: 13px;
	font-weight: 600;
	letter-spacing: 0.5px;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}
.reset-btn-container.data-v-35706aac {
	width: 100%;
	margin-top: 30px;
	margin-bottom: 20px;
}
.reset-btn.data-v-35706aac {
	width: 100%;
	height: 52px;
	background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
	border-radius: 26px;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	overflow: hidden;
	box-shadow: 0 4px 16px rgba(139, 69, 19, 0.3);
	border: none;
	transition: all 0.3s ease;
}
.reset-btn.data-v-35706aac:active {
	-webkit-transform: translateY(2px);
	        transform: translateY(2px);
	box-shadow: 0 2px 8px rgba(139, 69, 19, 0.4);
}
.reset-text.data-v-35706aac {
	color: #FFFFFF;
	font-size: 18px;
	font-weight: 600;
	letter-spacing: 2px;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
	position: relative;
	z-index: 10;
}
.back-container.data-v-35706aac {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 40px;
	padding: 10px;
}
.back-text.data-v-35706aac {
	color: #8B4513;
	font-size: 16px;
	font-weight: 500;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
	letter-spacing: 1px;
	text-decoration: underline;
	-webkit-text-decoration-color: rgba(139, 69, 19, 0.5);
	        text-decoration-color: rgba(139, 69, 19, 0.5);
}

