{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/public/single-screen.vue?bb76", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/public/single-screen.vue?285e", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/public/single-screen.vue?67d7", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/public/single-screen.vue?1ecc", "uni-app:///pages/subPackage/public/single-screen.vue", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/public/single-screen.vue?2f5b", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/public/single-screen.vue?ea32"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isMP", "screenMode", "showTemplates", "showEditor", "deviceInfo", "name", "id", "mac", "status", "templates", "selectedTemplate", "currentTemplate", "loading", "edit<PERSON>ields", "label", "value", "placeholder", "onLoad", "methods", "goBack", "uni", "getDeviceInfo", "setTimeout", "getTemplateList", "url", "method", "success", "image", "frontImage", "backImage", "background", "type", "fields", "console", "fail", "complete", "useDefaultTemplates", "getDeviceTemplate", "then", "res", "catch", "switchScreenMode", "title", "icon", "duration", "showTemplateSelector", "closeTemplateSelector", "showContentEditor", "closeContentEditor", "saveContent", "selectTemplate", "template", "newEdit<PERSON><PERSON>s", "confirmScreen", "<PERSON><PERSON><PERSON><PERSON>", "deviceId", "templateId", "content"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACc;;;AAG1E;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA6tB,CAAgB,6rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC8IjvB;AAAA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC,aACA;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA;IAEA;EACA;EACAC;IACA;IACA;MACA;MACA;MACA;MACA;MACA;IACA;;IAEA;IACA;;IAEA;;IAEA;EAEA;EACAC;IACAC;MACAC;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACAC;QACA;UACAjB;UACAC;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAe;MAAA;MACA;MACA;MACAH;QACAI;QACAC;QACAC;UACA;YACA;YACA;cACA;gBACApB;gBACAD;gBACAsB;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;cACA;YACA;;YAEA;YACA;cACA;cACA;YACA;UACA;YACAC;YACA;YACA;UACA;QACA;QACAC;UACAD;UACA;UACA;QACA;QACAE;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA,kBACA;QACA9B;QACAD;QACAsB;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACA1B;QACAD;QACAsB;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,EACA;;MAEA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAK;MAAA;MACA;MACA,wCACAC;QACA;UACA;UACA;UACA;UACA;YACAC;cACA;gBACA;cACA;YACA;UACA;QACA;MACA,GACAC;QACAP;MACA;IACA;IACA;IACAQ;MACA;MACArB;QACAsB;QACAC;QACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA3B;QACAI;QACAC;QACAC;UACA;YACA;YACA;YACA;;YAEA;YACA;cACA;gBACApB;gBACAwB;gBACAF;gBACAC;gBACAE;gBACAC;cACA;YACA;cACA;cACA;YACA;YAEA;UACA;QACA;QACAE;UACAD;QACA;QACAE;UACA;UACA;QACA;MACA;IACA;IACA;IACAa;MACA;IACA;IACA;IACAC;MACA;MACA7B;QACAsB;QACAC;MACA;IACA;IACA;IACAO;MAAA;MACA;MACA;MACA;MACA9B;QACAsB;QACAC;MACA;;MAEA;MACA;QACA;QACAQ;UACA;UACA;UACAC;YACAtC;YACAC;YACAC;UACA;QACA;QACA;MACA;IACA;IACA;IACAqC;MACA;MACA;MAAA,2CACA;QAAA;MAAA;QAAA;UAAA;UACA;YACAC;YACAlC;cACAsB;cACAC;YACA;YACA;UACA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;MAEA;QACA;QACA;UACAY;UACAC;UACAvD;UACAwD;YAAA;cACA3C;cACAC;YACA;UAAA;QACA;;QAEA;QACAkB;QACAb;UACAsB;QACA;;QAEA;QACApB;UACAF;UACAA;YACAsB;YACAC;UACA;;UAEA;UACArB;YACAF;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpdA;AAAA;AAAA;AAAA;AAAo2C,CAAgB,ouCAAG,EAAC,C;;;;;;;;;;;ACAx3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/subPackage/public/single-screen.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/subPackage/public/single-screen.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./single-screen.vue?vue&type=template&id=5356998b&\"\nvar renderjs\nimport script from \"./single-screen.vue?vue&type=script&lang=js&\"\nexport * from \"./single-screen.vue?vue&type=script&lang=js&\"\nimport style0 from \"./single-screen.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/subPackage/public/single-screen.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./single-screen.vue?vue&type=template&id=5356998b&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./single-screen.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./single-screen.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\"> \r\n\t\t<!-- 主内容区域 -->\r\n\t\t<view class=\"content\">\r\n\t\t\t<!-- 屏幕模式选择 -->\r\n\t\t\t<view class=\"screen-mode-tabs\">\r\n\t\t\t\t<view class=\"tab-row\">\r\n\t\t\t\t\t<view class=\"tab-item\" :class=\"{active: screenMode === 'same'}\" @click=\"switchScreenMode('same')\" style=\"width: 50%;\">\r\n\t\t\t\t\t\t<text>前后屏相同</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tab-item\" :class=\"{active: screenMode === 'different'}\" @click=\"switchScreenMode('different')\" style=\"width: 50%;\">\r\n\t\t\t\t\t\t<text>前后屏不同</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 预览区域 -->\r\n\t\t\t<view class=\"preview-area\">\r\n\t\t\t\t<!-- 前后屏相同模式 -->\r\n\t\t\t\t<view class=\"preview-card\" v-if=\"screenMode === 'same'\">\r\n\t\t\t\t\t<view class=\"template-preview\">\r\n\t\t\t\t\t\t<!-- 中国风边框和内容 -->\r\n\t\t\t\t\t\t<view class=\"chinese-style-template front-screen\" :style=\"{'background-image': currentTemplate && currentTemplate.background ? 'url(' + currentTemplate.background + ')' : 'url(/static/images/public_template/33882e8f90e990a4f1f140011e7a58ce.png)'}\">\r\n\t\t\t\t\t\t\t<view class=\"screen-tag\">前</view>\r\n\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"operation-buttons\">\r\n\t\t\t\t\t\t\t<view class=\"op-button-row\">\r\n\t\t\t\t\t\t\t\t<view class=\"op-button\" @click=\"showTemplateSelector\" style=\"width: 50%;\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"op-icon\" src=\"/static/images/template-icon.svg\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t\t\t<text class=\"op-text\">选择模板</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"op-button\" @click=\"showContentEditor\" style=\"width: 50%;\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"op-icon\" src=\"/static/images/edit-icon.svg\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t\t\t<text class=\"op-text\">编辑内容</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"bottom-gradient-line\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 前后屏不同模式 -->\r\n\t\t\t\t<view class=\"preview-cards\" v-if=\"screenMode === 'different'\">\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<!-- 前屏 -->\r\n\t\t\t\t\t\t<view class=\"preview-card\">\r\n\t\t\t\t\t\t\t<view class=\"template-preview\">\r\n\t\t\t\t\t\t\t\t<view class=\"chinese-style-template front-screen\" :style=\"{'background-image': currentTemplate && currentTemplate.background ? 'url(' + currentTemplate.background + ')' : 'url(/static/images/public_template/33882e8f90e990a4f1f140011e7a58ce.png)'}\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"screen-tag\">前</view> \r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- 按钮模块 -->\r\n\t\t\t\t\t\t<view class=\"operation-buttons\">\r\n\t\t\t\t\t\t\t<view class=\"op-button-row\">\r\n\t\t\t\t\t\t\t\t<view class=\"op-button\" @click=\"showTemplateSelector\" style=\"width: 50%;\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"op-icon\" src=\"/static/images/template-icon.svg\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t\t\t<text class=\"op-text\">选择模板</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"op-button\" @click=\"showContentEditor\" style=\"width: 50%;\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"op-icon\" src=\"/static/images/edit-icon.svg\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t\t\t<text class=\"op-text\">编辑内容</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"bottom-gradient-line\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<!-- 后屏 -->\r\n\t\t\t\t\t\t<view class=\"preview-card\">\r\n\t\t\t\t\t\t\t<view class=\"template-preview\">\r\n\t\t\t\t\t\t\t\t<view class=\"chinese-style-template back-screen\" :style=\"{'background-image': currentTemplate && currentTemplate.background ? 'url(' + currentTemplate.background + ')' : 'url(/static/images/public_template/f2e05295636f1b849b791a21b5548e30.png)'}\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"screen-tag\">后</view> \r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- 按钮模块 -->\r\n\t\t\t\t\t\t<view class=\"operation-buttons\">\r\n\t\t\t\t\t\t\t<view class=\"op-button-row\">\r\n\t\t\t\t\t\t\t\t<view class=\"op-button\" @click=\"showTemplateSelector\" style=\"width: 50%;\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"op-icon\" src=\"/static/images/template-icon.svg\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t\t\t<text class=\"op-text\">选择模板</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"op-button\" @click=\"showContentEditor\" style=\"width: 50%;\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"op-icon\" src=\"/static/images/edit-icon.svg\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t\t\t<text class=\"op-text\">编辑内容</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"bottom-gradient-line\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view> \r\n\t\t\t\t</view>\r\n\t\t\t</view> \r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 底部确认按钮 -->\r\n\t\t<view class=\"footer\">\r\n\t\t\t<view class=\"confirm-button\" @click=\"confirmScreen\">\r\n\t\t\t\t<text>确认无误，下一步</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 模板选择弹窗 -->\r\n\t\t<u-popup :show=\"showTemplates\" mode=\"bottom\" @close=\"closeTemplateSelector\" round=\"10\" closeable>\r\n\t\t\t<view class=\"template-popup\">\r\n\t\t\t\t<view class=\"popup-title\">\r\n\t\t\t\t\t<text>选择模板</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<scroll-view class=\"template-scroll\" scroll-x=\"true\" show-scrollbar=\"false\">\r\n\t\t\t\t\t<view class=\"template-list\">\r\n\t\t\t\t\t\t<view class=\"template-item\" v-for=\"(item, index) in templates\" :key=\"index\" @click=\"selectTemplate(item)\" :class=\"{active: selectedTemplate === item.id}\">\r\n\t\t\t\t\t\t\t<image class=\"template-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t<text class=\"template-name\">{{item.name}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\r\n\t\t<!-- 内容编辑弹窗 -->\r\n\t\t<u-popup :show=\"showEditor\" mode=\"bottom\" @close=\"closeContentEditor\" round=\"10\" closeable>\r\n\t\t\t<view class=\"editor-popup\">\r\n\t\t\t\t<view class=\"popup-title\">\r\n\t\t\t\t\t<text>编辑内容</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"edit-form\">\r\n\t\t\t\t\t<view class=\"form-item\" v-for=\"(field, index) in editFields\" :key=\"index\">\r\n\t\t\t\t\t\t<text class=\"form-label\">{{field.label}}</text>\r\n\t\t\t\t\t\t<input class=\"form-input\" v-model=\"field.value\" :placeholder=\"field.placeholder\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"popup-button\" @click=\"saveContent\">\r\n\t\t\t\t\t<text>保存</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { fetchTemplates, fetchDeviceTemplate } from '@/common/api.js';\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisMP: false, // 是否为小程序环境\r\n\t\t\t\tscreenMode: 'same', // 屏幕模式：same-前后屏相同，different-前后屏不同\r\n\t\t\t\tshowTemplates: false, // 是否显示模板选择弹窗\r\n\t\t\t\tshowEditor: false, // 是否显示内容编辑弹窗\r\n\t\t\t\tdeviceInfo: {\r\n\t\t\t\t\tname: '设备名称',\r\n\t\t\t\t\tid: '12345678',\r\n\t\t\t\t\tmac: '00:11:22:33:44:55',\r\n\t\t\t\t\tstatus: 'connected'\r\n\t\t\t\t},\r\n\t\t\t\ttemplates: [],\r\n\t\t\t\tselectedTemplate: null,\r\n\t\t\t\tcurrentTemplate: null,\r\n\t\t\t\tloading: false,\r\n\t\t\t\teditFields: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tlabel: '姓名',\r\n\t\t\t\t\t\tvalue: '',\r\n\t\t\t\t\t\tplaceholder: '请输入姓名'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tlabel: '职称',\r\n\t\t\t\t\t\tvalue: '',\r\n\t\t\t\t\t\tplaceholder: '请输入职称'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tlabel: '公司名称',\r\n\t\t\t\t\t\tvalue: '',\r\n\t\t\t\t\t\tplaceholder: '请输入公司名称'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tlabel: '后屏信息1',\r\n\t\t\t\t\t\tvalue: '',\r\n\t\t\t\t\t\tplaceholder: '请输入后屏信息1'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tlabel: '后屏信息2',\r\n\t\t\t\t\t\tvalue: '',\r\n\t\t\t\t\t\tplaceholder: '请输入后屏信息2'\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t// 获取设备信息\r\n\t\t\tif (options.deviceId) {\r\n\t\t\t\tthis.deviceInfo.id = options.deviceId;\r\n\t\t\t\t// 获取设备详情\r\n\t\t\t\tthis.getDeviceInfo(options.deviceId);\r\n\t\t\t\t// 获取设备当前使用的模板\r\n\t\t\t\tthis.getDeviceTemplate(options.deviceId);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 获取所有模板列表\r\n\t\t\tthis.getTemplateList();\r\n\t\t\t\r\n\t\t\t// 判断平台\r\n\t\t\t// #ifdef MP\r\n\t\t\tthis.isMP = true;\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgoBack() {\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取设备详情\r\n\t\t\tgetDeviceInfo(deviceId) {\r\n\t\t\t\t// 这里应该调用API获取设备详情\r\n\t\t\t\t// 模拟API调用\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.deviceInfo = {\r\n\t\t\t\t\t\tname: '电子桌牌-' + deviceId,\r\n\t\t\t\t\t\tid: deviceId,\r\n\t\t\t\t\t\tmac: '00:11:22:33:44:55',\r\n\t\t\t\t\t\tstatus: 'connected'\r\n\t\t\t\t\t};\r\n\t\t\t\t}, 500);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取模板列表\r\n\t\t\tgetTemplateList() {\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\t// 从本地temp.json文件加载模板数据\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl: '/pages/public/tempjson/temp.json',\r\n\t\t\t\t\tmethod: 'GET',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.data && res.data.code === 200 && res.data.data) {\r\n\t\t\t\t\t\t\t// 转换数据格式\r\n\t\t\t\t\t\t\tthis.templates = res.data.data.map(item => {\r\n\t\t\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\t\tid: item.id,\r\n\t\t\t\t\t\t\t\t\tname: item.template.name || `模板${item.id}`,\r\n\t\t\t\t\t\t\t\t\timage: item.template.path || '/static/images/public_template/33882e8f90e990a4f1f140011e7a58ce.png',\r\n\t\t\t\t\t\t\t\t\tfrontImage: '/static/images/bamboo.svg',\r\n\t\t\t\t\t\t\t\t\tbackImage: '/static/images/lantern.svg',\r\n\t\t\t\t\t\t\t\t\tbackground: item.background,\r\n\t\t\t\t\t\t\t\t\ttype: 'chinese',\r\n\t\t\t\t\t\t\t\t\tfields: ['姓名', '职称', '公司名称', '后屏信息1', '后屏信息2']\r\n\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 如果没有选中模板，默认选择第一个\r\n\t\t\t\t\t\t\tif (!this.selectedTemplate && this.templates.length > 0) {\r\n\t\t\t\t\t\t\t\tthis.selectedTemplate = this.templates[0].id;\r\n\t\t\t\t\t\t\t\tthis.currentTemplate = this.templates[0];\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.error('模板数据格式不正确');\r\n\t\t\t\t\t\t\t// 使用默认模拟数据\r\n\t\t\t\t\t\t\tthis.useDefaultTemplates();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('获取模板列表失败', err);\r\n\t\t\t\t\t\t// 使用默认模拟数据\r\n\t\t\t\t\t\tthis.useDefaultTemplates();\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcomplete: () => {\r\n\t\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 使用默认模拟数据\r\n\t\t\tuseDefaultTemplates() {\r\n\t\t\t\tthis.templates = [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 1,\r\n\t\t\t\t\t\tname: '中国风红色',\r\n\t\t\t\t\t\timage: '/static/images/public_template/33882e8f90e990a4f1f140011e7a58ce.png',\r\n\t\t\t\t\t\tfrontImage: '/static/images/bamboo.svg',\r\n\t\t\t\t\t\tbackImage: '/static/images/lantern.svg',\r\n\t\t\t\t\t\tbackground: 'http://www.chuantiba.com/api/storage/public/1633881600/33882e8f90e990a4f1f140011e7a58ce.png',\r\n\t\t\t\t\t\ttype: 'chinese',\r\n\t\t\t\t\t\tfields: ['姓名', '职称', '公司名称', '后屏信息1', '后屏信息2']\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 2,\r\n\t\t\t\t\t\tname: '中国风黑色',\r\n\t\t\t\t\t\timage: '/static/images/public_template/f2e05295636f1b849b791a21b5548e30.png',\r\n\t\t\t\t\t\tfrontImage: '/static/images/bamboo.svg',\r\n\t\t\t\t\t\tbackImage: '/static/images/lantern.svg',\r\n\t\t\t\t\t\tbackground: 'http://www.chuantiba.com/api/storage/public/1633881600/f2e05295636f1b849b791a21b5548e30.png',\r\n\t\t\t\t\t\ttype: 'chinese',\r\n\t\t\t\t\t\tfields: ['姓名', '职称', '公司名称', '后屏信息1', '后屏信息2']\r\n\t\t\t\t\t}\r\n\t\t\t\t];\r\n\t\t\t\t\r\n\t\t\t\t// 如果没有选中模板，默认选择第一个\r\n\t\t\t\tif (!this.selectedTemplate && this.templates.length > 0) {\r\n\t\t\t\t\tthis.selectedTemplate = this.templates[0].id;\r\n\t\t\t\t\tthis.currentTemplate = this.templates[0];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取设备当前使用的模板\r\n\t\t\tgetDeviceTemplate(deviceId) {\r\n\t\t\t\t// 调用API获取设备当前使用的模板\r\n\t\t\t\tfetchDeviceTemplate(deviceId)\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tif (res.code === 200 && res.data) {\r\n\t\t\t\t\t\t\tthis.selectedTemplate = res.data.templateId;\r\n\t\t\t\t\t\t\tthis.screenMode = res.data.screenMode || 'same';\r\n\t\t\t\t\t\t\t// 更新编辑字段\r\n\t\t\t\t\t\t\tif (res.data.content && res.data.content.length > 0) {\r\n\t\t\t\t\t\t\t\tres.data.content.forEach((item, index) => {\r\n\t\t\t\t\t\t\t\t\tif (this.editFields[index]) {\r\n\t\t\t\t\t\t\t\t\t\tthis.editFields[index].value = item.value;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('获取设备模板失败', err);\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 切换屏幕模式\r\n\t\t\tswitchScreenMode(mode) {\r\n\t\t\t\tthis.screenMode = mode;\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: mode === 'same' ? '已选择前后屏相同' : '已选择前后屏不同',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 1500\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 显示模板选择器\r\n\t\t\tshowTemplateSelector() {\r\n\t\t\t\tthis.showTemplates = true;\r\n\t\t\t},\r\n\t\t\t// 关闭模板选择器\r\n\t\t\tcloseTemplateSelector() {\r\n\t\t\t\tthis.showTemplates = false;\r\n\t\t\t},\r\n\t\t\t// 显示内容编辑器\r\n\t\t\tshowContentEditor() {\r\n\t\t\t\t// 从temp.json加载模板数据\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl: '/pages/public/tempjson/temp.json',\r\n\t\t\t\t\tmethod: 'GET',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.data && res.data.code === 200 && res.data.data && res.data.data.length > 0) {\r\n\t\t\t\t\t\t\t// 获取第一个模板的ID和背景\r\n\t\t\t\t\t\t\tconst templateId = res.data.data[0].id;\r\n\t\t\t\t\t\t\tconst templateBackground = res.data.data[0].background;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 更新当前模板和选中模板\r\n\t\t\t\t\t\t\tif (!this.currentTemplate) {\r\n\t\t\t\t\t\t\t\tthis.currentTemplate = {\r\n\t\t\t\t\t\t\t\t\tid: templateId,\r\n\t\t\t\t\t\t\t\t\tbackground: templateBackground,\r\n\t\t\t\t\t\t\t\t\tfrontImage: '/static/images/bamboo.svg',\r\n\t\t\t\t\t\t\t\t\tbackImage: '/static/images/lantern.svg',\r\n\t\t\t\t\t\t\t\t\ttype: 'chinese',\r\n\t\t\t\t\t\t\t\t\tfields: ['姓名', '职称', '公司名称', '后屏信息1', '后屏信息2']\r\n\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t// 更新现有模板的背景\r\n\t\t\t\t\t\t\t\tthis.currentTemplate.background = templateBackground;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tthis.selectedTemplate = templateId;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('加载模板数据失败', err);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcomplete: () => {\r\n\t\t\t\t\t\t// 显示编辑器弹窗\r\n\t\t\t\t\t\tthis.showEditor = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 关闭内容编辑器\r\n\t\t\tcloseContentEditor() {\r\n\t\t\t\tthis.showEditor = false;\r\n\t\t\t},\r\n\t\t\t// 保存编辑内容\r\n\t\t\tsaveContent() {\r\n\t\t\t\tthis.showEditor = false;\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '内容已保存',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 选择模板\r\n\t\t\tselectTemplate(template) {\r\n\t\t\t\tthis.selectedTemplate = template.id;\r\n\t\t\t\tthis.currentTemplate = template;\r\n\t\t\t\tthis.showTemplates = false;\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: `已选择${template.name}模板`,\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 根据模板更新编辑字段\r\n\t\t\t\tif (template.fields && template.fields.length > 0) {\r\n\t\t\t\t\tconst newEditFields = [];\r\n\t\t\t\t\ttemplate.fields.forEach((field, index) => {\r\n\t\t\t\t\t\t// 保留已有的值\r\n\t\t\t\t\t\tconst existingValue = this.editFields[index] ? this.editFields[index].value : '';\r\n\t\t\t\t\t\tnewEditFields.push({\r\n\t\t\t\t\t\t\tlabel: field,\r\n\t\t\t\t\t\t\tvalue: existingValue,\r\n\t\t\t\t\t\t\tplaceholder: `请输入${field}`\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.editFields = newEditFields;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 确认投屏\r\n\t\t\tconfirmScreen() {\r\n\t\t\t\t// 验证表单\r\n\t\t\t\tlet isValid = true;\r\n\t\t\t\tfor (let field of this.editFields) {\r\n\t\t\t\t\tif (!field.value) {\r\n\t\t\t\t\t\tisValid = false;\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: `请输入${field.label}`,\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (isValid) {\r\n\t\t\t\t\t// 构建投屏数据\r\n\t\t\t\t\tconst screenData = {\r\n\t\t\t\t\t\tdeviceId: this.deviceInfo.id,\r\n\t\t\t\t\t\ttemplateId: this.selectedTemplate,\r\n\t\t\t\t\t\tscreenMode: this.screenMode,\r\n\t\t\t\t\t\tcontent: this.editFields.map(field => ({\r\n\t\t\t\t\t\t\tlabel: field.label,\r\n\t\t\t\t\t\t\tvalue: field.value\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t};\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 发送投屏请求\r\n\t\t\t\t\tconsole.log('投屏数据:', screenData);\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '投屏中...'\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 模拟投屏请求\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '投屏成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 投屏成功后返回\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t}, 2000);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.container {\r\n\tflex: 1;\r\n\tbackground-color: #f5f5f5;\r\n\tbackground-image: url('/static/images/bg.svg');\r\n\tbackground-size: cover;\r\n\tbackground-position: center;\r\n}\r\n\r\n.navbar {\r\n\tflex-direction: row;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 20rpx 40rpx;\r\n\tpadding-top: calc(20rpx + var(--status-bar-height));\r\n\tbackground-color: transparent;\r\n}\r\n\r\n.left-capsule {\r\n\twidth: 80rpx;\r\n}\r\n\r\n.capsule-btn {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tborder-radius: 30rpx;\r\n\tbackground-color: rgba(255, 255, 255, 0.8);\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n}\r\n\r\n.nav-title {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #ffffff;\r\n}\r\n\r\n.right-capsule {\r\n\twidth: 80rpx;\r\n}\r\n\r\n.content {\r\n\tpadding: 30rpx;\r\n\tflex: 1;\r\n}\r\n\r\n/* 屏幕模式选择标签 */\r\n.screen-mode-tabs {\r\n\tbackground-color: #8B0000; /* 深红色背景 */\r\n\tborder-radius: 8rpx;\r\n\toverflow: hidden;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.tab-row {\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n\twidth: 100%;\r\n}\r\n\r\n.tab-item {\r\n\theight: 80rpx;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tdisplay: flex;\r\n}\r\n\r\n.tab-item text {\r\n\tcolor: #ffffff;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.tab-item.active {\r\n\tbackground-color: #FF0000; /* 鲜红色 */\r\n}\r\n\r\n/* 预览区域 */\r\n.preview-area {\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.preview-card {\r\n\tbackground-color: #ffffff;\r\n\t// border-radius: 16rpx;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n\t//margin-bottom: 20rpx;\r\n}\r\n\r\n.template-preview {\r\n\tpadding: 20rpx;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n/* 前后屏不同模式的卡片布局 */\r\n.preview-cards {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n/* 中国风模板样式 */\r\n.chinese-style-template {\r\n\twidth: 100%;\r\n\theight: 400rpx;\r\n\tposition: relative;\r\n\tborder: 2rpx solid #000000;\r\n\tborder-radius: 8rpx;\r\n\toverflow: hidden;\r\n\tbackground-color: #ffffff;\r\n\tbackground-size: cover;\r\n\tbackground-position: center;\r\n\tbackground-repeat: no-repeat;\r\n}\r\n\r\n/* 前后屏标签 */\r\n.screen-tag {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tbackground-color: #8B0000;\r\n\tcolor: #ffffff;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tborder-bottom-right-radius: 8rpx;\r\n}\r\n \r\n.template-content {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 40rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tbackground-color: rgba(255, 255, 255, 0.7);\r\n\tborder-radius: 8rpx;\r\n\tmargin: 20rpx;\r\n}\r\n\r\n/* 姓名和职称容器 */\r\n.name-title-container {\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.for-front-screen {\r\n\t// display: flex;\r\n\t// flex-direction: column;\r\n\twidth: 100%; /* 或指定具体的宽度 */\r\n    height: auto; /* 保持图片的原始比例 */\r\n    object-fit: cover; /* 覆盖容器，可能会裁剪图片 */\r\n}\r\n\r\n\r\n.content-divider {\r\n\tfont-size: 60rpx;\r\n\tfont-weight: bold;\r\n\tmargin: 0 20rpx;\r\n}\r\n\r\n.content-title {\r\n\tfont-size: 60rpx;\r\n\tfont-weight: bold;\r\n\ttext-shadow: 1rpx 1rpx 3rpx rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.company-name {\r\n\tfont-size: 36rpx;\r\n\tcolor: #FF0000;\r\n\tbackground: linear-gradient(to right, #FF0000, #000000);\r\n\t-webkit-background-clip: text;\r\n\t-webkit-text-fill-color: transparent;\r\n\tpadding: 10rpx 0;\r\n\ttext-shadow: 0 0 2rpx rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n.company-name-small {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tmargin-top: 10rpx;\r\n\tfont-weight: bold;\r\n\ttext-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.content-subtitle {\r\n\tfont-size: 36rpx;\r\n\tcolor: #333;\r\n\tfont-weight: bold;\r\n\ttext-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n/* 底部操作按钮 */\r\n.operation-buttons {\r\n\tmargin-bottom: 30rpx;\r\n\tbackground-color: #ffffff;\r\n\t// border-radius: 16rpx;\r\n\tpadding: 20rpx;\r\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n\tposition: relative;\r\n}\r\n\r\n.op-button-row {\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n\twidth: 100%;\r\n\tposition: relative;\r\n}\r\n\r\n/* 删除button-divider样式 */\r\n\r\n.bottom-gradient-line {\r\n\theight: 4rpx;\r\n\twidth: 100%;\r\n\tbackground: linear-gradient(to right, #8B0000, #000000);\r\n\tposition: absolute;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tborder-bottom-left-radius: 16rpx;\r\n\tborder-bottom-right-radius: 16rpx;\r\n}\r\n.op-button {\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 20rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.op-icon {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.op-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n/* 底部确认按钮 */\r\n.footer {\r\n\tposition: fixed;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbackground-color: transparent;\r\n\tpadding: 20rpx 30rpx;\r\n\t// padding-bottom: calc(20rpx + env(safe-area-inset-bottom));\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n}\r\n\r\n.confirm-button {\r\n\tbackground-color: #8B0000; /* 深红色 */\r\n\theight: 90rpx;\r\n\tborder-radius: 45rpx;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tbox-shadow: 0 4rpx 10rpx rgba(139, 0, 0, 0.3);\r\n\tdisplay: flex;\r\n\twidth: 80%;\r\n}\r\n\r\n.confirm-button text {\r\n\tcolor: #ffffff;\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\talign-items: center;\r\n}\r\n\r\n/* 弹窗样式 */\r\n.template-popup, .editor-popup {\r\n\tpadding: 30rpx;\r\n\tbackground-color: #ffffff;\r\n\tborder-top-left-radius: 20rpx;\r\n\tborder-top-right-radius: 20rpx;\r\n}\r\n\r\n.popup-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tmargin-bottom: 30rpx;\r\n\ttext-align: center;\r\n}\r\n\r\n.template-scroll {\r\n\twidth: 100%;\r\n\theight: 300rpx;\r\n}\r\n\r\n.template-list {\r\n\tflex-direction: row;\r\n\tpadding: 10rpx 0;\r\n}\r\n\r\n.template-item {\r\n\twidth: 200rpx;\r\n\tmargin-right: 20rpx;\r\n\talign-items: center;\r\n}\r\n\r\n.template-image {\r\n\twidth: 180rpx;\r\n\theight: 240rpx;\r\n\tborder-radius: 8rpx;\r\n\tborder: 2rpx solid #eee;\r\n}\r\n\r\n.template-item.active .template-image {\r\n\tborder: 2rpx solid #8B0000;\r\n}\r\n\r\n.template-name {\r\n\tfont-size: 24rpx;\r\n\tmargin-top: 10rpx;\r\n\ttext-align: center;\r\n}\r\n\r\n.edit-form {\r\n\tmargin-top: 20rpx;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.form-item {\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.form-label {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.form-input {\r\n\theight: 80rpx;\r\n\tborder: 1px solid #ddd;\r\n\tborder-radius: 8rpx;\r\n\tpadding: 0 20rpx;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.popup-button {\r\n\tbackground-color: #8B0000; /* 深红色 */\r\n\theight: 80rpx;\r\n\tborder-radius: 40rpx;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n}\r\n\r\n.popup-button text {\r\n\tcolor: #ffffff;\r\n\tfont-size: 30rpx;\r\n\tfont-weight: bold;\r\n}\r\n</style>", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./single-screen.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./single-screen.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751873716791\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}