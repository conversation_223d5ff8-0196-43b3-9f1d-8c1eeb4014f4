{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/edit.vue?2c43", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/edit.vue?e93d", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/edit.vue?4e50", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/edit.vue?6511", "uni-app:///pages/subPackage/meeting/edit.vue", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/edit.vue?c2fa", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/edit.vue?118c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isEdit", "roomId", "roomInfo", "name", "model", "version", "type", "code", "channel", "status", "onLoad", "methods", "getRoomDetail", "id", "cancelEdit", "uni", "saveRoom", "title", "icon", "saveData", "console", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChBA;AAAA;AAAA;AAAA;AAAotB,CAAgB,orBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoExuB;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;QACA;UACAC;UACAV;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;QACA;UACAI;UACAV;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;MAEA;QACA;QACA;QACA;QACA;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAK;MACAC;IACA;IAEA;IACAC;MACA;MACA;QACAD;UACAE;UACAC;QACA;QACA;MACA;;MAEA;MACA;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;MACAA;MAEAC;;MAEA;MACA;MACAL;QACAE;QACAC;MACA;;MAEA;MACAG;QACAN;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9KA;AAAA;AAAA;AAAA;AAA21C,CAAgB,2tCAAG,EAAC,C;;;;;;;;;;;ACA/2C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/subPackage/meeting/edit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/subPackage/meeting/edit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./edit.vue?vue&type=template&id=565efe04&\"\nvar renderjs\nimport script from \"./edit.vue?vue&type=script&lang=js&\"\nexport * from \"./edit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./edit.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/subPackage/meeting/edit.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=template&id=565efe04&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.roomInfo.status = \"active\"\n    }\n    _vm.e1 = function ($event) {\n      _vm.roomInfo.status = \"inactive\"\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<!-- 主内容区域 -->\n\t\t<view class=\"content\">\n\t\t\t<view class=\"edit-form\">\n\t\t\t\t<view class=\"form-title\">\n\t\t\t\t\t<text>{{ isEdit ? '编辑会议室' : '添加会议室' }}</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">会议室名称</text>\n\t\t\t\t\t<input class=\"form-input\" v-model=\"roomInfo.name\" placeholder=\"请输入会议室名称\" />\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">设备型号</text>\n\t\t\t\t\t<input class=\"form-input\" v-model=\"roomInfo.model\" placeholder=\"请输入设备型号\" />\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">设备版本</text>\n\t\t\t\t\t<input class=\"form-input\" v-model=\"roomInfo.version\" placeholder=\"请输入设备版本\" />\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">设备类型</text>\n\t\t\t\t\t<input class=\"form-input\" v-model=\"roomInfo.type\" placeholder=\"请输入设备类型\" />\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">设备编号</text>\n\t\t\t\t\t<input class=\"form-input\" v-model=\"roomInfo.code\" placeholder=\"请输入设备编号\" />\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">设备频道</text>\n\t\t\t\t\t<input class=\"form-input\" v-model=\"roomInfo.channel\" placeholder=\"请输入设备频道\" />\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">设备状态</text>\n\t\t\t\t\t<view class=\"status-selector\">\n\t\t\t\t\t\t<view class=\"status-option\" :class=\"{active: roomInfo.status === 'active'}\" @click=\"roomInfo.status = 'active'\">\n\t\t\t\t\t\t\t<text>启用</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"status-option\" :class=\"{active: roomInfo.status === 'inactive'}\" @click=\"roomInfo.status = 'inactive'\">\n\t\t\t\t\t\t\t<text>禁用</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 底部按钮 -->\n\t\t<view class=\"footer\">\n\t\t\t<view class=\"button-group\">\n\t\t\t\t<view class=\"cancel-button\" @click=\"cancelEdit\">\n\t\t\t\t\t<text>取消</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"save-button\" @click=\"saveRoom\">\n\t\t\t\t\t<text>保存</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tisEdit: false,\n\t\t\t\troomId: '',\n\t\t\t\troomInfo: {\n\t\t\t\t\tname: '',\n\t\t\t\t\tmodel: '',\n\t\t\t\t\tversion: '',\n\t\t\t\t\ttype: '',\n\t\t\t\t\tcode: '',\n\t\t\t\t\tchannel: '',\n\t\t\t\t\tstatus: 'active'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\tif (options.id) {\n\t\t\t\tthis.isEdit = true;\n\t\t\t\tthis.roomId = options.id;\n\t\t\t\t// 获取会议室详情\n\t\t\t\tthis.getRoomDetail(options.id);\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 获取会议室详情\n\t\t\tgetRoomDetail(id) {\n\t\t\t\t// 这里应该调用API获取会议室详情\n\t\t\t\t// 目前使用模拟数据\n\t\t\t\tconst mockData = {\n\t\t\t\t\t'1': {\n\t\t\t\t\t\tid: '1',\n\t\t\t\t\t\tname: '1号会议室',\n\t\t\t\t\t\tmodel: '未绑定',\n\t\t\t\t\t\tversion: '未绑定',\n\t\t\t\t\t\ttype: '未绑定',\n\t\t\t\t\t\tcode: '未绑定',\n\t\t\t\t\t\tchannel: '未绑定',\n\t\t\t\t\t\tstatus: 'active'\n\t\t\t\t\t},\n\t\t\t\t\t'2': {\n\t\t\t\t\t\tid: '2',\n\t\t\t\t\t\tname: '121212',\n\t\t\t\t\t\tmodel: '未绑定',\n\t\t\t\t\t\tversion: '未绑定',\n\t\t\t\t\t\ttype: '未绑定',\n\t\t\t\t\t\tcode: '未绑定',\n\t\t\t\t\t\tchannel: '未绑定',\n\t\t\t\t\t\tstatus: 'inactive'\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tif (mockData[id]) {\n\t\t\t\t\t// this.roomInfo = { ...mockData[id] };\n\t\t\t\t\t// 使用传统方式复制对象\n\t\t\t\t\tconst roomData = mockData[id];\n\t\t\t\t\tfor (let key in roomData) {\n\t\t\t\t\t\tif (roomData.hasOwnProperty(key)) {\n\t\t\t\t\t\t\tthis.roomInfo[key] = roomData[key];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 取消编辑\n\t\t\tcancelEdit() {\n\t\t\t\tuni.navigateBack();\n\t\t\t},\n\t\t\t\n\t\t\t// 保存会议室信息\n\t\t\tsaveRoom() {\n\t\t\t\t// 表单验证\n\t\t\t\tif (!this.roomInfo.name) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入会议室名称',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 构建保存数据\n\t\t\t\tconst saveData = {};\n\t\t\t\t// 复制表单数据\n\t\t\t\tfor (let key in this.roomInfo) {\n\t\t\t\t\tif (this.roomInfo.hasOwnProperty(key)) {\n\t\t\t\t\t\tsaveData[key] = this.roomInfo[key];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// 添加ID\n\t\t\t\tsaveData.id = this.isEdit ? this.roomId : new Date().getTime().toString();\n\t\t\t\t\n\t\t\t\tconsole.log('保存会议室信息:', saveData);\n\t\t\t\t\n\t\t\t\t// 这里应该调用API保存会议室信息\n\t\t\t\t// 模拟保存成功\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: this.isEdit ? '编辑成功' : '添加成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 返回上一页\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t}, 1500);\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n.container {\n\tflex: 1;\n\tbackground-color: #f5f5f5;\n\tbackground-image: url('/static/images/bg.svg');\n\tbackground-size: cover;\n\tbackground-position: center;\n}\n\n.content {\n\tpadding: 30rpx;\n\tflex: 1;\n\tpadding-bottom: 150rpx; /* 为底部按钮留出空间 */\n}\n\n/* 编辑表单样式 */\n.edit-form {\n\tbackground-color: #ffffff;\n\tborder-radius: 16rpx;\n\tpadding: 30rpx;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n}\n\n.form-title {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 30rpx;\n\ttext-align: center;\n}\n\n.form-item {\n\tmargin-bottom: 30rpx;\n}\n\n.form-label {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tmargin-bottom: 10rpx;\n\tdisplay: block;\n}\n\n.form-input {\n\theight: 80rpx;\n\tborder: 1px solid #ddd;\n\tborder-radius: 8rpx;\n\tpadding: 0 20rpx;\n\tfont-size: 28rpx;\n\tbackground-color: #f9f9f9;\n}\n\n/* 状态选择器样式 */\n.status-selector {\n\tdisplay: flex;\n\tflex-direction: row;\n}\n\n.status-option {\n\tflex: 1;\n\theight: 80rpx;\n\tborder: 1px solid #ddd;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tbackground-color: #f9f9f9;\n}\n\n.status-option:first-child {\n\tborder-top-left-radius: 8rpx;\n\tborder-bottom-left-radius: 8rpx;\n}\n\n.status-option:last-child {\n\tborder-top-right-radius: 8rpx;\n\tborder-bottom-right-radius: 8rpx;\n\tborder-left: none;\n}\n\n.status-option.active {\n\tbackground-color: #8B0000;\n}\n\n.status-option.active text {\n\tcolor: #ffffff;\n}\n\n.status-option text {\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n/* 底部按钮样式 */\n.footer {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground-color: transparent;\n\tpadding: 20rpx 30rpx;\n\tdisplay: flex;\n\tjustify-content: center;\n}\n\n.button-group {\n\tdisplay: flex;\n\tflex-direction: row;\n\twidth: 100%;\n}\n\n.cancel-button, .save-button {\n\theight: 90rpx;\n\tborder-radius: 45rpx;\n\tjustify-content: center;\n\talign-items: center;\n\tdisplay: flex;\n\tflex: 1;\n\tmargin: 0 15rpx;\n}\n\n.cancel-button {\n\tbackground-color: #f5f5f5;\n\tborder: 1px solid #ddd;\n}\n\n.cancel-button text {\n\tcolor: #666;\n\tfont-size: 32rpx;\n}\n\n.save-button {\n\tbackground-color: #8B0000; /* 深红色 */\n\tbox-shadow: 0 4rpx 10rpx rgba(139, 0, 0, 0.3);\n}\n\n.save-button text {\n\tcolor: #ffffff;\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n}\n</style>", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751873716789\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}