{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/edit_new.vue?e036", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/edit_new.vue?5c4e", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/edit_new.vue?ff33", "uni-app:///pages/subPackage/template/edit_new.vue", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/edit_new.vue?25a0", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/edit_new.vue?bb1d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "templateName", "insertType", "backgroundType", "backgroundImageUrl", "backgroundColor", "canvasWidth", "canvasHeight", "previewContainerStyle", "canvasContext", "fontFamilies", "textColor", "fontWeight", "fontStyle", "textDecoration", "fontSize", "cardType", "colorOptions", "colorValues", "threeColorOptions", "threeColorValues", "sixColorOptions", "sixColorValues", "previewImagePath", "previewLoading", "showTemplatePopup", "templateFields", "name", "position", "company", "other", "canvasElements", "selectedElement", "dragging", "lastTouchX", "lastTouchY", "touchStartTime", "showPropertyPopup", "editingElement", "showFontPicker", "showColorPicker", "showElementButtons", "elementButtonsPosition", "x", "y", "buttonHideTimer", "scaleStartDistance", "rotateStartAngle", "computed", "mounted", "onShow", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "methods", "initCanvas", "console", "finalWidth", "finalHeight", "screenSize", "statusBarHeight", "safeAreaTop", "availableHeight", "otherElementsHeight", "containerAvailableHeight", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "maxCanvasHeight", "finalSize", "aspectRatio", "draw<PERSON><PERSON>vas", "drawBackground", "ctx", "drawE<PERSON><PERSON><PERSON><PERSON>", "element", "textTop", "width", "textHeight", "drawElement", "drawTextDecoration", "onCanvasTouchStart", "onCanvasTouchMove", "newX", "newY", "onCanvasTouchEnd", "openElementPropertyPopup", "closePropertyPopup", "onTextChange", "uni", "title", "icon", "getFontFamilyIndex", "onFontPickerConfirm", "value", "toggleFontWeight", "toggleFontStyle", "toggleTextDecoration", "toggleStrikethrough", "onFontSizeChange", "getElementAtPosition", "isPointInElement", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "getElementHeight", "addElement", "id", "type", "text", "isTemplate", "color", "fontFamily", "duration", "setInsertType", "showFixedTextInput", "content", "editable", "placeholderText", "success", "addFixedText", "addImage", "count", "sourceType", "src", "height", "rotation", "fail", "setBackgroundType", "selectBackgroundImage", "url", "checkSelectedBackground", "setCardType", "setSolidBackground", "itemList", "colorMap", "clearBackground", "getColorIndex", "getColorName", "onColorPickerConfirm", "debounce", "args", "func", "timeout", "saveTemplate", "performSave", "canvasSize", "elements", "el", "settings", "createdAt", "version", "confirmAddTemplateText", "key", "label", "offsetY", "fontSizeRatio", "fieldConfigs", "<PERSON><PERSON><PERSON>", "templateLabel", "templateData", "newElements", "addedCount", "onTemplatePopupClose", "batchUpdateTemplateFields", "updatedCount", "getElementsByTemplateKey", "removeElementsByTemplateKey", "getTemplateFieldsStats", "stats", "openPreview", "setTimeout", "generatePreviewImage", "canvasId", "closePreview", "savePreviewImage", "centerAllElements", "textWidth", "elementsWithSize", "currentY", "clearAllElements", "showElementOperationButtons", "updateElementButtonsPosition", "elementLeft", "elementRight", "elementTop", "elementBottom", "buttonX", "buttonY", "hideElementButtons", "editSelectedElement", "deleteSelectedElement", "zoomInImage", "Math", "zoomOutImage", "rotateImage"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACqC;;;AAG5F;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/EA;AAAA;AAAA;AAAA;AAAwtB,CAAgB,wrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2S5uB;EACAC;IACA;MACA;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MAAA;;MAEA;MACA;MACA;MACA;MACA;MACAC,eACA,mCACA;MACAC,cACA,iEACA;MAEA;MACAC,oBACA,iBACA;MACAC,mBACA,gCACA;MAEA;MACAC,kBACA,mCACA;MACAC,iBACA,iEACA;MACA;MACAC;MACAC;MACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACA;MACAC;MACAC;MACA;MACAC;MACAC;QAAAC;QAAAC;MAAA;MACAC;MAEA;MACAC;MACAC;IACA;EACA;EACA;EACAC,WAEA;EACAC;IACA;IACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;IACA;;IAEA;IACA;MACAC;MACA;IACA;EAGA;EACAC;IACA;IACAC;MAAA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;MACAC;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;;MAEA;MACAC;MACAC;;MAEA;MACA;QACAA;QACAD;MACA;;MAEA;MACA;MACA;;MAEA;MACAA;MACAC;MAEA;MACA;;MAEA;MACAF;QACAG;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;QACA;UACA;UACA;YACA;YACA;cACA;YACA;YACA;UACA;YACAZ;UACA;QACA;UACAA;QACA;MACA;IACA;IAEA;IACAa;MAAA;MACA;QACAb;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;UACA;QACA;QAEA;MACA;QACAA;MACA;IACA;IAEA;IACAc;MACA;MAEA;QACA;UACA;YACA;YACAC;UACA;YACA;YACAA;YACAA;UACA;UACA;QACA;UACA;UACAA;UACAA;UACA;QACA;UACA;UACA;QACA;UACA;UACAA;UACAA;UACA;MAAA;IAEA;IAEA;IACAC;MACA;MACA;MACA;MAEA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;;QAEA;QACA,8BACAC,2BACAC,aACAC,YACAC,gBACA;MACA;QACA;QACA;QACA,8BACAH,yCACAA,0CACAA,6BACAA,6BACA;MACA;IACA;IAEA;IACAI;MACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;;QAEA;QACA;UACA;UACA;YACA;UACA;QACA;UACArB;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;UACA;QACA;MACA;QACA;QACA;UACA;UAEA;YACA;YACA;;YAEA;YACA;;YAEA;YACA;;YAEA;YACA,6BACAiB,aACA,oBACA,qBACAA,eACAA,eACA;;YAEA;YACA;UACA;YACA;YACA,6BACAA,aACAA,+BACAA,gCACAA,eACAA,eACA;UACA;QACA;MACA;IACA;IAEA;IACAK;MACA;MAEA;MACA;MACA;;MAEA;MACA;MACA;MAEA;MACA;MAEA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEAC;MACA;MAEA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;QACA;;QAEA;QACA;QACA;;QAEA;QACA;QACA;QACA;QAEAC;QACAC;MACA;QACA;QACA;QACA;QACA;QACA;QAEAD;QACAC;MACA;QACA;QACA;QACA;QACA;QACA;QAEAD;QACAC;MACA;MAIA;MACA;MAEA;MACA;;MAEA;MACA;IACA;IAEAC;MACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAIA;IACAC;MAAA;MACA;MACA9B;MACA;QACA+B;UACAC;UACAC;QACA;QACA;QACA;UACA;QACA;QACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAIA;IACAC;MACA;QAAAC;MACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;IACA;IAGA;IACAC;MACA;MACA;QACA;QACA;UACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QAEA,yCACAvD,kCACAC,gBACAA;MACA;QACA;QACA,6CACAD,sCACAC,uCACAA;MACA;MACA;IACA;IAEA;IACAuD;MACA;QACA;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;QACA;QACA;UACA;UACA;UACA;YACAzB;UACA;YACA;YACAA;UACA;QACA;QACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACA0B;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;QACA;QACA;QACA;;QAEA;QACA;QACA;;QAEA;QACA;QACA;QACA;;QAEA;QACA;QACA;QAEA;UACAC;UACAC;UACAC;UACAC;UACA9D;UACAC;UACA8D;UACA3F;UACAH;UACAC;UACA8F;UACA7F;QACA;QAEA;QACA;QACA;;QAEA;QACAwE;UACAC;UACAC;UACAoB;QACA;MACA;IACA;IAEA;IACAC;MACAtD;MACA;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;MACA;MACA+B;QACAC;QACAC;QACAoB;MACA;MACA;MACA;IACA;IAEA;IACAE;MAAA;MACAxB;QACAC;QACAwB;QACAC;QACAC;QACAC;UACA;YACA;UACA;YACA5B;cACAC;cACAC;YACA;UACA;QACA;MACA;IACA;IAEA;IACA2B;MAAA;MACA;MACA;QACAZ;QACAC;QACAzF;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MAEA;MACA;MAEA;QACAuF;QACAC;QACAC;QACA7D;QACAC;QACA7B;QACA4F;QACAD;QACA9F;QACAC;QACAC;MACA;MACA;;MAEA;MACA;MACA;MAEAwE;QACAC;QACAC;QACAoB;MACA;IACA;IAEA;IACAQ;MAAA;MACA9B;QACA+B;QACAC;QACAJ;UACA;UACA;YACAZ;YACAC;YACAgB;YACA5E;YACAC;YACA8B;YACA8C;YACAC;UACA;UACA;UACA;QACA;QACAC;UACApC;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAmC;MACA;;MAEA;MACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;MAAA;MAGA;IACA;IAEA;IACAC;MACAtC;QACAuC;QACAH;UACAnE;UACA+B;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAsC;MACA;QACA;QACA;UACA;UACA;UACA;UACA;;UAEA;UACAxC;UAEAA;YACAC;YACAC;UACA;QACA;MACA;QACAjC;MACA;IACA;IAEA;IACAwE;MACA;;MAEA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;MAEAzC;QACAC;QACAC;MACA;IACA;IAEA;IACAwC;MAAA;MACA;MACA;MACA;MAEA;QACA;QACAC;QACAC;UACA;UACA;UACA;QACA;MACA;QACA;QACAD;QACAC;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;MAEA5C;QACA2C;QACAf;UACA;;UAEA;UACA;UACA;UACA;UAEA5B;YACAC;YACAC;UACA;QACA;MACA;IACA;IACA;IACA2C;MAAA;MACA7C;QACAC;QACAwB;QACAG;UACA;YACA;YACA;YACA;YAEA5B;cACAC;cACAC;YACA;UACA;QACA;MACA;IACA;IACA;IACA4C;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MACA;QAAA3C;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACA4C;MACA;MACA;QAAA;QAAA;UAAAC;QAAA;QACA;UACApF;UACAqF;QACA;QACArF;QACAsF;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACArD;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAwB;UACAG;YACA;cACA;YACA;UACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACA0B;MAAA;MACA;QACAjH;QACAkH;UACAnE;UACA8C;QACA;QACAsB;UAAA,uCACAC;YACA;YACApC;UAAA;QAAA,CACA;QACAqC;UACA7I;UACAC;UACAC;QACA;QACA4I;QACAC;MACA;;MAEA;MACA3F;MAEA+B;QACAC;QACAC;MACA;IACA;IAEA;IACA2D;MAAA;MACA;MACA,kEACA;QACA7D;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;;MAEA,oBACA;QAAA4D;QAAAC;QAAAC;QAAAC;QAAA3I;MAAA,GACA;QAAAwI;QAAAC;QAAAC;QAAAC;QAAA3I;MAAA,GACA;QAAAwI;QAAAC;QAAAC;QAAAC;QAAA3I;MAAA,GACA;QAAAwI;QAAAC;QAAAC;QAAAC;QAAA3I;MAAA,EACA;MAEA;MACA;MAEA4I;QACA;QACA;UACA;;UAEA;UACA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;UACA;UAEA;YACAlD;YACAC;YACAC;YACAC;YACAgD;YACAC;YACAC;YACAhH;YACAC;YACA8D;YACA3F;YACAH;YACAC;YACA8F;YACA7F;UACA;UACA8I;UACA;UACAC;QACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;;MAEA;MACA;MAEAvE;QACAC;QACAC;QACAoB;MACA;IACA;IACAkD;MACA;MACA;MACA;MACA;MACA;QACAnI;QACAC;QACAC;QACAC;MACA;IACA;IAGA;IACAiI;MACA;MACA;MAEA;QACA;UACA;UACA;YACA;YACAvF;YACA;YACAA;YACAwF;UACA;QACA;MACA;MAEA;QACA;QACA1E;UACAC;UACAC;UACAoB;QACA;MACA;MAEA;IACA;IAEA;IACAqD;MACA;QAAA,OACAzF;MAAA,EACA;IACA;IAEA;IACA0F;MACA;MACA;QAAA,OACA;MAAA,EACA;MAEA;MACA;QACA;QACA;QACA5E;UACAC;UACAC;UACAoB;QACA;MACA;MAEA;IACA;IAEA;IACAuD;MACA;MAEA;QACA;UACA;YACAC;cACA/C;cACAgC;cACAP;YACA;UACA;UACAsB;UACAA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;;MAEA;MACAC;QACA;QACA;MACA;IACA;IAEAC;MAAA;MACA;MACAjF;QACAkF;QACAtD;UACA;UACA;QACA;QACAQ;UACAnE;UACA;UACA+B;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEAiF;MACA;MACA;MACA;IACA;IAEAC;MACA;MACApF;QACAC;QACAC;MACA;IACA;IAEA;IACAmF;MAAA;MACA;QACArF;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;QAAA;MAAA;;MAEA;MACA;MACA;QACA;QACA;QACA,uCACAhB;UACAoG;UACAjG;QAAA;MAEA;MACA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;MACA;;MAEA;MACA;QACAW;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;MACAqF;QACA;QACA;QACArG;;QAEA;QACAA;QAEAsG;MACA;MAEA;MAEAxF;QACAC;QACAC;QACAoB;MACA;IACA;IACA;IACAmE;MAAA;MACAzF;QACAC;QACAwB;QACAG;UACA;YACA;YACA;YACA;YACA;YACA;YAEA5B;cACAC;cACAC;cACAoB;YACA;UACA;QACA;MACA;IACA;IAEA;IACAoE;MAAA;MACA;;MAEA;MACA;QACA5H;QACA;MACA;;MAEA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACA6H;MACA;MAEA;MACA;;MAEA;MACA;MAEA;QACA;QACAC;QACAC;QACAC;QACAC;MACA;QACA;QACAH;QACAC;QACAC;QACAC;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;QACAC;MACA;MACA;QACAC;MACA;;MAEA;MACA;QACAD;MACA;;MAEA;MACA;QACAC;MACA;MAEA;QAAA5I;QAAAC;MAAA;IACA;IAEA;IACA4I;MACA;MACA;QACApI;QACA;MACA;IACA;IAEA;IACAqI;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEApG;QACAC;QACAwB;QACAG;UACA;YACA;YACA;cAAA;YAAA;YACA;cACA;;cAEA;cACA;cACA;;cAEA;cACA;cAEA5B;gBACAC;gBACAC;gBACAoB;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACA+E;MACA;MAEApI;MAEA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MACA;MAEA;MACA;;MAEA;MACA;MACA;MAEA,sDACAqI;MACA,uDACAA;MAEArI;QACAmB;QACA8C;MACA;MAEA;IACA;IAEA;IACAqE;MACA;MAEAtI;MAEA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MAEA;MACA;MAEAA;QACAmB;QACA8C;MACA;MAEA;IACA;IAEA;IACAsE;MACA;MAEAvI;MAEA;;MAEA;MACA;MAEAA;QACAkE;MACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACx4DA;AAAA;AAAA;AAAA;AAAijC,CAAgB,i+BAAG,EAAC,C;;;;;;;;;;;ACArkC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/subPackage/template/edit_new.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/subPackage/template/edit_new.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./edit_new.vue?vue&type=template&id=4aed2b70&scoped=true&\"\nvar renderjs\nimport script from \"./edit_new.vue?vue&type=script&lang=js&\"\nexport * from \"./edit_new.vue?vue&type=script&lang=js&\"\nimport style0 from \"./edit_new.vue?vue&type=style&index=0&id=4aed2b70&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4aed2b70\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/subPackage/template/edit_new.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit_new.vue?vue&type=template&id=4aed2b70&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-picker/u-picker\" */ \"@/uni_modules/uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n    uSlider: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-slider/u-slider\" */ \"@/uni_modules/uview-ui/components/u-slider/u-slider.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.editingElement ? _vm.getFontFamilyIndex() : null\n  var m1 =\n    _vm.editingElement && _vm.editingElement.type === \"text\"\n      ? _vm.getColorName()\n      : null\n  var m2 = _vm.editingElement ? _vm.getColorIndex() : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showFontPicker = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.showFontPicker = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.showFontPicker = false\n    }\n    _vm.e3 = function ($event) {\n      _vm.showColorPicker = true\n    }\n    _vm.e4 = function ($event) {\n      _vm.showColorPicker = false\n    }\n    _vm.e5 = function ($event) {\n      _vm.showColorPicker = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit_new.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit_new.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"template-editor\">\n\t\t<!-- 模板预览区域 -->\n\t\t<view class=\"preview-container\">\n\t\t\t<canvas class=\"preview-canvas\" canvas-id=\"templateCanvas\" :canvas-width=\"canvasWidth\"\n\t\t\t\t:canvas-height=\"canvasHeight\" :style=\"{width: canvasWidth + 'px', height: canvasHeight + 'px'}\"\n\t\t\t\t@touchstart=\"onCanvasTouchStart\" @touchmove=\"onCanvasTouchMove\" @touchend=\"onCanvasTouchEnd\"></canvas>\n\n\t\t\t<!-- 元素操作按钮 -->\n\t\t\t<view v-if=\"selectedElement && showElementButtons\" class=\"element-buttons\"\n\t\t\t\t:style=\"{left: elementButtonsPosition.x + 'px', top: elementButtonsPosition.y + 'px'}\">\n\t\t\t\t<!-- 文字元素按钮 -->\n\t\t\t\t<template v-if=\"selectedElement.type === 'text'\">\n\t\t\t\t\t<view class=\"element-btn edit-btn\" @click=\"editSelectedElement\">\n\t\t\t\t\t\t<text class=\"btn-icon\">✏️</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"element-btn delete-btn\" @click=\"deleteSelectedElement\">\n\t\t\t\t\t\t<text class=\"btn-icon\">🗑️</text>\n\t\t\t\t\t</view>\n\t\t\t\t</template>\n\t\t\t\t\n\t\t\t\t<!-- 图片元素按钮 -->\n\t\t\t\t<template v-if=\"selectedElement.type === 'image'\">\n\t\t\t\t\t<view class=\"element-btn zoom-in-btn\" \n\t\t\t\t\t\************=\"zoomInImage\">\n\t\t\t\t\t\t<text class=\"btn-icon\">+</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"element-btn zoom-out-btn\" \n\t\t\t\t\t\************=\"zoomOutImage\">\n\t\t\t\t\t\t<text class=\"btn-icon\">-</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"element-btn rotate-btn\" \n\t\t\t\t\t\************=\"rotateImage\">\n\t\t\t\t\t\t<text class=\"btn-icon\">🔄</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"element-btn delete-btn\" @click.stop=\"deleteSelectedElement\">\n\t\t\t\t\t\t<text class=\"btn-icon\">🗑️</text>\n\t\t\t\t\t</view>\n\t\t\t\t</template>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 模板基础设置 -->\n\t\t<view class=\"template-settings\">\n\t\t\t<view class=\"setting-row\">\n\t\t\t\t<text class=\"setting-label\">模板名称</text>\n\t\t\t\t<input class=\"template-name-input\" v-model=\"templateName\" placeholder=\"新增模板\" />\n\t\t\t</view>\n\n\t\t\t<view class=\"setting-row\">\n\t\t\t\t<text class=\"setting-label\">对象插入</text>\n\t\t\t\t<view class=\"insert-options\">\n\t\t\t\t\t<view class=\"insert-btn\" :class=\"{active: insertType === 'template-text'}\"\n\t\t\t\t\t\t@click=\"setInsertType('template-text')\">\n\t\t\t\t\t\t模板文字\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"insert-btn\" :class=\"{active: insertType === 'fixed-text'}\"\n\t\t\t\t\t\t@click=\"setInsertType('fixed-text')\">\n\t\t\t\t\t\t固定文字\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"insert-btn\" :class=\"{active: insertType === 'image'}\" @click=\"setInsertType('image')\">\n\t\t\t\t\t\t图片\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 桌牌类型选择 -->\n\t\t<view class=\"background-settings\">\n\t\t\t<view class=\"setting-row\">\n\t\t\t\t<text class=\"setting-label\">桌牌类型</text>\n\t\t\t\t<view class=\"background-options\">\n\t\t\t\t\t<view class=\"bg-btn\" :class=\"{active: cardType === 'three-color'}\"\n\t\t\t\t\t\t@click=\"setCardType('three-color')\">\n\t\t\t\t\t\t三色桌牌\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"bg-btn\" :class=\"{active: cardType === 'six-color'}\"\n\t\t\t\t\t\t@click=\"setCardType('six-color')\">\n\t\t\t\t\t\t六色桌牌\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 背景选择功能 -->\n\t\t<view class=\"background-settings\">\n\t\t\t<view class=\"setting-row\">\n\t\t\t\t<text class=\"setting-label\">背景选择</text>\n\t\t\t\t<view class=\"background-options\">\n\t\t\t\t\t<view class=\"bg-btn\" :class=\"{active: backgroundType === 'select'}\"\n\t\t\t\t\t\t@click=\"setBackgroundType('select')\">\n\t\t\t\t\t\t选择背景\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"bg-btn\" :class=\"{active: backgroundType === 'solid'}\"\n\t\t\t\t\t\t@click=\"setBackgroundType('solid')\">\n\t\t\t\t\t\t纯色背景\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"bg-btn\" :class=\"{active: backgroundType === 'clear'}\"\n\t\t\t\t\t\t@click=\"setBackgroundType('clear')\">\n\t\t\t\t\t\t清除背景\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 批量操作 -->\n\t\t<view class=\"background-settings\">\n\t\t\t<view class=\"setting-row\">\n\t\t\t\t<text class=\"setting-label\">批量操作</text>\n\t\t\t\t<view class=\"background-options\">\n\t\t\t\t\t<view class=\"bg-btn\" @click=\"centerAllElements\">\n\t\t\t\t\t\t垂直水平居中\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"bg-btn danger-btn\" @click=\"clearAllElements\">\n\t\t\t\t\t\t清空画布\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 保存按钮 -->\n\t\t<view class=\"save-container\">\n\t\t\t<view class=\"preview-btn\" @click=\"openPreview\">\n\t\t\t\t<text class=\"btn-icon\">👁</text>\n\t\t\t\t<text class=\"btn-text\">预览</text>\n\t\t\t</view>\n\t\t\t<view class=\"save-btn\" @click=\"saveTemplate\">\n\t\t\t\t<text class=\"btn-icon\">💾</text>\n\t\t\t\t<text class=\"btn-text\">保存</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 模板字段选择弹窗 -->\n\t\t<!-- 模板文字弹窗 -->\n\t\t<u-popup :show=\"showTemplatePopup\" mode=\"bottom\" @close=\"onTemplatePopupClose\" :round=\"16\"\n\t\t\t:safeAreaInsetBottom=\"true\" closeable>\n\t\t\t<view class=\"template-popup-content\">\n\t\t\t\t<view class=\"popup-title\">添加模版文字</view>\n\n\t\t\t\t<view class=\"template-field\">\n\t\t\t\t\t<text class=\"field-label\">姓名</text>\n\t\t\t\t\t<input v-model=\"templateFields.name\" class=\"field-input\" placeholder=\"请输入姓名\" />\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"template-field\">\n\t\t\t\t\t<text class=\"field-label\">职位</text>\n\t\t\t\t\t<input v-model=\"templateFields.position\" class=\"field-input\" placeholder=\"请输入职位\" />\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"template-field\">\n\t\t\t\t\t<text class=\"field-label\">公司</text>\n\t\t\t\t\t<input v-model=\"templateFields.company\" class=\"field-input\" placeholder=\"请输入公司\" />\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"template-field\">\n\t\t\t\t\t<text class=\"field-label\">其他信息</text>\n\t\t\t\t\t<input v-model=\"templateFields.other\" class=\"field-input\" placeholder=\"请输入其他信息\" />\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"popup-btns\">\n\t\t\t\t\t<view class=\"popup-btn cancel\" @click=\"onTemplatePopupClose\">取消</view>\n\t\t\t\t\t<view class=\"popup-btn confirm\" @click=\"confirmAddTemplateText\">确认</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</u-popup>\n\n\t\t<!-- 预览弹窗 -->\n\t\t<u-popup :show=\"showPreview\" mode=\"center\" @close=\"closePreview\" :round=\"16\" :safeAreaInsetBottom=\"true\"\n\t\t\tcloseable>\n\t\t\t<view class=\"preview-popup-content\">\n\t\t\t\t<view class=\"popup-title\">模板预览</view>\n\n\t\t\t\t<view class=\"preview-image-container\">\n\t\t\t\t\t<image v-if=\"previewImagePath\" :src=\"previewImagePath\" mode=\"aspectFit\" class=\"preview-image\">\n\t\t\t\t\t</image>\n\t\t\t\t\t<view v-else-if=\"previewLoading\" class=\"preview-loading\">\n\t\t\t\t\t\t<text>生成预览中...</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-else class=\"preview-error\">\n\t\t\t\t\t\t<text>预览生成失败</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"popup-btns\">\n\t\t\t\t\t<view class=\"popup-btn cancel\" @click=\"closePreview\">关闭</view>\n\t\t\t\t\t<view class=\"popup-btn confirm\" @click=\"savePreviewImage\" v-if=\"previewImagePath\">保存图片</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</u-popup>\n\n\t\t<!-- 元素属性编辑弹窗 -->\n\t\t<u-popup :show=\"showPropertyPopup\" mode=\"bottom\" @close=\"closePropertyPopup\" :round=\"16\"\n\t\t\t:safeAreaInsetBottom=\"true\" closeable>\n\t\t\t<view class=\"property-popup-content\">\n\t\t\t\t<!-- 固定标题 -->\n\t\t\t\t<view class=\"property-title\">属性设置</view>\n\t\t\t\t<!-- 可滚动的内容区域 -->\n\t\t\t\t<scroll-view class=\"popup-scroll-content\" scroll-y=\"true\">\n\t\t\t\t\t<view class=\"edit-form-container\" v-if=\"editingElement\">\n\t\t\t\t\t\t<view class=\"edit-form\">\n\t\t\t\t\t\t\t<!-- 文字内容 -->\n\t\t\t\t\t\t\t<view class=\"property-row\" v-if=\"editingElement.type === 'text'\">\n\t\t\t\t\t\t\t\t<text class=\"property-label\">文字内容</text>\n\t\t\t\t\t\t\t\t<input class=\"text-input\" v-model=\"editingElement.text\" @input=\"onTextChange\"\n\t\t\t\t\t\t\t\t\tplaceholder=\"请输入文字内容\" />\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<!-- 文字字体 -->\n\t\t\t\t\t\t\t<view class=\"property-row\" v-if=\"editingElement.type === 'text'\">\n\t\t\t\t\t\t\t\t<text class=\"property-label\">文字字体</text>\n\t\t\t\t\t\t\t\t<view class=\"property-picker\" @click=\"showFontPicker = true\">\n\t\t\t\t\t\t\t\t\t<view class=\"picker-value\">{{editingElement.fontFamily || fontFamilies[0]}}\n\t\t\t\t\t\t\t\t\t\t<text class=\"picker-arrow\">▼</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 字体选择器 -->\n\t\t\t\t\t\t\t<u-picker \n\t\t\t\t\t\t\t\t:show=\"showFontPicker\" \n\t\t\t\t\t\t\t\t:columns=\"[fontFamilies]\" \n\t\t\t\t\t\t\t\t:defaultIndex=\"[getFontFamilyIndex()]\"\n\t\t\t\t\t\t\t\ttitle=\"选择字体\"\n\t\t\t\t\t\t\t\t@confirm=\"onFontPickerConfirm\"\n\t\t\t\t\t\t\t\t@cancel=\"showFontPicker = false\"\n\t\t\t\t\t\t\t\t@close=\"showFontPicker = false\"\n\t\t\t\t\t\t\t></u-picker>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 文字颜色 -->\n\t\t\t\t\t\t\t<view class=\"property-row\" v-if=\"editingElement.type === 'text'\">\n\t\t\t\t\t\t\t\t<text class=\"property-label\">文字颜色</text>\n\t\t\t\t\t\t\t\t<view class=\"property-picker\" @click=\"showColorPicker = true\">\n\t\t\t\t\t\t\t\t\t<view class=\"picker-value\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"color-preview-small\" :style=\"{backgroundColor: editingElement.color}\"></view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"color-name\">{{getColorName()}}</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"picker-arrow\">▼</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 颜色选择器 -->\n\t\t\t\t\t\t\t<u-picker \n\t\t\t\t\t\t\t\t:show=\"showColorPicker\" \n\t\t\t\t\t\t\t\t:columns=\"[colorOptions]\" \n\t\t\t\t\t\t\t\t:defaultIndex=\"[getColorIndex()]\"\n\t\t\t\t\t\t\t\ttitle=\"选择颜色\"\n\t\t\t\t\t\t\t\t@confirm=\"onColorPickerConfirm\"\n\t\t\t\t\t\t\t\t@cancel=\"showColorPicker = false\"\n\t\t\t\t\t\t\t\t@close=\"showColorPicker = false\"\n\t\t\t\t\t\t\t></u-picker>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 文字字形 -->\n\t\t\t\t\t\t\t<view class=\"property-row\" v-if=\"editingElement.type === 'text'\">\n\t\t\t\t\t\t\t\t<text class=\"property-label\">文字字形</text>\n\t\t\t\t\t\t\t\t<view class=\"style-buttons\">\n\t\t\t\t\t\t\t\t\t<view class=\"style-btn\" :class=\"{active: editingElement.fontWeight === 'bold'}\"\n\t\t\t\t\t\t\t\t\t\t@click=\"toggleFontWeight()\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"style-text\">B</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"style-btn\" :class=\"{active: editingElement.fontStyle === 'italic'}\"\n\t\t\t\t\t\t\t\t\t\t@click=\"toggleFontStyle()\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"style-text\">/</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"style-btn\"\n\t\t\t\t\t\t\t\t\t\t:class=\"{active: editingElement.textDecoration === 'underline'}\"\n\t\t\t\t\t\t\t\t\t\t@click=\"toggleTextDecoration()\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"style-text\">U</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"style-btn\"\n\t\t\t\t\t\t\t\t\t\t:class=\"{active: editingElement.textDecoration === 'line-through'}\"\n\t\t\t\t\t\t\t\t\t\t@click=\"toggleStrikethrough()\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"style-text\">S</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 文字字号 -->\n\t\t\t\t\t\t\t<view class=\"property-row\" v-if=\"editingElement.type === 'text'\">\n\t\t\t\t\t\t\t\t<text class=\"property-label\">文字字号</text>\n\t\t\t\t\t\t\t\t<view class=\"slider-container\">\n\t\t\t\t\t\t\t\t\t<u-slider \n\t\t\t\t\t\t\t\t\t\t:min=\"12\" \n\t\t\t\t\t\t\t\t\t\t:max=\"72\" \n\t\t\t\t\t\t\t\t\t\t:step=\"1\" \n\t\t\t\t\t\t\t\t\t\t:value=\"editingElement.fontSize\" \n\t\t\t\t\t\t\t\t\t\t:showValue=\"true\"\n\t\t\t\t\t\t\t\t\t\t@change=\"onFontSizeChange\"\n\t\t\t\t\t\t\t\t\t></u-slider>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t</u-popup> \n\t</view> \n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 模板基础信息\n\t\t\t\ttemplateName: '新增模板',\n\t\t\t\tinsertType: 'template-text',\n\t\t\t\tbackgroundType: 'select',\n\t\t\t\t// 背景相关\n\t\t\t\tbackgroundImageUrl: '',\n\t\t\t\tbackgroundColor: '#FFFFFF', \n\t\t\t\t// 画布相关\n\t\t\t\tcanvasWidth: 0,\n\t\t\t\tcanvasHeight: 0,\n\t\t\t\tpreviewContainerStyle:0,\n\t\t\t\tcanvasContext: null, \n\t\t\t\t// 文字属性\n\t\t\t\tfontFamilies: ['微软雅黑', '宋体', '黑体', '楷体', 'Arial', 'Times New Roman'],\n\t\t\t\ttextColor: '#000000',\n\t\t\t\tfontWeight: 'normal',\n\t\t\t\tfontStyle: 'normal',\n\t\t\t\ttextDecoration: 'none',\n\t\t\t\tfontSize: 30,\n\t\t\t\t// 电子桌牌类型\n\t\t\t\tcardType: 'six-color', // 'three-color' | 'six-color'\n\t\t\t\t\n\t\t\t\t//六色电子桌牌\n\t\t\t\t//黑、白、红、黄、蓝、绿\n\t\t\t\t//三色电子桌牌\n\t\t\t\t//黑白红 \n\t\t\t\t// 颜色选择器\n\t\t\t\tcolorOptions: [\n\t\t\t\t\t'黑色', '白色', '红色', '黄色', '蓝色', '绿色' \n\t\t\t\t],\n\t\t\t\tcolorValues: [\n\t\t\t\t\t'#000000', '#FFFFFF', '#FF0000', '#FFFF00', '#0000FF', '#00FF00' \n\t\t\t\t],\n\t\t\t\t\n\t\t\t\t// 三色桌牌颜色选项\n\t\t\t\tthreeColorOptions: [\n\t\t\t\t\t'黑色', '白色', '红色'\n\t\t\t\t],\n\t\t\t\tthreeColorValues: [\n\t\t\t\t\t'#000000', '#FFFFFF', '#FF0000'\n\t\t\t\t],\n\t\t\t\t\n\t\t\t\t// 六色桌牌颜色选项\n\t\t\t\tsixColorOptions: [\n\t\t\t\t\t'黑色', '白色', '红色', '黄色', '蓝色', '绿色'\n\t\t\t\t],\n\t\t\t\tsixColorValues: [\n\t\t\t\t\t'#000000', '#FFFFFF', '#FF0000', '#FFFF00', '#0000FF', '#00FF00'\n\t\t\t\t], \n\t\t\t\t// 预览相关\n\t\t\t\tpreviewImagePath: '',\n\t\t\t\tpreviewLoading: false,\n\t\t\t\tshowTemplatePopup: false, \n\t\t\t\t// 模板字段输入数据\n\t\t\t\ttemplateFields: {\n\t\t\t\t\tname: '',\n\t\t\t\t\tposition: '',\n\t\t\t\t\tcompany: '',\n\t\t\t\t\tother: ''\n\t\t\t},\n\t\t\t\t// 画布元素\n\t\t\t\tcanvasElements: [],\n\t\t\t\tselectedElement: null,\n\t\t\t\tdragging: false,\n\t\t\t\tlastTouchX: 0,\n\t\t\t\tlastTouchY: 0,\n\t\t\t\ttouchStartTime: 0, \n\t\t\t\t// 属性编辑弹窗\n\t\t\t\tshowPropertyPopup: false,\n\t\t\t\teditingElement: null,\n\t\t\t\t// picker控制\n\t\t\t\tshowFontPicker: false,\n\t\t\t\tshowColorPicker: false,\n\t\t\t\t// 元素操作按钮\n\t\t\t\tshowElementButtons: false,\n\t\t\t\telementButtonsPosition: { x: 0, y: 0 },\n\t\t\t\tbuttonHideTimer: null,\n\t\t\t\t\n\t\t\t\t// 缩放和旋转控制（保留基础变量用于其他功能）\n\t\t\t\tscaleStartDistance: 0,\n\t\t\t\trotateStartAngle: 0\n\t\t\t}\n\t\t},\n\t\t//\n\t\tcomputed: {\n\t\t\t \n\t\t},\n\t\tmounted() {\n\t\t\tthis.initCanvas()\n\t\t\t// 初始化防抖函数\n\t\t\tthis.debouncedDrawCanvas = this.debounce(this.drawCanvas, 16) // 约60fps\n\t\t},\n\t\t\n\t\tonShow() {\n\t\t\t// 检查是否有选择的背景图片\n\t\t\tthis.checkSelectedBackground()\n\t\t},\n\t\t\n\t\tbeforeDestroy() {\n\t\t\t// 清理资源\n\t\t\tif (this.canvasContext) {\n\t\t\t\tthis.canvasContext = null\n\t\t\t}\n\t\t\tthis.canvasElements = []\n\t\t\t\n\t\t\t// 清理定时器\n\t\t\tif (this.buttonHideTimer) {\n\t\t\t\tclearTimeout(this.buttonHideTimer)\n\t\t\t\tthis.buttonHideTimer = null\n\t\t\t}\n\t\t\t\n\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化画布\n\t\t\tinitCanvas() {\n\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\t\t\t\n\t\t\t\t// 获取状态栏高度和安全区域信息\n\t\t\t\tconst statusBarHeight = systemInfo.statusBarHeight || 0;\n\t\t\t\tconst safeAreaTop = systemInfo.safeArea ? systemInfo.safeArea.top : statusBarHeight;\n\t\t\t\t\n\t\t\t\t// 计算可用屏幕高度（减去状态栏、导航栏等系统UI占用的空间）\n\t\t\t\tconst availableHeight = systemInfo.windowHeight - safeAreaTop;\n\t\t\t\t\n\t\t\t\t// 考虑页面其他元素的空间占用\n\t\t\t\t// 预估其他UI元素总高度：模板设置(80px) + 背景设置(80px) + 批量操作(60px) + 属性面板(200px) + 保存按钮(75px) + 各种margin/padding(80px)\n\t\t\t\tconst otherElementsHeight = 80 + 80 + 60 + 200 + 75 + 80;\n\t\t\t\t\n\t\t\t\t// 计算画布容器可用高度\n\t\t\t\tconst containerAvailableHeight = Math.max(300, availableHeight - otherElementsHeight);\n\t\t\t\t\n\t\t\t\t// 考虑preview-container的margin(20px)、padding(16px)和border(4px)\n\t\t\t\tconst containerOverhead = 20 + 16 + 4; // margin: 10px*2 + padding: 8px*2 + border: 2px*2\n\t\t\t\tconst maxCanvasHeight = containerAvailableHeight - containerOverhead;\n\t\t\t\tconsole.log(\"maxCanvasHeight\",maxCanvasHeight);\n\t\t\t\t// 电子桌牌标准比例 800:480 = 5:3\n\t\t\t\tconst aspectRatio = 5 / 3;\n\t\t\t\t\n\t\t\t\t// 计算画布可用宽度（考虑容器的margin、padding和border）\n\t\t\t\tconst widthOverhead = 20 + 16 + 4; // margin: 10px*2 + padding: 8px*2 + border: 2px*2\n\t\t\t\tconst maxCanvasWidth = systemInfo.windowWidth - widthOverhead;\n\t\t\t\t\t\n\t\t\t\t// 根据比例和可用空间计算最终尺寸\n\t\t\t\tlet finalWidth, finalHeight;\n\t\t\t\t\n\t\t\t\t// 按宽度优先计算\n\t\t\t\tfinalWidth = maxCanvasWidth;\n\t\t\t\tfinalHeight = finalWidth / aspectRatio;\n\t\t\t\t\n\t\t\t\t// 如果高度超出限制，则按高度重新计算\n\t\t\t\tif (finalHeight > maxCanvasHeight) {\n\t\t\t\t\tfinalHeight = maxCanvasHeight;\n\t\t\t\t\tfinalWidth = finalHeight * aspectRatio;\n\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 确保最小尺寸，但要考虑屏幕限制\n\t\t\t\tconst minWidth = Math.min(320, maxCanvasWidth); // 最小宽度不超过可用宽度\n\t\t\t\tconst minHeight = minWidth / aspectRatio;\n\t\t\t\t\n\t\t\t\t// 确保尺寸不超出限制\n\t\t\t\tfinalWidth = Math.min(finalWidth, maxCanvasWidth);\n\t\t\t\tfinalHeight = Math.min(finalHeight, maxCanvasHeight);\n\t\t\t\t\n\t\t\t\tthis.canvasWidth = Math.max(minWidth, Math.floor(finalWidth));\n\t\t\t\tthis.canvasHeight = Math.max(minHeight, Math.floor(finalHeight));\n\t\t\t\t\t\n\t\t\t\t\t// 输出调试信息\n\t\t\t\tconsole.log('画布尺寸计算:', {\n\t\t\t\t\tscreenSize: `${systemInfo.windowWidth}x${systemInfo.windowHeight}`,\n\t\t\t\t\tstatusBarHeight,\n\t\t\t\t\tsafeAreaTop,\n\t\t\t\t\tavailableHeight,\n\t\t\t\t\totherElementsHeight,\n\t\t\t\t\tcontainerAvailableHeight,\n\t\t\t\t\tmaxCanvasWidth,\n\t\t\t\t\tmaxCanvasHeight,\n\t\t\t\t\tfinalSize: `${this.canvasWidth}x${this.canvasHeight}`,\n\t\t\t\t\taspectRatio: (this.canvasWidth / this.canvasHeight).toFixed(2)\n\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tthis.canvasContext = uni.createCanvasContext('templateCanvas', this);\n\t\t\t\t\t\tif (this.canvasContext) {\n\t\t\t\t\t\t\t// 设置canvas的实际渲染尺寸\n\t\t\t\t\t\t\tif (typeof this.canvasContext.setCanvasSize === 'function') {\n\t\t\t\t\t\t\t\tthis.canvasContext.setCanvasSize(this.canvasWidth, this.canvasHeight);\n\t\t\t\t\t\t\t}  \n\t\t\t\t\t\t\tthis.drawCanvas();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconsole.error('Canvas context creation failed');\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.error('Canvas initialization error:', error);\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\t\n\t\t\t// 绘制画布 - 优化版本\n\t\t\tdrawCanvas() {\n\t\t\t\tif (!this.canvasContext) {\n\t\t\t\t\tconsole.warn('Canvas context not available');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (!this.canvasWidth || !this.canvasHeight) {\n\t\t\t\t\tconsole.warn('Canvas dimensions not set');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t// 清空画布\n\t\t\t\t\tthis.canvasContext.clearRect(0, 0, this.canvasWidth, this.canvasHeight)\n\t\t\t\t\t\n\t\t\t\t\t// 绘制背景\n\t\t\t\t\tthis.drawBackground()\n\t\t\t\t\t\n\t\t\t\t\t// 绘制选中元素的高亮边框\n\t\t\t\t\tif (this.selectedElement) {\n\t\t\t\t\t\tthis.drawElementHighlight(this.selectedElement)\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 绘制元素\n\t\t\t\t\tthis.canvasElements.forEach(element => {\n\t\t\t\t\t\tthis.drawElement(element)\n\t\t\t\t\t})\n\t\t\t\t\t\n\t\t\t\t\tthis.canvasContext.draw()\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('Canvas drawing error:', error);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 绘制背景\n\t\t\tdrawBackground() {\n\t\t\t\tconst ctx = this.canvasContext\n\t\t\t\t\n\t\t\t\tswitch (this.backgroundType) {\n\t\t\t\t\tcase 'select':\n\t\t\t\t\t\tif (this.backgroundImageUrl) {\n\t\t\t\t\t\t\t// 绘制背景图片\n\t\t\t\t\t\t\tctx.drawImage(this.backgroundImageUrl, 0, 0, this.canvasWidth, this.canvasHeight)\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 没有选择图片时使用默认白色背景\n\t\t\t\t\t\t\tctx.setFillStyle('#FFFFFF')\n\t\t\t\t\t\t\tctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak\n\t\t\t\t\tcase 'solid':\n\t\t\t\t\t\t// 绘制纯色背景\n\t\t\t\t\t\tctx.setFillStyle(this.backgroundColor || '#FFFFFF')\n\t\t\t\t\t\tctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)\n\t\t\t\t\t\tbreak\n\t\t\t\t\tcase 'clear':\n\t\t\t\t\t\t// 透明背景，不绘制任何背景\n\t\t\t\t\t\tbreak\n\t\t\t\t\tdefault:\n\t\t\t\t\t\t// 默认白色背景\n\t\t\t\t\t\tctx.setFillStyle('#FFFFFF')\n\t\t\t\t\t\tctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)\n\t\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 绘制元素高亮边框\n\t\t\tdrawElementHighlight(element) {\n\t\t\t\t// 设置高亮边框样式\n\t\t\t\tthis.canvasContext.setStrokeStyle('#007AFF')\n\t\t\t\tthis.canvasContext.setLineWidth(2)\n\t\t\t\t\n\t\t\t\tif (element.type === 'text') {\n\t\t\t\t\tconst width = this.getElementWidth(element)\n\t\t\t\t\tconst fontSize = element.fontSize || this.fontSize\n\t\t\t\t\t\n\t\t\t\t\t// 计算文本边界，考虑基线位置\n\t\t\t\t\t// element.y 是基线位置，文本顶部在基线上方 fontSize * 0.8\n\t\t\t\t\tconst textTop = element.y - fontSize * 0.8\n\t\t\t\t\tconst textBottom = element.y + fontSize * 0.2\n\t\t\t\t\tconst textHeight = textBottom - textTop\n\t\t\t\t\t\n\t\t\t\t\t// 绘制高亮边框，添加5px边距\n\t\t\t\t\tthis.canvasContext.strokeRect(\n\t\t\t\t\t\telement.x - width/2 - 5, \n\t\t\t\t\t\ttextTop - 5, \n\t\t\t\t\t\twidth + 10, \n\t\t\t\t\t\ttextHeight + 10\n\t\t\t\t\t)\n\t\t\t\t} else if (element.type === 'image') {\n\t\t\t\t\t// 图片元素的高亮边框\n\t\t\t\t\tconst padding = 5 // 边框内边距\n\t\t\t\t\tthis.canvasContext.strokeRect(\n\t\t\t\t\t\telement.x - element.width/2 - padding,\n\t\t\t\t\t\telement.y - element.height/2 - padding,\n\t\t\t\t\t\telement.width + padding * 2,\n\t\t\t\t\t\telement.height + padding * 2\n\t\t\t\t\t)\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 绘制元素\n\t\t\tdrawElement(element) {\n\t\t\t\tif (element.type === 'text') {\n\t\t\t\t\tthis.canvasContext.setFillStyle(element.color || this.textColor)\n\t\t\t\t\tthis.canvasContext.setFontSize(element.fontSize || this.fontSize)\n\t\t\t\t\t\n\t\t\t\t\t// 构建字体样式字符串\n\t\t\t\t\tconst fontWeight = element.fontWeight || 'normal'\n\t\t\t\t\tconst fontStyle = element.fontStyle || 'normal'\n\t\t\t\t\tconst fontFamily = element.fontFamily || this.fontFamilies[0]\n\t\t\t\t\tconst fontSize = element.fontSize || this.fontSize\n\t\t\t\t\t\n\t\t\t\t\t// 设置字体样式 - 使用uni-app canvas的正确方式\n\t\t\t\t\t// 注意：uni-app的canvas可能不完全支持CSS font属性，需要分别设置\n\t\t\t\t\tthis.canvasContext.setFontSize(fontSize)\n\t\t\t\t\t\n\t\t\t\t\t// 尝试设置字体样式（某些平台可能不支持）\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst fontString = `${fontStyle} ${fontWeight} ${fontSize}px ${fontFamily}`\n\t\t\t\t\t\tif (this.canvasContext.font !== undefined) {\n\t\t\t\t\t\t\tthis.canvasContext.font = fontString\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.warn('设置字体样式失败，使用默认样式:', e)\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 设置文本对齐方式\n\t\t\t\t\tthis.canvasContext.textAlign = 'center'\n\t\t\t\t\t\n\t\t\t\t\t// 绘制文本\n\t\t\t\t\tthis.canvasContext.fillText(element.text, element.x, element.y)\n\t\t\t\t\t\n\t\t\t\t\t// 处理文本装饰（下划线和删除线）\n\t\t\t\t\tif (element.textDecoration && element.textDecoration !== 'none') {\n\t\t\t\t\t\tthis.drawTextDecoration(element)\n\t\t\t\t\t}\n\t\t\t\t} else if (element.type === 'image') {\n\t\t\t\t// 绘制图片（支持旋转）\n\t\t\t\tif (element.src) {\n\t\t\t\t\tconst rotation = element.rotation || 0\n\t\t\t\t\t\n\t\t\t\t\tif (rotation !== 0) {\n\t\t\t\t\t\t// 保存当前状态\n\t\t\t\t\t\tthis.canvasContext.save()\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 移动到图片中心点\n\t\t\t\t\t\tthis.canvasContext.translate(element.x, element.y)\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 旋转\n\t\t\t\t\t\tthis.canvasContext.rotate(rotation * Math.PI / 180)\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 绘制图片（相对于旋转后的坐标系）\n\t\t\t\t\t\tthis.canvasContext.drawImage(\n\t\t\t\t\t\t\telement.src,\n\t\t\t\t\t\t\t-element.width / 2,\n\t\t\t\t\t\t\t-element.height / 2,\n\t\t\t\t\t\t\telement.width,\n\t\t\t\t\t\t\telement.height\n\t\t\t\t\t\t)\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 恢复状态\n\t\t\t\t\t\tthis.canvasContext.restore()\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 无旋转时的正常绘制\n\t\t\t\t\t\tthis.canvasContext.drawImage(\n\t\t\t\t\t\t\telement.src,\n\t\t\t\t\t\t\telement.x - element.width / 2,\n\t\t\t\t\t\t\telement.y - element.height / 2,\n\t\t\t\t\t\t\telement.width,\n\t\t\t\t\t\t\telement.height\n\t\t\t\t\t\t)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 绘制文本装饰（下划线和删除线）\n\t\t\tdrawTextDecoration(element) {\n\t\t\t\tif (!element.textDecoration || element.textDecoration === 'none') return\n\t\t\t\t\n\t\t\t\tconst fontSize = element.fontSize || this.fontSize\n\t\t\t\tconst textWidth = this.getElementWidth(element)\n\t\t\t\tconst lineThickness = Math.max(1, fontSize / 20) // 线条粗细根据字体大小调整\n\t\t\t\t\n\t\t\t\t// 设置线条样式\n\t\t\t\tthis.canvasContext.setStrokeStyle(element.color || this.textColor)\n\t\t\t\tthis.canvasContext.setLineWidth(lineThickness)\n\t\t\t\t\n\t\t\t\tconst startX = element.x - textWidth / 2\n\t\t\t\tconst endX = element.x + textWidth / 2\n\t\t\t\t\n\t\t\t\tif (element.textDecoration === 'underline') {\n\t\t\t\t\t// 下划线位置：文本基线下方\n\t\t\t\t\tconst lineY = element.y + fontSize * 0.1\n\t\t\t\t\tthis.canvasContext.beginPath()\n\t\t\t\t\tthis.canvasContext.moveTo(startX, lineY)\n\t\t\t\t\tthis.canvasContext.lineTo(endX, lineY)\n\t\t\t\t\tthis.canvasContext.stroke()\n\t\t\t\t} else if (element.textDecoration === 'line-through') {\n\t\t\t\t\t// 删除线位置：文本中间\n\t\t\t\t\tconst lineY = element.y - fontSize * 0.3\n\t\t\t\t\tthis.canvasContext.beginPath()\n\t\t\t\t\tthis.canvasContext.moveTo(startX, lineY)\n\t\t\t\t\tthis.canvasContext.lineTo(endX, lineY)\n\t\t\t\t\tthis.canvasContext.stroke()\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 画布触摸事件\n\t\t\tonCanvasTouchStart(e) {\n\t\t\t\tconst touch = e.touches[0]\n\t\t\t\tthis.lastTouchX = touch.x\n\t\t\t\tthis.lastTouchY = touch.y\n\t\t\t\tthis.touchStartTime = Date.now()\n\t\t\t\t\n\t\t\t\t// 检查是否点击了元素\n\t\t\t\tconst element = this.getElementAtPosition(touch.x, touch.y)\n\t\t\t\tif (element) {\n\t\t\t\t\tthis.selectedElement = element\n\t\t\t\t\tthis.dragging = true\n\t\t\t\t\t// 显示操作按钮\n\t\t\t\t\tthis.showElementOperationButtons(element)\n\t\t\t\t\t// 重绘画布以显示高亮边框\n\t\t\t\t\tthis.drawCanvas()\n\t\t\t\t} else {\n\t\t\t\t\t// 点击空白区域，隐藏按钮\n\t\t\t\t\tthis.hideElementButtons()\n\t\t\t\t\tthis.selectedElement = null\n\t\t\t\t\t// 重绘画布以移除高亮边框\n\t\t\t\t\tthis.drawCanvas()\n\t\t\t\t}  \n\t\t\t},\n\t\t\t\n\t\t\tonCanvasTouchMove(e) {\n\t\t\t\tif (!this.dragging || !this.selectedElement) return\n\t\t\t\t\n\t\t\t\tconst touch = e.touches[0]\n\t\t\t\tconst deltaX = touch.x - this.lastTouchX\n\t\t\t\tconst deltaY = touch.y - this.lastTouchY\n\t\t\t\t\n\t\t\t\t// 计算新位置\n\t\t\t\tlet newX = this.selectedElement.x + deltaX\n\t\t\t\tlet newY = this.selectedElement.y + deltaY\n\t\t\t\t\n\t\t\t\t// 边界检测 - 防止元素超出画布范围\n\t\t\t\tconst elementWidth = this.getElementWidth(this.selectedElement)\n\t\t\t\tconst elementHeight = this.getElementHeight(this.selectedElement)\n\t\t\t\t\n\t\t\t\t// 改进边界检测，确保元素完全在画布内\n\t\t\t\t// 设置合理的边距，防止元素贴边或超出\n\t\t\t\tconst margin = 10\n\t\t\t\t\n\t\t\t\t// 对于文本元素，考虑文本对齐方式的影响\n\t\t\t\tif (this.selectedElement.type === 'text') {\n\t\t\t\t\tconst fontSize = this.selectedElement.fontSize || this.fontSize\n\t\t\t\t\t\n\t\t\t\t\t// 文本居中对齐\n\t\t\t\t\tconst minX = margin + elementWidth / 2\n\t\t\t\t\tconst maxX = this.canvasWidth - margin - elementWidth / 2\n\t\t\t\t\t\n\t\t\t\t\t// 考虑文本基线偏移，newY是基线位置\n\t\t\t\t\t// 文本顶部需要 fontSize * 0.8 的空间，底部需要 fontSize * 0.2 的空间\n\t\t\t\t\tconst minY = margin + fontSize * 0.8\n\t\t\t\t\tconst maxY = this.canvasHeight - margin - fontSize * 0.2\n\t\t\t\t\t\n\t\t\t\t\tnewX = Math.max(minX, Math.min(newX, maxX))\n\t\t\t\t\tnewY = Math.max(minY, Math.min(newY, maxY))\n\t\t\t\t} else if (this.selectedElement.type === 'image') {\n\t\t\t\t\t// 图片元素的边界检测\n\t\t\t\t\tconst minX = margin + elementWidth / 2\n\t\t\t\t\tconst maxX = this.canvasWidth - margin - elementWidth / 2\n\t\t\t\t\tconst minY = margin + elementHeight / 2\n\t\t\t\t\tconst maxY = this.canvasHeight - margin - elementHeight / 2\n\t\t\t\t\t\n\t\t\t\t\tnewX = Math.max(minX, Math.min(newX, maxX))\n\t\t\t\t\tnewY = Math.max(minY, Math.min(newY, maxY))\n\t\t\t\t} else {\n\t\t\t\t\t// 其他类型元素的边界检测\n\t\t\t\t\tconst minX = margin + elementWidth / 2\n\t\t\t\t\tconst maxX = this.canvasWidth - margin - elementWidth / 2\n\t\t\t\t\tconst minY = margin + elementHeight / 2\n\t\t\t\t\tconst maxY = this.canvasHeight - margin - elementHeight / 2\n\t\t\t\t\t\n\t\t\t\t\tnewX = Math.max(minX, Math.min(newX, maxX))\n\t\t\t\t\tnewY = Math.max(minY, Math.min(newY, maxY))\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t\n\t\t\t\t\n\t\t\t\tthis.selectedElement.x = newX\n\t\t\t\tthis.selectedElement.y = newY\n\t\t\t\t\n\t\t\t\tthis.lastTouchX = touch.x\n\t\t\t\tthis.lastTouchY = touch.y\n\t\t\t\t\n\t\t\t\t// 使用防抖渲染提高性能\n\t\t\t\tthis.debouncedDrawCanvas()\n\t\t\t},\n\t\t\t\n\t\t\tonCanvasTouchEnd(e) {\n\t\t\t\tconst touchEndTime = Date.now()\n\t\t\t\tconst touchDuration = touchEndTime - this.touchStartTime\n\t\t\t\t\n\t\t\t\t// 如果是拖拽结束，更新按钮位置\n\t\t\t\tif (this.dragging && this.selectedElement) {\n\t\t\t\t\tthis.updateElementButtonsPosition(this.selectedElement)\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.dragging = false\n\t\t\t},\n\t\t\t\n\t\t\t// 打开元素属性编辑弹窗\n\t\t\topenElementPropertyPopup() {\n\t\t\t\tif (!this.selectedElement) return\n\t\t\t\t\n\t\t\t\t// 设置当前编辑的元素\n\t\t\t\tthis.editingElement = this.selectedElement\n\t\t\t\t\n\t\t\t\t// 显示弹窗\n\t\t\t\tthis.showPropertyPopup = true\n\t\t\t},\n\t\t\t\n\t\t\t// 关闭属性编辑弹窗\n\t\t\tclosePropertyPopup() {\n\t\t\t\tthis.showPropertyPopup = false\n\t\t\t\tthis.editingElement = null\n\t\t\t},\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t// 文字内容编辑\n\t\t\tonTextChange(e) {\n\t\t\t\tconst newText = e.detail.value.trim();\n\t\t\t\tconsole.log(this.editingElement.text);\n\t\t\t\tif (newText === '') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '文字内容不能为空',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\t// 恢复原来的文字\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.editingElement.text = this.editingElement.text || '文字'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tthis.editingElement.text = newText\n\t\t\t\tthis.drawCanvas()\n\t\t\t},\n\t\t\t\n\t\t\t// 获取字体索引\n\t\t\tgetFontFamilyIndex() {\n\t\t\t\tif (!this.editingElement || !this.editingElement.fontFamily) return 0\n\t\t\t\treturn this.fontFamilies.indexOf(this.editingElement.fontFamily) || 0\n\t\t\t},\n\t\t\t\n\n\t\t\t\n\t\t\t// 属性编辑相关方法（立即生效）\n\t\t\tonFontPickerConfirm(e) {\n\t\t\t\tconst { indexs, value } = e\n\t\t\t\tif (this.editingElement && this.editingElement.type === 'text') {\n\t\t\t\t\tthis.editingElement.fontFamily = value[0]\n\t\t\t\t\tthis.drawCanvas()\n\t\t\t\t}\n\t\t\t\tthis.showFontPicker = false\n\t\t\t},\n\n\t\t\ttoggleFontWeight() {\n\t\t\t\tif (!this.editingElement || this.editingElement.type !== 'text') return\n\t\t\t\tthis.editingElement.fontWeight = this.editingElement.fontWeight === 'bold' ? 'normal' : 'bold'\n\t\t\t\tthis.drawCanvas()\n\t\t\t},\n\t\t\ttoggleFontStyle() {\n\t\t\t\tif (!this.editingElement || this.editingElement.type !== 'text') return\n\t\t\t\tthis.editingElement.fontStyle = this.editingElement.fontStyle === 'italic' ? 'normal' : 'italic'\n\t\t\t\tthis.drawCanvas()\n\t\t\t},\n\t\t\ttoggleTextDecoration() {\n\t\t\t\tif (!this.editingElement || this.editingElement.type !== 'text') return\n\t\t\t\tif (this.editingElement.textDecoration === 'underline') {\n\t\t\t\t\tthis.editingElement.textDecoration = 'none'\n\t\t\t\t} else {\n\t\t\t\t\tthis.editingElement.textDecoration = 'underline'\n\t\t\t\t}\n\t\t\t\tthis.drawCanvas()\n\t\t\t},\n\t\t\ttoggleStrikethrough() {\n\t\t\t\tif (!this.editingElement || this.editingElement.type !== 'text') return\n\t\t\t\tif (this.editingElement.textDecoration === 'line-through') {\n\t\t\t\t\tthis.editingElement.textDecoration = 'none'\n\t\t\t\t} else {\n\t\t\t\t\tthis.editingElement.textDecoration = 'line-through'\n\t\t\t\t}\n\t\t\t\tthis.drawCanvas()\n\t\t\t},\n\t\t\tonFontSizeChange(value) {\n\t\t\t\tif (this.editingElement && this.editingElement.type === 'text') {\n\t\t\t\t\tthis.editingElement.fontSize = value\n\t\t\t\t\tthis.drawCanvas()\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t\n\t\t\t// 获取指定位置的元素 - 改进的碰撞检测算法\n\t\t\tgetElementAtPosition(x, y) {\n\t\t\t\t// 从后往前遍历，优先选择最上层的元素\n\t\t\t\tfor (let i = this.canvasElements.length - 1; i >= 0; i--) {\n\t\t\t\t\tconst element = this.canvasElements[i]\n\t\t\t\t\tif (this.isPointInElement(x, y, element)) {\n\t\t\t\t\t\treturn element\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn null\n\t\t\t},\n\t\t\t\n\t\t\t// 检查点是否在元素内\n\t\t\tisPointInElement(x, y, element) {\n\t\t\t\tif (element.type === 'text') {\n\t\t\t\t\tconst textWidth = this.getElementWidth(element)\n\t\t\t\t\tconst textHeight = this.getElementHeight(element)\n\t\t\t\t\tconst fontSize = element.fontSize || this.fontSize\n\t\t\t\t\t\n\t\t\t\t\t// 考虑文本基线偏移，element.y是基线位置\n\t\t\t\t\t// 文本顶部位置 = element.y - fontSize * 0.8\n\t\t\t\t\t// 文本底部位置 = element.y + fontSize * 0.2\n\t\t\t\t\tconst textTop = element.y - fontSize * 0.8\n\t\t\t\t\tconst textBottom = element.y + fontSize * 0.2\n\t\t\t\t\t\n\t\t\t\t\treturn x >= element.x - textWidth/2 && \n\t\t\t\t\t\t   x <= element.x + textWidth/2 && \n\t\t\t\t\t\t   y >= textTop && \n\t\t\t\t\t\t   y <= textBottom\n\t\t\t\t} else if (element.type === 'image') {\n\t\t\t\t\t// 图片元素的碰撞检测\n\t\t\t\t\treturn x >= element.x - element.width/2 && \n\t\t\t\t\t\t   x <= element.x + element.width/2 && \n\t\t\t\t\t\t   y >= element.y - element.height/2 && \n\t\t\t\t\t\t   y <= element.y + element.height/2\n\t\t\t\t}\n\t\t\t\treturn false\n\t\t\t},\n\t\t\t\n\t\t\t// 获取元素宽度\n\t\t\tgetElementWidth(element) {\n\t\t\t\tif (element.type === 'text') {\n\t\t\t\t\tconst fontSize = element.fontSize || this.fontSize\n\t\t\t\t\tconst text = element.text || ''\n\t\t\t\t\t\n\t\t\t\t\t// 对于模板字段，使用字段名称长度来估算宽度\n\t\t\t\t\tif (element.isTemplate && element.fieldName) {\n\t\t\t\t\t\treturn element.fieldName.length * fontSize * 0.8\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 改进的文本宽度计算\n\t\t\t\t\t// 考虑中文字符和英文字符的不同宽度\n\t\t\t\t\tlet width = 0\n\t\t\t\t\tfor (let i = 0; i < text.length; i++) {\n\t\t\t\t\t\tconst char = text.charAt(i);\n\t\t\t\t\t\t// 中文字符、全角字符宽度约为fontSize\n\t\t\t\t\t\tif (/[\\u4e00-\\u9fa5\\uff00-\\uffef]/.test(char)) {\n\t\t\t\t\t\t\twidth += fontSize * 0.9;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 英文字符、数字等宽度约为fontSize的0.5-0.6倍\n\t\t\t\t\t\t\twidth += fontSize * 0.55\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn Math.max(width, fontSize * 0.5) // 最小宽度\n\t\t\t\t} else if (element.type === 'image') {\n\t\t\t\t\treturn element.width || 100\n\t\t\t\t}\n\t\t\t\treturn 50 // 默认宽度\n\t\t\t},\n\t\t\t\n\t\t\t// 获取元素高度\n\t\t\tgetElementHeight(element) {\n\t\t\t\tif (element.type === 'text') {\n\t\t\t\t\treturn element.fontSize || this.fontSize\n\t\t\t\t} else if (element.type === 'image') {\n\t\t\t\t\treturn element.height || 100\n\t\t\t\t}\n\t\t\t\treturn 20 // 默认高度\n\t\t\t}, \n\t\t\t//添加元素 - 增强版本\n\t\t\taddElement(x, y) {\n\t\t\t\t// 如果是模板文字类型，通过按钮打开弹窗，这里不处理\n\t\t\t\tif (this.insertType === 'template-text') {\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (this.insertType === 'fixed-text') {\n\t\t\t\t\t// 改进的边界验证\n\t\t\t\t\tconst estimatedWidth = '固定文字'.length * this.fontSize * 0.6\n\t\t\t\t\tconst margin = 10\n\t\t\t\t\t\n\t\t\t\t\t// 文本居中对齐\n\t\t\t\t\tconst minX = margin + estimatedWidth / 2\n\t\t\t\t\tconst maxX = this.canvasWidth - margin - estimatedWidth / 2\n\t\t\t\t\t\n\t\t\t\t\t// 考虑文本基线偏移，Y坐标应该是基线位置\n\t\t\t\t\t// 文本顶部需要 fontSize * 0.8 的空间，底部需要 fontSize * 0.2 的空间\n\t\t\t\t\tconst minY = margin + this.fontSize * 0.8\n\t\t\t\t\tconst maxY = this.canvasHeight - margin - this.fontSize * 0.2\n\t\t\t\t\t\n\t\t\t\t\t// 使用验证后的位置\n\t\t\t\t\tconst snappedX = Math.max(minX, Math.min(x, maxX))\n\t\t\t\t\tconst snappedY = Math.max(minY, Math.min(y, maxY))\n\t\t\t\t\t\n\t\t\t\t\tconst newElement = {\n\t\t\t\t\t\tid: Date.now(),\n\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\ttext: '固定文字',\n\t\t\t\t\t\tisTemplate: false,\n\t\t\t\t\t\tx: snappedX,\n\t\t\t\t\t\ty: snappedY,\n\t\t\t\t\t\tcolor: this.textColor,\n\t\t\t\t\t\tfontSize: this.fontSize,\n\t\t\t\t\t\tfontWeight: this.fontWeight,\n\t\t\t\t\t\tfontStyle: this.fontStyle,\n\t\t\t\t\t\tfontFamily: this.fontFamilies[0],\n\t\t\t\t\t\ttextDecoration: this.textDecoration\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.canvasElements.push(newElement)\n\t\t\t\t\tthis.selectedElement = newElement // 自动选中新添加的元素\n\t\t\t\t\tthis.drawCanvas()\n\t\t\t\t\t\n\t\t\t\t\t// 提供用户反馈\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '元素已添加',\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t//设置插入类型\n\t\t\tsetInsertType(type) {\n\t\t\t\tconsole.log('setInsertType called with:', type)\n\t\t\t\tthis.insertType = type\n\t\t\t\t// 如果选择模板文字，显示字段选择弹窗\n\t\t\t\tif (type === 'template-text') {\n\t\t\t\t\t// 打开模板文字弹窗\n\t\t\t\t\tthis.showTemplatePopup = true\n\t\t\t\t} else if (type === 'fixed-text') {\n\t\t\t\t// 处理固定文字逻辑\n\t\t\t\tthis.showFixedTextInput()\n\t\t\t\t} else if (type === 'image') {\n\t\t\t\t\t// 处理图片逻辑\n\t\t\t\t\tthis.addImage()\n\t\t\t\t}\n\t\t\t\t// 添加用户反馈\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: `已选择: ${type === 'template-text' ? '模板文字' : type === 'fixed-text' ? '固定文字' : '图片'}`,\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 1000\n\t\t\t\t})\n\t\t\t\t// 强制更新视图\n\t\t\tthis.$forceUpdate()\n\t\t},\n\t\t\t\n\t\t// 显示固定文字输入弹窗\n\t\t\tshowFixedTextInput() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '添加固定文字',\n\t\t\t\t\tcontent: '请输入文字内容',\n\t\t\t\t\teditable: true,\n\t\t\t\t\tplaceholderText: '请输入文字内容',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm && res.content && res.content.trim()) {\n\t\t\t\t\t\t\tthis.addFixedText(res.content.trim())\n\t\t\t\t\t\t} else if (res.confirm && (!res.content || !res.content.trim())) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '文字内容不能为空',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\t\n\t\t\t// 添加固定文字\n\t\t\taddFixedText(text = '固定文字') {\n\t\t\t\t// 创建临时元素用于计算宽度\n\t\t\t\tconst tempElement = {\n\t\t\t\t\ttype: 'text',\n\t\t\t\t\ttext: text,\n\t\t\t\t\tfontSize: this.fontSize\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 计算文字宽度\n\t\t\t\tconst textWidth = this.getElementWidth(tempElement)\n\t\t\t\tconst margin = 10\n\t\t\t\t\n\t\t\t\t// 计算安全的初始位置\n\t\t\t\tconst minX = margin + textWidth / 2\n\t\t\t\tconst maxX = this.canvasWidth - margin - textWidth / 2\n\t\t\t\tconst minY = margin + this.fontSize * 0.8\n\t\t\t\tconst maxY = this.canvasHeight - margin - this.fontSize * 0.2\n\t\t\t\t\n\t\t\t\t// 使用画布中心位置，但确保在边界内\n\t\t\t\tconst centerX = this.canvasWidth / 2\n\t\t\t\tconst centerY = this.canvasHeight / 2\n\t\t\t\t\n\t\t\t\tconst safeX = Math.max(minX, Math.min(centerX, maxX))\n\t\t\t\tconst safeY = Math.max(minY, Math.min(centerY, maxY))\n\t\t\t\t\n\t\t\t\tconst newElement = {\n\t\t\t\t\tid: Date.now(),\n\t\t\t\t\ttype: 'text',\n\t\t\t\t\ttext: text,\n\t\t\t\t\tx: safeX,\n\t\t\t\t\ty: safeY,\n\t\t\t\t\tfontSize: this.fontSize,\n\t\t\t\t\tfontFamily: this.fontFamilies[0],\n\t\t\t\t\tcolor: this.textColor,\n\t\t\t\t\tfontWeight: this.fontWeight,\n\t\t\t\t\tfontStyle: this.fontStyle,\n\t\t\t\t\ttextDecoration: this.textDecoration\n\t\t\t\t}\n\t\t\t\tthis.canvasElements.push(newElement)\n\t\t\t\t\n\t\t\t\t// 自动选中新添加的元素\n\t\t\t\tthis.selectedElement = newElement\n\t\t\t\tthis.drawCanvas()\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '文字已添加',\n\t\t\t\t\ticon: 'success',\n\t\t\t\t\tduration: 1000\n\t\t\t\t})\n\t\t\t},\n\t\t\t\n\t\t\t// 添加图片\n\t\t\taddImage() {\n\t\t\t\tuni.chooseImage({\n\t\t\t\t\tcount: 1,\n\t\t\t\t\tsourceType: ['album', 'camera'],\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconst tempFilePath = res.tempFilePaths[0]\n\t\t\t\t\t\tconst newElement = {\n\t\t\t\t\tid: Date.now(),\n\t\t\t\t\ttype: 'image',\n\t\t\t\t\tsrc: tempFilePath,\n\t\t\t\t\tx: this.canvasWidth / 2,\n\t\t\t\t\ty: this.canvasHeight / 2,\n\t\t\t\t\twidth: 100,\n\t\t\t\t\theight: 100,\n\t\t\t\t\trotation: 0\n\t\t\t\t}\n\t\t\t\t\t\tthis.canvasElements.push(newElement)\n\t\t\t\t\t\tthis.drawCanvas()\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '选择图片失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t//设置背景类型\n\t\t\tsetBackgroundType(type) {\n\t\t\t\tthis.backgroundType = type\n\t\t\t\t\n\t\t\t\t// 根据背景类型执行相应操作\n\t\t\t\tswitch(type) {\n\t\t\t\t\tcase 'select':\n\t\t\t\t\t\tthis.selectBackgroundImage()\n\t\t\t\t\t\tbreak\n\t\t\t\t\tcase 'solid':\n\t\t\t\t\t\tthis.setSolidBackground()\n\t\t\t\t\t\tbreak\n\t\t\t\t\tcase 'clear':\n\t\t\t\t\t\tthis.clearBackground()\n\t\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.drawCanvas()\n\t\t\t},\n\t\t\t\n\t\t\t// 选择背景图片\n\t\t\tselectBackgroundImage() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/subPackage/template/backgroundList?select=true',\n\t\t\t\t\tfail: (error) => {\n\t\t\t\t\t\tconsole.error('跳转背景选择页面失败:', error)\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '打开背景选择失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\t\n\t\t\t// 检查选择的背景图片\n\t\t\tcheckSelectedBackground() {\n\t\t\t\ttry {\n\t\t\t\t\tconst selectedBackground = uni.getStorageSync('selectedBackgroundImage')\n\t\t\t\t\tif (selectedBackground) {\n\t\t\t\t\t\t// 设置背景图片\n\t\t\t\t\t\tthis.backgroundImageUrl = selectedBackground\n\t\t\t\t\t\tthis.backgroundType = 'select'\n\t\t\t\t\t\tthis.drawCanvas()\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 清除存储，避免重复使用\n\t\t\t\t\t\tuni.removeStorageSync('selectedBackgroundImage')\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '背景设置成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('检查背景选择失败:', error)\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 设置桌牌类型\n\t\t\tsetCardType(type) {\n\t\t\t\tthis.cardType = type\n\t\t\t\t\n\t\t\t\t// 更新颜色选择器选项\n\t\t\t\tif (type === 'three-color') {\n\t\t\t\t\tthis.colorOptions = [...this.threeColorOptions]\n\t\t\t\t\tthis.colorValues = [...this.threeColorValues]\n\t\t\t\t} else {\n\t\t\t\t\tthis.colorOptions = [...this.sixColorOptions]\n\t\t\t\t\tthis.colorValues = [...this.sixColorValues]\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: `已切换到${type === 'three-color' ? '三色' : '六色'}桌牌模式`,\n\t\t\t\t\ticon: 'success'\n\t\t\t\t})\n\t\t\t},\n\t\t\t\n\t\t\t// 设置纯色背景\n\t\t\tsetSolidBackground() {\n\t\t\t\t// 根据桌牌类型提供不同的背景选择\n\t\t\t\tlet itemList = []\n\t\t\t\tlet colorMap = {}\n\t\t\t\t\n\t\t\t\tif (this.cardType === 'three-color') {\n\t\t\t\t\t// 三色桌牌：黑、白、红\n\t\t\t\t\titemList = ['黑色背景', '白色背景', '红色背景']\n\t\t\t\t\tcolorMap = {\n\t\t\t\t\t\t0: '#000000',\n\t\t\t\t\t\t1: '#FFFFFF', \n\t\t\t\t\t\t2: '#FF0000'\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// 六色桌牌：黑、白、红、黄、蓝、绿\n\t\t\t\t\titemList = ['黑色背景', '白色背景', '红色背景', '黄色背景', '蓝色背景', '绿色背景']\n\t\t\t\t\tcolorMap = {\n\t\t\t\t\t\t0: '#000000',\n\t\t\t\t\t\t1: '#FFFFFF',\n\t\t\t\t\t\t2: '#FF0000',\n\t\t\t\t\t\t3: '#FFFF00',\n\t\t\t\t\t\t4: '#0000FF',\n\t\t\t\t\t\t5: '#00FF00'\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tuni.showActionSheet({\n\t\t\t\t\titemList: itemList,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconst lastIndex = itemList.length - 1\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 使用预设颜色\n\t\t\t\t\t\tthis.backgroundColor = colorMap[res.tapIndex]\n\t\t\t\t\t\tthis.backgroundImageUrl = ''\n\t\t\t\t\t\tthis.drawCanvas()\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '背景设置成功',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t},\n\t\t\t// 清除背景\n\t\t\tclearBackground() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '清除背景',\n\t\t\t\t\tcontent: '确定要清除当前背景吗？',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tthis.backgroundImageUrl = ''\n\t\t\t\t\t\t\tthis.backgroundColor = 'transparent'\n\t\t\t\t\t\t\tthis.drawCanvas()\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '背景已清除',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}, \n\t\t\t// 颜色选择相关方法\n\t\t\tgetColorIndex() {\n\t\t\t\tif (!this.editingElement || !this.editingElement.color) return 0\n\t\t\t\tconst index = this.colorValues.indexOf(this.editingElement.color)\n\t\t\t\treturn index >= 0 ? index : 0\n\t\t\t},\n\t\t\t\n\t\t\tgetColorName() {\n\t\t\t\tif (!this.editingElement || !this.editingElement.color) return this.colorOptions[0]\n\t\t\t\tconst index = this.colorValues.indexOf(this.editingElement.color)\n\t\t\t\treturn index >= 0 ? this.colorOptions[index] : this.colorOptions[0]\n\t\t\t},\n\t\t\t\n\t\t\tonColorPickerConfirm(e) {\n\t\t\t\tconst { indexs, value } = e\n\t\t\t\tif (this.editingElement && this.editingElement.type === 'text') {\n\t\t\t\t\tconst colorIndex = indexs[0]\n\t\t\t\t\tthis.editingElement.color = this.colorValues[colorIndex]\n\t\t\t\t\tthis.drawCanvas()\n\t\t\t\t}\n\t\t\t\tthis.showColorPicker = false\n\t\t\t},\n\t\t\t\n\t\t\t// 防抖函数\n\t\t\tdebounce(func, wait) {\n\t\t\t\tlet timeout\n\t\t\t\treturn function executedFunction(...args) {\n\t\t\t\t\tconst later = () => {\n\t\t\t\t\t\tclearTimeout(timeout)\n\t\t\t\t\t\tfunc.apply(this, args)\n\t\t\t\t\t}\n\t\t\t\t\tclearTimeout(timeout)\n\t\t\t\t\ttimeout = setTimeout(later, wait)\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 保存模板 - 增强版本\n\t\t\tsaveTemplate() {\n\t\t\t\t// 验证模板数据\n\t\t\t\tif (!this.templateName.trim()) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入模板名称',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (this.canvasElements.length === 0) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: '模板中没有任何元素，确定要保存吗？',\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tthis.performSave()\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t} \n\t\t\t\tthis.performSave()\n\t\t\t},\n\t\t\t\n\t\t\t// 执行保存操作\n\t\t\tperformSave() {\n\t\t\t\tconst templateData = {\n\t\t\t\t\tname: this.templateName.trim(),\n\t\t\t\t\tcanvasSize: {\n\t\t\t\t\t\twidth: this.canvasWidth,\n\t\t\t\t\t\theight: this.canvasHeight\n\t\t\t\t\t},\n\t\t\t\t\telements: this.canvasElements.map(el => ({\n\t\t\t\t\t\t...el,\n\t\t\t\t\t\t// 确保所有属性都被保存\n\t\t\t\t\t\tfontFamily: this.fontFamilies[this.fontFamilyIndex]\n\t\t\t\t\t})),\n\t\t\t\t\tsettings: {\n\t\t\t\t\t\tbackgroundType: this.backgroundType,\n\t\t\t\t\t\tbackgroundImageUrl: this.backgroundImageUrl,\n\t\t\t\t\t\tbackgroundColor: this.backgroundColor\n\t\t\t\t\t},\n\t\t\t\t\tcreatedAt: new Date().toISOString(),\n\t\t\t\t\tversion: '1.0'\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 这里可以添加实际的保存逻辑，如发送到服务器\n\t\t\t\tconsole.log('保存模板数据:', templateData)\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '模板保存成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t})\n\t\t\t},\n\t\t\t\n\t\t\t// 模板文字弹窗相关方法\n\t\t\tconfirmAddTemplateText() {\n\t\t\t\t// 检查是否有输入内容\n\t\t\t\tif (!this.templateFields.name && !this.templateFields.position && \n\t\t\t\t\t!this.templateFields.company && !this.templateFields.other) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请至少输入一项内容',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 获取画布中心位置\n\t\t\t\tconst centerX = this.canvasWidth / 2;\n\t\t\t\tconst centerY = this.canvasHeight / 2;\n\t\t\t\t\n\t\t\t\t// 为每个有内容的字段创建独立的元素对象，设置字体层次和居中布局\n\t\t\t\t// 根据画布高度动态调整间距，适应不同屏幕尺寸\n\t\t\t\tconst baseSpacing = Math.min(this.canvasHeight * 0.15, 40) // 基础间距不超过40px\n\t\t\t\t\n\t\t\t\t// 根据画布大小动态计算字体粗细\n\t\t\t\tconst canvasArea = this.canvasWidth * this.canvasHeight\n\t\t\t\tconst baseFontWeight = Math.min(Math.max(canvasArea / 50000, 0.5), 1.2) // 基础字体粗细系数\n\t\t\t\t\n\t\t\t\tconst fieldConfigs = [\n\t\t\t\t\t{ key: 'name', label: '姓名', offsetY: -baseSpacing * 1.5, fontSizeRatio: 1.3, fontWeight: Math.round(700 * baseFontWeight).toString() },\n\t\t\t\t\t{ key: 'position', label: '职位', offsetY: -baseSpacing * 0.5, fontSizeRatio: 1.0, fontWeight: Math.round(600 * baseFontWeight).toString() },\n\t\t\t\t\t{ key: 'company', label: '公司', offsetY: baseSpacing * 0.5, fontSizeRatio: 0.9, fontWeight: Math.round(500 * baseFontWeight).toString()},\n\t\t\t\t\t{ key: 'other', label: '其他', offsetY: baseSpacing * 1.5, fontSizeRatio: 0.8, fontWeight: Math.round(400 * baseFontWeight).toString()}\n\t\t\t\t]\n\t\t\t\t\n\t\t\t\tlet addedCount = 0\n\t\t\t\tconst newElements = []\n\t\t\t\t\n\t\t\t\tfieldConfigs.forEach(config => {\n\t\t\t\t\tconst fieldValue = this.templateFields[config.key]\n\t\t\t\t\tif (fieldValue && fieldValue.trim()) {\n\t\t\t\t\t\tconst fontSize = Math.round(this.fontSize * config.fontSizeRatio)\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 计算文本尺寸用于边界检测\n\t\t\t\t\t\tconst textWidth = fieldValue.length * fontSize * 0.6\n\t\t\t\t\t\tconst textHeight = fontSize * this.heightStretch\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 边界检测，确保文本不超出画布\n\t\t\t\t\t\tconst margin = 20\n\t\t\t\t\t\tconst minX = margin + textWidth / 2\n\t\t\t\t\t\tconst maxX = this.canvasWidth - margin - textWidth / 2\n\t\t\t\t\t\t// 考虑文本基线偏移，Y坐标应该是基线位置\n\t\t\t\t\t\tconst minY = margin + fontSize * 0.8\n\t\t\t\t\t\tconst maxY = this.canvasHeight - margin - fontSize * 0.2\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 应用边界约束\n\t\t\t\t\t\tconst safeX = Math.max(minX, Math.min(centerX, maxX))\n\t\t\t\t\t\tconst safeY = Math.max(minY, Math.min(centerY + config.offsetY, maxY))\n\t\t\t\t\t\t\n\t\t\t\t\t\tconst newElement = {\n\t\t\t\t\t\tid: Date.now() + addedCount,\n\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\ttext: fieldValue,\n\t\t\t\t\t\tisTemplate: true,\n\t\t\t\t\t\ttemplateKey: config.key,\n\t\t\t\t\t\ttemplateLabel: config.label,\n\t\t\t\t\t\ttemplateData: { [config.key]: fieldValue },\n\t\t\t\t\t\tx: safeX,\n\t\t\t\t\t\ty: safeY,\n\t\t\t\t\t\tcolor: this.textColor,\n\t\t\t\t\t\tfontSize: fontSize,\n\t\t\t\t\t\tfontWeight: config.fontWeight,\n\t\t\t\t\t\tfontStyle: this.fontStyle,\n\t\t\t\t\t\tfontFamily: this.fontFamilies[0],\n\t\t\t\t\t\ttextDecoration: this.textDecoration\n\t\t\t\t\t}\n\t\t\t\t\t\tnewElements.push(newElement)\n\t\t\t\t\t\tthis.canvasElements.push(newElement)\n\t\t\t\t\t\taddedCount++\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t\n\t\t\t\t// 选中最后一个添加的元素\n\t\t\t\tif (newElements.length > 0) {\n\t\t\t\t\tthis.selectedElement = newElements[newElements.length - 1]\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.drawCanvas()\n\t\t\t\t\n\t\t\t\t// 关闭弹窗并清空输入\n\t\t\t\tthis.onTemplatePopupClose()\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: `已添加${addedCount}个模板字段`,\n\t\t\t\t\ticon: 'success',\n\t\t\t\t\tduration: 1500\n\t\t\t\t})\n\t\t\t}, \n\t\t\tonTemplatePopupClose() {\n\t\t\t\t// 关闭弹窗时重置插入类型\n\t\t\t\tthis.showTemplatePopup = false;\n\t\t\t\tthis.insertType = '';\n\t\t\t\t// 清空输入字段\n\t\t\t\tthis.templateFields = {\n\t\t\t\t\tname: '',\n\t\t\t\t\tposition: '',\n\t\t\t\t\tcompany: '',\n\t\t\t\t\tother: ''\n\t\t\t\t}\n\t\t\t}, \n\n\t\t\t\n\t\t\t// 批量修改模板字段方法\n\t\t\tbatchUpdateTemplateFields(updates) {\n\t\t\t\t// updates 格式: { key: newValue, key2: newValue2 }\n\t\t\t\tlet updatedCount = 0\n\t\t\t\t\n\t\t\t\tthis.canvasElements.forEach(element => {\n\t\t\t\t\tif (element.isTemplate && element.templateKey) {\n\t\t\t\t\t\tconst newValue = updates[element.templateKey]\n\t\t\t\t\t\tif (newValue !== undefined && newValue !== null) {\n\t\t\t\t\t\t\t// 更新元素文本\n\t\t\t\t\t\t\telement.text = newValue\n\t\t\t\t\t\t\t// 更新模板数据\n\t\t\t\t\t\t\telement.templateData[element.templateKey] = newValue\n\t\t\t\t\t\t\tupdatedCount++\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t\n\t\t\t\tif (updatedCount > 0) {\n\t\t\t\t\tthis.drawCanvas()\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: `已更新${updatedCount}个字段`,\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn updatedCount\n\t\t\t},\n\t\t\t\n\t\t\t// 根据模板key获取所有相关元素\n\t\t\tgetElementsByTemplateKey(templateKey) {\n\t\t\t\treturn this.canvasElements.filter(element => \n\t\t\t\t\telement.isTemplate && element.templateKey === templateKey\n\t\t\t\t)\n\t\t\t},\n\t\t\t\n\t\t\t// 删除指定模板key的所有元素\n\t\t\tremoveElementsByTemplateKey(templateKey) {\n\t\t\t\tconst initialLength = this.canvasElements.length\n\t\t\t\tthis.canvasElements = this.canvasElements.filter(element => \n\t\t\t\t\t!(element.isTemplate && element.templateKey === templateKey)\n\t\t\t\t)\n\t\t\t\t\n\t\t\t\tconst removedCount = initialLength - this.canvasElements.length\n\t\t\t\tif (removedCount > 0) {\n\t\t\t\t\tthis.selectedElement = null\n\t\t\t\t\tthis.drawCanvas()\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: `已删除${removedCount}个${templateKey}字段`,\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn removedCount\n\t\t\t},\n\t\t\t\n\t\t\t// 获取所有模板字段的统计信息\n\t\t\tgetTemplateFieldsStats() {\n\t\t\t\tconst stats = {}\n\t\t\t\t\n\t\t\t\tthis.canvasElements.forEach(element => {\n\t\t\t\t\tif (element.isTemplate && element.templateKey) {\n\t\t\t\t\t\tif (!stats[element.templateKey]) {\n\t\t\t\t\t\t\tstats[element.templateKey] = {\n\t\t\t\t\t\t\t\tcount: 0,\n\t\t\t\t\t\t\t\tlabel: element.templateLabel || element.templateKey,\n\t\t\t\t\t\t\t\telements: []\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tstats[element.templateKey].count++\n\t\t\t\t\t\tstats[element.templateKey].elements.push(element)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t\n\t\t\t\treturn stats\n\t\t\t},\n\t\t\t\n\t\t\t// 预览弹窗相关方法\n\t\t\topenPreview() {\n\t\t\t\t// 生成预览图片\n\t\t\t\tthis.previewLoading = true\n\t\t\t\tthis.showPreview = true\n\t\t\t\t\n\t\t\t\t// 模拟生成预览图片的过程\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t// 这里应该调用画布截图API生成预览图\n\t\t\t\t\tthis.generatePreviewImage()\n\t\t\t\t}, 500)\n\t\t\t},\n\t\t\t\n\t\t\tgeneratePreviewImage() {\n\t\t\t\t// 使用canvas生成预览图片\n\t\t\t\tuni.canvasToTempFilePath({\n\t\t\t\t\tcanvasId: 'templateCanvas',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tthis.previewImagePath = res.tempFilePath\n\t\t\t\t\t\tthis.previewLoading = false\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('生成预览图片失败:', err)\n\t\t\t\t\t\tthis.previewLoading = false\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '预览生成失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}, this)\n\t\t\t},\n\t\t\t\n\t\t\tclosePreview() {\n\t\t\t\tthis.showPreview = false\n\t\t\t\tthis.previewImagePath = ''\n\t\t\t\tthis.previewLoading = false\n\t\t\t},\n\t\t\t\n\t\t\tsavePreviewImage() {\n\t\t\t\t// 保存预览图片的逻辑\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '图片保存功能待实现',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t},\n\t\t\t\n\t\t\t//批量操作方法\n\t\t\tcenterAllElements() {\n\t\t\t\tif (this.canvasElements.length === 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '没有可操作的元素',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 按Y坐标排序元素\n\t\t\t\tconst sortedElements = [...this.canvasElements].sort((a, b) => a.y - b.y)\n\t\t\t\t\n\t\t\t\t// 计算元素尺寸和边界\n\t\t\t\tconst margin = 20 // 边界留白\n\t\t\t\tconst elementsWithSize = sortedElements.map(element => {\n\t\t\t\t\tconst textWidth = this.getElementWidth(element)\n\t\t\t\t\tconst textHeight = element.fontSize || this.fontSize\n\t\t\t\t\treturn {\n\t\t\t\t\t\t...element,\n\t\t\t\t\t\ttextWidth,\n\t\t\t\t\t\ttextHeight\n\t\t\t\t\t}\n\t\t\t\t}) \n\t\t\t\t// 计算可用空间和分布参数\n\t\t\t\tconst maxTextWidth = Math.max(...elementsWithSize.map(e => e.textWidth))\n\t\t\t\tconst totalTextHeight = elementsWithSize.reduce((sum, e) => sum + e.textHeight, 0)\n\t\t\t\tconst availableWidth = this.canvasWidth - 2 * margin\n\t\t\t\tconst availableHeight = this.canvasHeight - 2 * margin\n\t\t\t\t\n\t\t\t\t// 检查是否有足够空间\n\t\t\t\tif (maxTextWidth > availableWidth) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '元素宽度超出画布范围',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 计算垂直分布\n\t\t\t\tconst canvasCenterX = this.canvasWidth / 2\n\t\t\t\tconst remainingHeight = availableHeight - totalTextHeight\n\t\t\t\tconst spacing = elementsWithSize.length > 1 ? remainingHeight / (elementsWithSize.length + 1) : remainingHeight / 2\n\t\t\t\t\n\t\t\t\t// 重新分布元素，确保不超出边界\n\t\t\t\tlet currentY = margin + spacing\n\t\t\t\telementsWithSize.forEach((element) => {\n\t\t\t\t\t// 水平居中，确保不超出边界\n\t\t\t\t\tconst halfWidth = element.textWidth / 2\n\t\t\t\t\telement.x = Math.max(margin + halfWidth, Math.min(canvasCenterX, this.canvasWidth - margin - halfWidth))\n\t\t\t\t\t\n\t\t\t\t\t// 垂直分布，确保不超出边界\n\t\t\t\t\telement.y = Math.max(margin + element.textHeight / 2, Math.min(currentY + element.textHeight / 2, this.canvasHeight - margin - element.textHeight / 2))\n\t\t\t\t\t\n\t\t\t\t\tcurrentY += element.textHeight + spacing\n\t\t\t\t})\n\t\t\t\t\n\t\t\t\tthis.drawCanvas()\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '元素已垂直分布',\n\t\t\t\t\ticon: 'success',\n\t\t\t\t\tduration: 1000\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 清空画布\n\t\t\tclearAllElements() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认清空',\n\t\t\t\t\tcontent: '确定要清空画布上的所有元素吗？此操作不可撤销。',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tconst elementCount = this.canvasElements.length\n\t\t\t\t\t\t\tthis.canvasElements = []\n\t\t\t\t\t\t\tthis.selectedElement = null\n\t\t\t\t\t\t\tthis.hideElementButtons()\n\t\t\t\t\t\t\tthis.drawCanvas()\n\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: `已清空${elementCount}个元素`,\n\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 显示元素操作按钮\n\t\t\tshowElementOperationButtons(element) {\n\t\t\t\tif (!element) return\n\t\t\t\t\n\t\t\t\t// 清除之前的定时器\n\t\t\t\tif (this.buttonHideTimer) {\n\t\t\t\t\tclearTimeout(this.buttonHideTimer)\n\t\t\t\t\tthis.buttonHideTimer = null\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 计算按钮位置\n\t\t\t\tthis.updateElementButtonsPosition(element)\n\t\t\t\t\n\t\t\t\t// 显示按钮\n\t\t\t\tthis.showElementButtons = true\n\t\t\t\t\n\t\t\t\t// 设置自动隐藏定时器\n\t\t\t\tthis.buttonHideTimer = setTimeout(() => {\n\t\t\t\t\tthis.hideElementButtons()\n\t\t\t\t}, 3000) // 3秒后自动隐藏\n\t\t\t},\n\t\t\t\n\t\t\t// 更新按钮位置\n\t\t\tupdateElementButtonsPosition(element) {\n\t\t\t\tif (!element) return\n\t\t\t\t\n\t\t\t\tconst elementWidth = this.getElementWidth(element)\n\t\t\t\tconst elementHeight = this.getElementHeight(element)\n\t\t\t\t\n\t\t\t\t// 计算元素边界\n\t\t\t\tlet elementLeft, elementTop, elementRight, elementBottom\n\t\t\t\t\n\t\t\t\tif (element.type === 'text') {\n\t\t\t\t\tconst fontSize = element.fontSize || this.fontSize\n\t\t\t\t\telementLeft = element.x - elementWidth / 2\n\t\t\t\t\telementRight = element.x + elementWidth / 2\n\t\t\t\t\telementTop = element.y - fontSize * 0.8\n\t\t\t\t\telementBottom = element.y + fontSize * 0.2\n\t\t\t\t} else if (element.type === 'image') {\n\t\t\t\t\t// 图片元素边界\n\t\t\t\t\telementLeft = element.x - element.width / 2\n\t\t\t\t\telementRight = element.x + element.width / 2\n\t\t\t\t\telementTop = element.y - element.height / 2\n\t\t\t\t\telementBottom = element.y + element.height / 2\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 按钮尺寸\n\t\t\tconst buttonSize = 32\n\t\t\tconst buttonSpacing = 8\n\t\t\t\n\t\t\t// 根据元素类型计算按钮数量\n\t\t\tconst buttonCount = element.type === 'image' ? 3 : 2\n\t\t\tconst totalButtonWidth = buttonSize * buttonCount + buttonSpacing * (buttonCount - 1)\n\t\t\t\n\t\t\t// 计算按钮位置（在元素右上角）\n\t\t\tlet buttonX = elementRight + 5\n\t\t\tlet buttonY = elementTop - buttonSize - 5\n\t\t\t\t\n\t\t\t\t// 边界检测，确保按钮不超出画布\n\t\t\t\tif (buttonX + totalButtonWidth > this.canvasWidth) {\n\t\t\t\t\tbuttonX = elementLeft - totalButtonWidth - 5\n\t\t\t\t}\n\t\t\t\tif (buttonY < 0) {\n\t\t\t\t\tbuttonY = elementBottom + 5\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 确保按钮不超出左边界\n\t\t\t\tif (buttonX < 0) {\n\t\t\t\t\tbuttonX = 5\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 确保按钮不超出下边界\n\t\t\t\tif (buttonY + buttonSize > this.canvasHeight) {\n\t\t\t\t\tbuttonY = this.canvasHeight - buttonSize - 5\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.elementButtonsPosition = { x: buttonX, y: buttonY }\n\t\t\t},\n\t\t\t\n\t\t\t// 隐藏元素操作按钮\n\t\t\thideElementButtons() {\n\t\t\t\tthis.showElementButtons = false\n\t\t\t\tif (this.buttonHideTimer) {\n\t\t\t\t\tclearTimeout(this.buttonHideTimer)\n\t\t\t\t\tthis.buttonHideTimer = null\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 编辑选中元素\n\t\t\teditSelectedElement() {\n\t\t\t\tif (!this.selectedElement) return\n\t\t\t\t\n\t\t\t\t// 隐藏操作按钮\n\t\t\t\tthis.hideElementButtons()\n\t\t\t\t\n\t\t\t\t// 打开属性编辑弹窗\n\t\t\t\tthis.openElementPropertyPopup()\n\t\t\t},\n\t\t\t\n\t\t\t// 删除选中元素\n\t\t\tdeleteSelectedElement() {\n\t\t\t\tif (!this.selectedElement) return\n\t\t\t\t\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认删除',\n\t\t\t\t\tcontent: '确定要删除这个元素吗？',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t// 找到元素索引并删除\n\t\t\t\t\t\t\tconst index = this.canvasElements.findIndex(el => el.id === this.selectedElement.id)\n\t\t\t\t\t\t\tif (index !== -1) {\n\t\t\t\t\t\t\t\tthis.canvasElements.splice(index, 1)\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 清除选中状态和按钮\n\t\t\t\t\t\t\t\tthis.selectedElement = null\n\t\t\t\t\t\t\t\tthis.hideElementButtons()\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 重绘画布\n\t\t\t\t\t\t\t\tthis.drawCanvas()\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '元素已删除',\n\t\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}, \n\n\t\t\t// 放大图片\n\t\t\tzoomInImage() {\n\t\t\t\tif (!this.selectedElement || this.selectedElement.type !== 'image') return\n\t\t\t\t\n\t\t\t\tconsole.log('放大图片')\n\t\t\t\t\n\t\t\t\tconst currentWidth = this.selectedElement.width || 100\n\t\t\t\tconst currentHeight = this.selectedElement.height || 100\n\t\t\t\t\n\t\t\t\t// 等比例放大5%\n\t\t\t\tconst newWidth = currentWidth * 1.05\n\t\t\t\tconst newHeight = currentHeight * 1.05\n\t\t\t\t\n\t\t\t\t// 边界检测，确保放大后的图片不超出画布\n\t\t\t\tconst margin = 10\n\t\t\t\tconst maxWidth = (this.canvasWidth - margin * 2)\n\t\t\t\tconst maxHeight = (this.canvasHeight - margin * 2)\n\t\t\t\t\n\t\t\t\tthis.selectedElement.width = Math.min(newWidth, maxWidth)\n\t\t\t\tthis.selectedElement.height = Math.min(newHeight, maxHeight)\n\t\t\t\t\n\t\t\t\t// 确保图片中心不超出边界\n\t\t\t\tconst halfWidth = this.selectedElement.width / 2\n\t\t\t\tconst halfHeight = this.selectedElement.height / 2\n\t\t\t\t\n\t\t\t\tthis.selectedElement.x = Math.max(halfWidth + margin, \n\t\t\t\t\tMath.min(this.selectedElement.x, this.canvasWidth - halfWidth - margin))\n\t\t\t\tthis.selectedElement.y = Math.max(halfHeight + margin, \n\t\t\t\t\tMath.min(this.selectedElement.y, this.canvasHeight - halfHeight - margin))\n\t\t\t\t\n\t\t\t\tconsole.log('图片放大完成:', {\n\t\t\t\t\twidth: this.selectedElement.width,\n\t\t\t\t\theight: this.selectedElement.height\n\t\t\t\t})\n\t\t\t\t\n\t\t\t\tthis.drawCanvas()\n\t\t\t},\n\t\t\t\n\t\t\t// 缩小图片\n\t\t\tzoomOutImage() {\n\t\t\t\tif (!this.selectedElement || this.selectedElement.type !== 'image') return\n\t\t\t\t\n\t\t\t\tconsole.log('缩小图片')\n\t\t\t\t\n\t\t\t\tconst currentWidth = this.selectedElement.width || 100\n\t\t\t\tconst currentHeight = this.selectedElement.height || 100\n\t\t\t\t\n\t\t\t\t// 等比例缩小5%\n\t\t\t\tconst newWidth = currentWidth * 0.95\n\t\t\t\tconst newHeight = currentHeight * 0.95\n\t\t\t\t\n\t\t\t\t// 设置最小尺寸限制\n\t\t\t\tconst minSize = 20\n\t\t\t\t\n\t\t\t\tthis.selectedElement.width = Math.max(newWidth, minSize)\n\t\t\t\tthis.selectedElement.height = Math.max(newHeight, minSize)\n\t\t\t\t\n\t\t\t\tconsole.log('图片缩小完成:', {\n\t\t\t\t\twidth: this.selectedElement.width,\n\t\t\t\t\theight: this.selectedElement.height\n\t\t\t\t})\n\t\t\t\t\n\t\t\t\tthis.drawCanvas()\n\t\t\t},\n\t\t\t\n\t\t\t// 旋转图片\n\t\t\trotateImage() {\n\t\t\t\tif (!this.selectedElement || this.selectedElement.type !== 'image') return\n\t\t\t\t\n\t\t\t\tconsole.log('旋转图片')\n\t\t\t\t\n\t\t\t\tconst currentRotation = this.selectedElement.rotation || 0\n\t\t\t\t\n\t\t\t\t// 每次点击旋转15度\n\t\t\t\tthis.selectedElement.rotation = (currentRotation + 15) % 360\n\t\t\t\t\n\t\t\t\tconsole.log('图片旋转完成:', {\n\t\t\t\t\trotation: this.selectedElement.rotation\n\t\t\t\t})\n\t\t\t\t\n\t\t\t\tthis.drawCanvas()\n\t\t\t}\n\t\t}\n\t}\n\n</script>\n\n<style scoped>\n\t.template-editor {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\theight: 100vh;\n\t\tbackground-color: #f5f5f5;\n\t}\n\t\n\t/* 预览区域 */\n\t.preview-container {\n\t\tposition: relative;\n\t\tmargin: 10px;\n\t\tpadding: 8px;\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 8px;\n\t\tbox-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n\t\tborder: 2px solid #e0e0e0;\n\t\toverflow: visible; /* 确保按钮不被裁剪 */\n\t}\n\t\n\t.preview-canvas {\n\t\tdisplay: block;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tz-index: 1000;\n\t}\n\t\n\t/* 元素操作按钮 */\n\t.element-buttons {\n\t\tposition: absolute;\n\t\tdisplay: flex;\n\t\tgap: 8px;\n\t\tz-index: 99999999;\n\t\tpointer-events: auto;\n\t}\n\t\n\t.element-btn {\n\t\twidth: 32px;\n\t\theight: 32px;\n\t\tborder-radius: 16px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n\t\tcursor: pointer;\n\t\ttransition: all 0.3s ease;\n\t\tborder: 2px solid #fff;\n\t\tpointer-events: auto;\n\t\tuser-select: none;\n\t\t-webkit-user-select: none;\n\t\ttouch-action: manipulation;\n\t\t-webkit-touch-callout: none;\n\t\t-webkit-tap-highlight-color: transparent;\n\t\tposition: relative;\n\t\tz-index: 99999999;\n\t}\n\t\n\t.edit-btn {\n\t\tbackground-color: #007AFF;\n\t}\n\t\n\t.zoom-in-btn {\n\t\tbackground-color: #007AFF;\n\t}\n\t\n\t.zoom-out-btn {\n\t\tbackground-color: #FF9500;\n\t}\n\t\n\t.rotate-btn {\n\t\tbackground-color: #FF9500;\n\t}\n\t\n\t.delete-btn {\n\t\tbackground-color: #FF3B30;\n\t}\n\t\n\t.element-btn:hover {\n\t\ttransform: scale(1.1);\n\t\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);\n\t}\n\t\n\t.element-btn:active {\n\t\ttransform: scale(0.95);\n\t}\n\t\n\t.btn-icon {\n\t\tfont-size: 14px;\n\t\tcolor: #fff;\n\t\tline-height: 1;\n\t}\n\t\n\n\t\n\t/* 设置区域 */\n\t.template-settings, .background-settings {\n\t\tpadding: 15px;\n\t\tmargin: 0 10px 10px;\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 8px;\n\t\tbox-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t/* 批量操作区域 */\n\t.control-group {\n\t\tpadding: 15px;\n\t\tmargin: 0 10px 10px;\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 8px;\n\t\tbox-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.group-title {\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 12px;\n\t\tdisplay: block;\n\t}\n\t\n\t.button-row {\n\t\tdisplay: flex;\n\t\tgap: 8px;\n\t\tmargin-bottom: 8px;\n\t}\n\t\n\t.button-row:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\t\n\t.control-btn {\n\t\tflex: 1;\n\t\theight: 36px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder: 1px solid #ddd;\n\t\tborder-radius: 6px;\n\t\tbackground-color: #fff;\n\t\tfont-size: 13px;\n\t\tcolor: #666;\n\t\ttransition: all 0.3s ease;\n\t\tcursor: pointer;\n\t}\n\t\n\t.control-btn:hover {\n\t\tbackground-color: #f8f9fa;\n\t\tborder-color: #007AFF;\n\t\tcolor: #007AFF;\n\t\ttransform: translateY(-1px);\n\t\tbox-shadow: 0 2px 8px rgba(0, 122, 255, 0.15);\n\t}\n\t\n\t.control-btn:active {\n\t\ttransform: translateY(0);\n\t\tbox-shadow: 0 1px 4px rgba(0, 122, 255, 0.2);\n\t}\n\t\n\t/* 危险操作按钮 */\n\t.danger-btn {\n\t\tborder-color: #ff4757;\n\t\tcolor: #ff4757;\n\t}\n\t\n\t.danger-btn:hover {\n\t\tbackground-color: #ff4757;\n\t\tcolor: #fff;\n\t\tborder-color: #ff4757;\n\t\tbox-shadow: 0 2px 8px rgba(255, 71, 87, 0.25);\n\t}\n\t\n\t.danger-btn:active {\n\t\tbackground-color: #ff3742;\n\t\tborder-color: #ff3742;\n\t}\n\t\n\t.setting-row {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 15px;\n\t\tposition: relative;\n\t\tz-index: 2; /* 提高层级 */\n\t\tpointer-events: auto; /* 确保可点击 */\n\t}\n\t\n\t.setting-row:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\t\n\t.setting-label {\n\t\tfont-size: 14px;\n\t\tcolor: #333;\n\t\twidth: 80px;\n\t\tflex-shrink: 0;\n\t}\n\t\n\t.template-name-input {\n\t\tflex: 1;\n\t\theight: 35px;\n\t\tpadding: 0 10px;\n\t\tborder: 1px solid #ddd;\n\t\tborder-radius: 4px;\n\t\tfont-size: 14px;\n\t}\n\t\n\t/* 按钮组 */\n\t.insert-options, .background-options {\n\t\tdisplay: flex;\n\t\tflex: 1;\n\t\tgap: 8px;\n\t\tposition: relative;\n\t\tz-index: 2; /* 提高层级 */\n\t}\n\t\n\t.insert-btn, .bg-btn {\n\t\tflex: 1;\n\t\theight: 35px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder: 1px solid #ddd;\n\t\tborder-radius: 4px;\n\t\tbackground-color: #fff;\n\t\tfont-size: 12px;\n\t\tcolor: #666;\n\t\ttransition: all 0.3s;\n\t\tposition: relative; /* 确保定位正确 */\n\t\tz-index: 1; /* 提高层级 */\n\t\tpointer-events: auto; /* 确保可点击 */\n\t}\n\t\n\t.insert-btn.active, .bg-btn.active {\n\t\tbackground-color: #d32f2f;\n\t\tcolor: #fff;\n\t\tborder-color: #d32f2f;\n\t}\n\t\n\t/* 属性面板 */\n\t.property-panel {\n\t\t/* flex: 1; */\n\t\tpadding: 15px;\n\t\tmargin: 0 10px;\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 8px;\n\t\tbox-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n\t\theight: 40vh;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\toverflow: hidden;\n\t}\n\t\n\t/* 属性内容容器 */\n\t.property-container {\n\t\t/* flex: 1; */\n\t\toverflow-y: auto;\n\t\t-webkit-overflow-scrolling: touch; /* iOS平滑滚动 */\n\t\tscrollbar-width: thin; /* Firefox细滚动条 */\n\t\tscrollbar-color: #c1c1c1 transparent; /* Firefox滚动条颜色 */\n\t}\n\t\n\t/* 自定义滚动条样式 - Webkit内核 */\n\t.property-container::-webkit-scrollbar {\n\t\twidth: 6px;\n\t}\n\t\n\t.property-container::-webkit-scrollbar-track {\n\t\tbackground: transparent;\n\t\tborder-radius: 3px;\n\t}\n\t\n\t.property-container::-webkit-scrollbar-thumb {\n\t\tbackground: #c1c1c1;\n\t\tborder-radius: 3px;\n\t\ttransition: background 0.3s ease;\n\t}\n\t\n\t.property-container::-webkit-scrollbar-thumb:hover {\n\t\tbackground: #a8a8a8;\n\t}\n\t\n\t/* 响应式设计 - 小屏幕优化 */\n\t@media (max-height: 600px) {\n\t\t.property-panel {\n\t\t\theight: 40vh; /* 小屏幕增加高度比例 */\n\t\t}\n\t}\n\t\n\t@media (max-height: 500px) {\n\t\t.property-panel {\n\t\t\theight: 45vh; /* 更小屏幕进一步增加高度比例 */\n\t\t}\n\t}\n\t\n\t/* 移动端触摸优化 */\n\t@media (max-width: 768px) {\n\t\t.property-panel {\n\t\t\tpadding: 12px; /* 移动端减少内边距 */\n\t\t\tmargin: 0 5px; /* 减少外边距 */\n\t\t}\n\t\t\n\t\t.property-container::-webkit-scrollbar {\n\t\t\twidth: 4px; /* 移动端更细的滚动条 */\n\t\t}\n\t}\n\t\n\t/* 面板头部 - 固定定位 */\n\t.panel-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 15px;\n\t\tposition: sticky;\n\t\ttop: 0;\n\t\tz-index: 10;\n\t\tbackground-color: #ffffff;\n\t\tpadding: 10px 0;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t\tmargin: -15px -15px 15px -15px;\n\t\tpadding-left: 15px;\n\t\tpadding-right: 15px;\n\t}\n\t\n\t.panel-title {\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\t\n\t\n\t.property-group {\n\t\tmargin-bottom: 20px;\n\t}\n\t\n\t.property-row {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-top:9px ;\n\t\tmargin-bottom: 9px;\n\t}\n\t\n\t.property-label {\n\t\tfont-size: 13px;\n\t\tcolor: #333;\n\t\twidth: 70px;\n\t\tflex-shrink: 0;\n\t}\n\t\n\t/* 对齐按钮 */\n\t.align-options, .style-options {\n\t\tdisplay: flex;\n\t\tflex: 1;\n\t\tgap: 4px;\n\t}\n\t\n\t.align-btn, .style-btn {\n\t\twidth: 32px;\n\t\theight: 32px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder: 1px solid #ddd;\n\t\tborder-radius: 4px;\n\t\tbackground-color: #fff;\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t\ttransition: all 0.3s;\n\t}\n\t\n\t.align-btn.active, .style-btn.active {\n\t\tbackground-color: #d32f2f;\n\t\tcolor: #fff;\n\t\tborder-color: #d32f2f;\n\t}\n\t\n\n\t\n\t/* 保存按钮容器 */\n\t.save-container {\n\t\tpadding: 15px;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tgap: 15px;\n\t\tflex-wrap: wrap;\n\t}\n\t\n\t/* 预览按钮 */\n\t.preview-btn {\n\t\twidth: 140px;\n\t\theight: 45px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground: linear-gradient(135deg, #4A90E2 0%, #357ABD 50%, #4A90E2 100%);\n\t\tborder-radius: 25px;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);\n\t\ttransition: all 0.3s ease;\n\t}\n\t\n\t.preview-btn:hover {\n\t\ttransform: translateY(-2px);\n\t\tbox-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);\n\t}\n\t\n\t.preview-btn::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: -100%;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n\t\ttransition: left 0.5s;\n\t}\n\t\n\t.preview-btn:active::before {\n\t\tleft: 100%;\n\t}\n\t\n\t.preview-btn:active {\n\t\ttransform: translateY(0);\n\t}\n\t\n\t/* 保存按钮 */\n\t.save-btn {\n\t\twidth: 140px;\n\t\theight: 45px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground: linear-gradient(135deg, #8B0000 0%, #DC143C 50%, #8B0000 100%);\n\t\tborder-radius: 25px;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 4px 15px rgba(139, 0, 0, 0.3);\n\t\ttransition: all 0.3s ease;\n\t}\n\t\n\t.save-btn:hover {\n\t\ttransform: translateY(-2px);\n\t\tbox-shadow: 0 6px 20px rgba(139, 0, 0, 0.4);\n\t}\n\t\n\t.save-btn::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: -100%;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n\t\ttransition: left 0.5s;\n\t}\n\t\n\t.save-btn:active::before {\n\t\tleft: 100%;\n\t}\n\t\n\t.save-btn:active {\n\t\ttransform: translateY(0);\n\t}\n\t\n\t/* 按钮图标样式 */\n\t.btn-icon {\n\t\tcolor: #fff;\n\t\tfont-size: 18px;\n\t\tmargin-right: 6px;\n\t\tz-index: 1;\n\t\tpointer-events: none;\n\t\tfilter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.3));\n\t}\n\t\n\t/* 按钮文字样式 */\n\t.btn-text {\n\t\tcolor: #fff;\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t\ttext-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);\n\t\tz-index: 1;\n\t\tpointer-events: none;\n\t}\n\t\n\t/* 响应式设计 - 保持并排显示 */\n\t@media (max-width: 480px) {\n\t\t.save-container {\n\t\t\tpadding: 10px;\n\t\t\tgap: 8px;\n\t\t\tjustify-content: center;\n\t\t}\n\t\t\n\t\t.preview-btn,\n\t\t.save-btn {\n\t\t\twidth: 110px;\n\t\t\theight: 40px;\n\t\t}\n\t\t\n\t\t.btn-icon {\n\t\t\tfont-size: 16px;\n\t\t\tmargin-right: 4px;\n\t\t}\n\t\t\n\t\t.btn-text {\n\t\t\tfont-size: 14px;\n\t\t}\n\t}\n\t\n\t/* 超小屏幕优化 - 保持并排显示 */\n\t@media (max-width: 360px) {\n\t\t.save-container {\n\t\t\tgap: 6px;\n\t\t\tpadding: 8px;\n\t\t}\n\t\t\n\t\t.preview-btn,\n\t\t.save-btn {\n\t\t\twidth: 90px;\n\t\t\theight: 36px;\n\t\t}\n\t\t\n\t\t.btn-icon {\n\t\t\tfont-size: 14px;\n\t\t\tmargin-right: 2px;\n\t\t}\n\t\t\n\t\t.btn-text {\n\t\t\tfont-size: 12px;\n\t\t}\n\t}\n\t\n\t/* 模板字段选择器弹窗 */\n\t.field-picker-modal {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\tdisplay: flex;\n\t\talign-items: flex-end;\n\t\tjustify-content: center;\n\t\tz-index: 1000;\n\t\tanimation: fadeIn 0.3s ease-out;\n\t}\n\t\n\t.field-picker-content {\n\t\tbackground-color: #fff;\n\t\tborder-radius: 12px 12px 0 0;\n\t\tpadding: 0;\n\t\tmax-width: 90%;\n\t\tmax-height: 80%;\n\t\twidth: 400px;\n\t\tbox-shadow: 0 -5px 30px rgba(0, 0, 0, 0.3);\n\t\toverflow: hidden;\n\t\tanimation: slideUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n\t\ttransform-origin: bottom;\n\t}\n\t\n\t/* 弹窗动画 */\n\t@keyframes fadeIn {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t}\n\t\tto {\n\t\t\topacity: 1;\n\t\t}\n\t}\n\t\n\t@keyframes slideUp {\n\t\tfrom {\n\t\t\ttransform: translateY(100%);\n\t\t\topacity: 0.8;\n\t\t}\n\t\tto {\n\t\t\ttransform: translateY(0);\n\t\t\topacity: 1;\n\t\t}\n\t}\n\t\n\t/* 退出动画 */\n\t.field-picker-modal.fade-out {\n\t\tanimation: fadeOut 0.25s ease-in;\n\t}\n\t\n\t.field-picker-modal.fade-out .field-picker-content {\n\t\tanimation: slideDown 0.3s cubic-bezier(0.55, 0.06, 0.68, 0.19);\n\t}\n\t\n\t@keyframes fadeOut {\n\t\tfrom {\n\t\t\topacity: 1;\n\t\t}\n\t\tto {\n\t\t\topacity: 0;\n\t\t}\n\t}\n\t\n\t@keyframes slideDown {\n\t\tfrom {\n\t\t\ttransform: translateY(0);\n\t\t\topacity: 1;\n\t\t}\n\t\tto {\n\t\t\ttransform: translateY(100%);\n\t\t\topacity: 0.8;\n\t\t}\n\t}\n\t\n\t.field-picker-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 20px;\n\t\tbackground: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);\n\t\tcolor: white;\n\t}\n\t\n\t.field-picker-title {\n\t\tfont-size: 18px;\n\t\tfont-weight: bold;\n\t}\n\t\n\t.field-picker-close {\n\t\twidth: 30px;\n\t\theight: 30px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder-radius: 50%;\n\t\tbackground-color: rgba(255, 255, 255, 0.2);\n\t\tfont-size: 20px;\n\t\tcursor: pointer;\n\t\ttransition: background-color 0.3s;\n\t}\n\t\n\t.field-picker-close:hover {\n\t\tbackground-color: rgba(255, 255, 255, 0.3);\n\t}\n\t\n\t.field-grid {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(2, 1fr);\n\t\tgap: 15px;\n\t\tpadding: 20px;\n\t\tmax-height: 400px;\n\t\toverflow-y: auto;\n\t}\n\t\n\t.field-item {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tpadding: 15px;\n\t\tborder: 2px solid #e0e0e0;\n\t\tborder-radius: 8px;\n\t\tbackground-color: #fafafa;\n\t\tcursor: pointer;\n\t\ttransition: all 0.3s;\n\t\ttext-align: center;\n\t}\n\t\n\t.field-item:hover {\n\t\tborder-color: #d32f2f;\n\t\tbackground-color: #fff;\n\t\ttransform: translateY(-2px);\n\t\tbox-shadow: 0 4px 12px rgba(211, 47, 47, 0.15);\n\t}\n\t\n\t.field-icon {\n\t\tfont-size: 24px;\n\t\tmargin-bottom: 8px;\n\t}\n\t\n\t.field-name {\n\t\tfont-size: 14px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 4px;\n\t}\n\t\n\t.field-desc {\n\t\tfont-size: 12px;\n\t\tcolor: #666;\n\t\tline-height: 1.3;\n\t}\n\t\n\n\t\n\t/* 位置信息显示 */\n\t.position-info {\n\t\tflex: 1;\n\t\tfont-size: 12px;\n\t\tcolor: #666;\n\t\tbackground-color: #f8f9fa;\n\t\tpadding: 6px 10px;\n\t\tborder-radius: 4px;\n\t\tborder: 1px solid #e9ecef;\n\t\tfont-family: 'Courier New', monospace;\n\t}\n\t\n\t \n\t\n\t/* 预览容器增强 */\n\t.preview-container {\n\t\tposition: relative;\n\t\tbackground-color: #ffffff;\n\t\tmargin: 10px;\n\t\tborder-radius: 8px;\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n\t\toverflow: visible; /* 改为visible避免裁剪 */ \n\t\tdisplay: flex;\n\t\talign-items: flex-start; /* 改为flex-start避免压缩 */\n\t\tjustify-content: center;\n\t\tpadding: 8px;\n\t\tbox-sizing: border-box;\n\t\ttouch-action: none; /* 防止默认触摸行为 */\n\t}\n\t\n\t/* 画布样式 */\n\t.preview-canvas {\n\t\tdisplay: block;\n\t\tborder: 2px solid #e0e0e0;\n\t\tborder-radius: 4px;\n\t\tbackground-color: #fafafa;\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n\t\t/* 移除max-width和max-height限制，让画布按计算尺寸显示 */\n\t\ttransition: box-shadow 0.3s ease;\n\t}\n\t\n\t.preview-canvas:hover {\n\t\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);\n\t}\n\t\n\t/* 弹窗样式 */\n\t.template-popup-content, .preview-popup-content, .edit-text-popup-content, .property-popup-content {\n\t\tpadding: 20px;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 16px;\n\t\tmin-width: 300px;\n\t\tmax-width: 90vw;\n\t}\n\t\n\t/* 属性编辑弹窗特定样式 */\n\t.property-popup-content {\n\t\tmax-width: 100vw;\n\t\tmax-height: 70vh;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tpadding: 0;\n\t\tborder-radius: 16px 16px 0 0;\n\t}\n\n\t.property-title {\n\t\tfont-size: 18px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\ttext-align: center;\n\t\tpadding: 16px 0;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t\tbackground-color: #fff;\n\t\tflex-shrink: 0;\n\t\tborder-radius: 16px 16px 0 0;\n\t}\n\n\t.popup-header {\n\t\tpadding: 20px 20px 0 20px;\n\t\tflex-shrink: 0;\n\t}\n\n\t.popup-scroll-content {\n\t\tflex: 1;\n\t\tpadding: 8px 16px;\n\t\toverflow-y: auto;\n\t}\n \n\t.style-buttons {\n\t\tdisplay: flex;\n\t\tgap: 8px;\n\t\tflex: 1;\n\t\tmargin-left: 12px;\n\t}\n\t\n\t.style-btn {\n\t\twidth: 32px;\n\t\theight: 32px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder: 1px solid #e0e0e0;\n\t\tborder-radius: 4px;\n\t\tbackground-color: #fff;\n\t\tcursor: pointer;\n\t\ttransition: all 0.2s;\n\t\tuser-select: none;\n\t}\n\t\n\t.style-btn:hover {\n\t\tbackground-color: #f0f0f0;\n\t\tborder-color: #007aff;\n\t}\n\t\n\t.style-btn.active {\n\t\tbackground-color: #007aff;\n\t\tborder-color: #007aff;\n\t\tcolor: #fff;\n\t}  \n\n\t.popup-title {\n\t\tfont-size: 18px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\ttext-align: center;\n\t\tmargin-bottom: 20px;\n\t}\n\t\n\t.template-field {\n\t\tmargin-bottom: 15px;\n\t}\n\t\n\t.field-label {\n\t\tfont-size: 14px;\n\t\tcolor: #333;\n\t\tmargin-bottom: 8px;\n\t\tdisplay: block;\n\t}\n\t\n\t.field-input {\n\t\twidth: 100%;\n\t\theight: 40px;\n\t\tpadding: 0 12px;\n\t\tborder: 1px solid #ddd;\n\t\tborder-radius: 6px;\n\t\tfont-size: 14px;\n\t\tbox-sizing: border-box;\n\t}\n\t\n\t.field-input:focus {\n\t\tborder-color: #d32f2f;\n\t\toutline: none;\n\t}\n\t\n\t\n\t\n\t/* 弹窗按钮样式 */\n\t.popup-btns {\n\t\tdisplay: flex;\n\t\tgap: 12px;\n\t\tmargin-top: 20px;\n\t\tpadding: 0 20px 20px 20px;\n\t}\n\t\n\t.popup-btn {\n\t\tflex: 1;\n\t\theight: 44px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder-radius: 8px;\n\t\tfont-size: 16px;\n\t\tfont-weight: 500;\n\t\tcursor: pointer;\n\t\ttransition: all 0.2s;\n\t}\n\t\n\t.popup-btn.cancel {\n\t\tbackground-color: #f8f9fa;\n\t\tcolor: #6c757d;\n\t\tborder: 1px solid #dee2e6;\n\t}\n\t\n\t.popup-btn.cancel:hover {\n\t\tbackground-color: #e9ecef;\n\t}\n\t\n\t.popup-btn.confirm {\n\t\tbackground-color: #007aff;\n\t\tcolor: #fff;\n\t\tborder: 1px solid #007aff;\n\t}\n\t\n\t.popup-btn.confirm:hover {\n\t\tbackground-color: #0056b3;\n\t}\n\t\n\t/* 文字编辑弹窗专用样式 */\n\t.edit-form-container {\n\t\tpadding: 0 16px 16px 16px;\n\t\tmax-width: 500px;\n\t\tmargin: 0 auto;\n\t}\n\t\n\t.element-actions {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tgap: 16px;\n\t\tmargin-bottom: 24px;\n\t\tpadding: 16px;\n\t\tbackground-color: #f8f9fa;\n\t\tborder-radius: 12px;\n\t\tborder: 1px solid #e9ecef;\n\t}\n\t\n\t.action-btn {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 12px 20px;\n\t\tborder-radius: 8px;\n\t\tcursor: pointer;\n\t\ttransition: all 0.3s ease;\n\t\tmin-width: 80px;\n\t\tbox-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n\t}\n\t\n\t.edit-btn {\n\t\tbackground: linear-gradient(135deg, #007aff 0%, #0056b3 100%);\n\t\tcolor: white;\n\t}\n\t\n\t.edit-btn:hover {\n\t\tbackground: linear-gradient(135deg, #0056b3 0%, #004085 100%);\n\t\ttransform: translateY(-2px);\n\t\tbox-shadow: 0 4px 8px rgba(0, 122, 255, 0.3);\n\t}\n\t\n\t.delete-btn {\n\t\tbackground: linear-gradient(135deg, #dc3545 0%, #c82333 100%);\n\t\tcolor: white;\n\t}\n\t\n\t.delete-btn:hover {\n\t\tbackground: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);\n\t\ttransform: translateY(-2px);\n\t\tbox-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);\n\t}\n\t\n\t.action-icon {\n\t\tfont-size: 20px;\n\t\tmargin-bottom: 4px;\n\t}\n\t\n\t.action-text {\n\t\tfont-size: 12px;\n\t\tfont-weight: 500;\n\t\ttext-align: center;\n\t} \n\t\n\t.edit-form {\n\t\tpadding: 0 16px;\n\t}\n\t\n\t.property-row {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 12px 0;\n\t\tborder-bottom: 1px solid #f5f5f5;\n\t}\n\t\n\t.property-row:last-child {\n\t\tborder-bottom: none;\n\t}\n\t\n\t.property-label {\n\t\tfont-size: 14px;\n\t\tcolor: #333;\n\t\tfont-weight: 500;\n\t\tmin-width: 80px;\n\t}\n\t\n\t.property-picker {\n\t\tflex: 1;\n\t\tmargin-left: 12px;\n\t\tcursor: pointer;\n\t}\n\t\n\t.text-input {\n\t\tflex: 1;\n\t\tmargin-left: 12px;\n\t\tpadding: 8px 12px;\n\t\tborder: 1px solid #e0e0e0;\n\t\tborder-radius: 6px;\n\t\tbackground-color: #fff;\n\t\tfont-size: 14px;\n\t\tcolor: #333;\n\t\toutline: none;\n\t}\n\t\n\t.text-input:focus {\n\t\tborder-color: #007aff;\n\t\tbox-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);\n\t}\n\t\n\t.picker-value {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 8px 12px;\n\t\tborder: 1px solid #e0e0e0;\n\t\tborder-radius: 6px;\n\t\tbackground-color: #fafafa;\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t}\n\t\n\t.picker-arrow {\n\t\tfont-size: 12px;\n\t\tcolor: #999;\n\t} \n\t.color-preview-small {\n\t\twidth: 20px;\n\t\theight: 20px;\n\t\tborder-radius: 4px;\n\t\tborder: 1px solid #ddd;\n\t\tflex-shrink: 0;\n\t}\n\t\n\t.color-name {\n\t\tflex: 1;\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t\tmargin-left: 8px;\n\t}\n\t\n\t.slider-container {\n\t\tflex: 1;\n\t\tmargin-left: 12px;\n\t\tpadding: 0 8px;\n\t}\n\t\n\t.style-text {\n\t\tfont-size: 14px;\n\t\tfont-weight: bold;\n\t\tcolor: inherit;\n\t} \n\n\t.preview-image-container {\n\t\twidth: 300px;\n\t\theight: 200px;\n\t\tborder: 1px solid #ddd;\n\t\tborder-radius: 6px;\n\t\toverflow: hidden;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin: 0 auto 20px;\n\t}\n\t\n\t.preview-image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\t\n\t.preview-loading, .preview-error {\n\t\tcolor: #666;\n\t\tfont-size: 14px;\n\t}\n\t\n\n</style>\n", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit_new.vue?vue&type=style&index=0&id=4aed2b70&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit_new.vue?vue&type=style&index=0&id=4aed2b70&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751874188492\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}