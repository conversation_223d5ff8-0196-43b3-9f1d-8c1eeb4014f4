{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/public/batch-screen.vue?da1c", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/public/batch-screen.vue?0c4c", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/public/batch-screen.vue?4f01", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/public/batch-screen.vue?63be", "uni-app:///pages/subPackage/public/batch-screen.vue", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/public/batch-screen.vue?7bd5", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/public/batch-screen.vue?df09"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isMP", "screenMode", "showTemplates", "showEditor", "deviceInfo", "name", "id", "mac", "status", "templates", "selectedTemplate", "currentTemplate", "loading", "edit<PERSON>ields", "label", "value", "placeholder", "onLoad", "methods", "goBack", "uni", "getDeviceInfo", "setTimeout", "getTemplateList", "url", "method", "success", "image", "frontImage", "backImage", "background", "type", "fields", "console", "fail", "complete", "useDefaultTemplates", "getDeviceTemplate", "then", "res", "catch", "switchScreenMode", "title", "icon", "duration", "showTemplateSelector", "closeTemplateSelector", "showContentEditor", "closeContentEditor", "saveContent", "selectTemplate", "template", "newEdit<PERSON><PERSON>s", "confirmScreen", "<PERSON><PERSON><PERSON><PERSON>", "deviceId", "templateId", "content"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACc;;;AAGzE;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA4tB,CAAgB,4rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC8IhvB;AAAA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC,aACA;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA;IAEA;EACA;EACAC;IACA;IACA;MACA;MACA;MACA;MACA;MACA;IACA;;IAEA;IACA;;IAEA;;IAEA;EAEA;EACAC;IACAC;MACAC;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACAC;QACA;UACAjB;UACAC;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAe;MAAA;MACA;MACA;MACAH;QACAI;QACAC;QACAC;UACA;YACA;YACA;cACA;gBACApB;gBACAD;gBACAsB;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;cACA;YACA;;YAEA;YACA;cACA;cACA;YACA;UACA;YACAC;YACA;YACA;UACA;QACA;QACAC;UACAD;UACA;UACA;QACA;QACAE;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA,kBACA;QACA9B;QACAD;QACAsB;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACA1B;QACAD;QACAsB;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,EACA;;MAEA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAK;MAAA;MACA;MACA,wCACAC;QACA;UACA;UACA;UACA;UACA;YACAC;cACA;gBACA;cACA;YACA;UACA;QACA;MACA,GACAC;QACAP;MACA;IACA;IACA;IACAQ;MACA;MACArB;QACAsB;QACAC;QACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA3B;QACAI;QACAC;QACAC;UACA;YACA;YACA;YACA;;YAEA;YACA;cACA;gBACApB;gBACAwB;gBACAF;gBACAC;gBACAE;gBACAC;cACA;YACA;cACA;cACA;YACA;YAEA;UACA;QACA;QACAE;UACAD;QACA;QACAE;UACA;UACA;QACA;MACA;IACA;IACA;IACAa;MACA;IACA;IACA;IACAC;MACA;MACA7B;QACAsB;QACAC;MACA;IACA;IACA;IACAO;MAAA;MACA;MACA;MACA;MACA9B;QACAsB;QACAC;MACA;;MAEA;MACA;QACA;QACAQ;UACA;UACA;UACAC;YACAtC;YACAC;YACAC;UACA;QACA;QACA;MACA;IACA;IACA;IACAqC;MACA;MACA;MAAA,2CACA;QAAA;MAAA;QAAA;UAAA;UACA;YACAC;YACAlC;cACAsB;cACAC;YACA;YACA;UACA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;MAEA;QACA;QACA;UACAY;UACAC;UACAvD;UACAwD;YAAA;cACA3C;cACAC;YACA;UAAA;QACA;;QAEA;QACAkB;QACAb;UACAsB;QACA;;QAEA;QACApB;UACAF;UACAA;YACAsB;YACAC;UACA;;UAEA;UACArB;YACAF;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpdA;AAAA;AAAA;AAAA;AAAm2C,CAAgB,muCAAG,EAAC,C;;;;;;;;;;;ACAv3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/subPackage/public/batch-screen.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/subPackage/public/batch-screen.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./batch-screen.vue?vue&type=template&id=41fdf355&\"\nvar renderjs\nimport script from \"./batch-screen.vue?vue&type=script&lang=js&\"\nexport * from \"./batch-screen.vue?vue&type=script&lang=js&\"\nimport style0 from \"./batch-screen.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/subPackage/public/batch-screen.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./batch-screen.vue?vue&type=template&id=41fdf355&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./batch-screen.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./batch-screen.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\"> \n\t\t<!-- 主内容区域 -->\n\t\t<view class=\"content\">\n\t\t\t<!-- 屏幕模式选择 -->\n\t\t\t<view class=\"screen-mode-tabs\">\n\t\t\t\t<view class=\"tab-row\">\n\t\t\t\t\t<view class=\"tab-item\" :class=\"{active: screenMode === 'same'}\" @click=\"switchScreenMode('same')\" style=\"width: 50%;\">\n\t\t\t\t\t\t<text>前后屏相同</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tab-item\" :class=\"{active: screenMode === 'different'}\" @click=\"switchScreenMode('different')\" style=\"width: 50%;\">\n\t\t\t\t\t\t<text>前后屏不同</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 预览区域 -->\n\t\t\t<view class=\"preview-area\">\n\t\t\t\t<!-- 前后屏相同模式 -->\n\t\t\t\t<view class=\"preview-card\" v-if=\"screenMode === 'same'\">\n\t\t\t\t\t<view class=\"template-preview\">\n\t\t\t\t\t\t<!-- 中国风边框和内容 -->\n\t\t\t\t\t\t<view class=\"chinese-style-template front-screen\" :style=\"{'background-image': currentTemplate && currentTemplate.background ? 'url(' + currentTemplate.background + ')' : 'url(/static/images/public_template/33882e8f90e990a4f1f140011e7a58ce.png)'}\">\n\t\t\t\t\t\t\t<view class=\"screen-tag\">前</view>\n\t\t\t\t\t\t\t \n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"operation-buttons\">\n\t\t\t\t\t\t\t<view class=\"op-button-row\">\n\t\t\t\t\t\t\t\t<view class=\"op-button\" @click=\"showTemplateSelector\" style=\"width: 50%;\">\n\t\t\t\t\t\t\t\t\t<image class=\"op-icon\" src=\"/static/images/template-icon.svg\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t\t\t<text class=\"op-text\">选择模板</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"op-button\" @click=\"showContentEditor\" style=\"width: 50%;\">\n\t\t\t\t\t\t\t\t\t<image class=\"op-icon\" src=\"/static/images/edit-icon.svg\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t\t\t<text class=\"op-text\">编辑内容</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"bottom-gradient-line\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 前后屏不同模式 -->\n\t\t\t\t<view class=\"preview-cards\" v-if=\"screenMode === 'different'\">\n\t\t\t\t\t<view>\n\t\t\t\t\t\t<!-- 前屏 -->\n\t\t\t\t\t\t<view class=\"preview-card\">\n\t\t\t\t\t\t\t<view class=\"template-preview\">\n\t\t\t\t\t\t\t\t<view class=\"chinese-style-template front-screen\" :style=\"{'background-image': currentTemplate && currentTemplate.background ? 'url(' + currentTemplate.background + ')' : 'url(/static/images/public_template/33882e8f90e990a4f1f140011e7a58ce.png)'}\">\n\t\t\t\t\t\t\t\t\t<view class=\"screen-tag\">前</view> \n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 按钮模块 -->\n\t\t\t\t\t\t<view class=\"operation-buttons\">\n\t\t\t\t\t\t\t<view class=\"op-button-row\">\n\t\t\t\t\t\t\t\t<view class=\"op-button\" @click=\"showTemplateSelector\" style=\"width: 50%;\">\n\t\t\t\t\t\t\t\t\t<image class=\"op-icon\" src=\"/static/images/template-icon.svg\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t\t\t<text class=\"op-text\">选择模板</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"op-button\" @click=\"showContentEditor\" style=\"width: 50%;\">\n\t\t\t\t\t\t\t\t\t<image class=\"op-icon\" src=\"/static/images/edit-icon.svg\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t\t\t<text class=\"op-text\">编辑内容</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"bottom-gradient-line\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view>\n\t\t\t\t\t\t<!-- 后屏 -->\n\t\t\t\t\t\t<view class=\"preview-card\">\n\t\t\t\t\t\t\t<view class=\"template-preview\">\n\t\t\t\t\t\t\t\t<view class=\"chinese-style-template back-screen\" :style=\"{'background-image': currentTemplate && currentTemplate.background ? 'url(' + currentTemplate.background + ')' : 'url(/static/images/public_template/f2e05295636f1b849b791a21b5548e30.png)'}\">\n\t\t\t\t\t\t\t\t\t<view class=\"screen-tag\">后</view> \n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 按钮模块 -->\n\t\t\t\t\t\t<view class=\"operation-buttons\">\n\t\t\t\t\t\t\t<view class=\"op-button-row\">\n\t\t\t\t\t\t\t\t<view class=\"op-button\" @click=\"showTemplateSelector\" style=\"width: 50%;\">\n\t\t\t\t\t\t\t\t\t<image class=\"op-icon\" src=\"/static/images/template-icon.svg\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t\t\t<text class=\"op-text\">选择模板</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"op-button\" @click=\"showContentEditor\" style=\"width: 50%;\">\n\t\t\t\t\t\t\t\t\t<image class=\"op-icon\" src=\"/static/images/edit-icon.svg\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t\t\t<text class=\"op-text\">编辑内容</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"bottom-gradient-line\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view> \n\t\t\t\t</view>\n\t\t\t</view> \n\t\t</view>\n\t\t\n\t\t<!-- 底部确认按钮 -->\n\t\t<view class=\"footer\">\n\t\t\t<view class=\"confirm-button\" @click=\"confirmScreen\">\n\t\t\t\t<text>确认无误，下一步</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 模板选择弹窗 -->\n\t\t<u-popup :show=\"showTemplates\" mode=\"bottom\" @close=\"closeTemplateSelector\" round=\"10\" closeable>\n\t\t\t<view class=\"template-popup\">\n\t\t\t\t<view class=\"popup-title\">\n\t\t\t\t\t<text>选择模板</text>\n\t\t\t\t</view>\n\t\t\t\t<scroll-view class=\"template-scroll\" scroll-x=\"true\" show-scrollbar=\"false\">\n\t\t\t\t\t<view class=\"template-list\">\n\t\t\t\t\t\t<view class=\"template-item\" v-for=\"(item, index) in templates\" :key=\"index\" @click=\"selectTemplate(item)\" :class=\"{active: selectedTemplate === item.id}\">\n\t\t\t\t\t\t\t<image class=\"template-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t<text class=\"template-name\">{{item.name}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t</u-popup>\n\n\t\t<!-- 内容编辑弹窗 -->\n\t\t<u-popup :show=\"showEditor\" mode=\"bottom\" @close=\"closeContentEditor\" round=\"10\" closeable>\n\t\t\t<view class=\"editor-popup\">\n\t\t\t\t<view class=\"popup-title\">\n\t\t\t\t\t<text>编辑内容</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"edit-form\">\n\t\t\t\t\t<view class=\"form-item\" v-for=\"(field, index) in editFields\" :key=\"index\">\n\t\t\t\t\t\t<text class=\"form-label\">{{field.label}}</text>\n\t\t\t\t\t\t<input class=\"form-input\" v-model=\"field.value\" :placeholder=\"field.placeholder\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup-button\" @click=\"saveContent\">\n\t\t\t\t\t<text>保存</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</u-popup>\n\t</view>\n</template>\n\n<script>\n\timport { fetchTemplates, fetchDeviceTemplate } from '@/common/api.js';\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tisMP: false, // 是否为小程序环境\n\t\t\t\tscreenMode: 'same', // 屏幕模式：same-前后屏相同，different-前后屏不同\n\t\t\t\tshowTemplates: false, // 是否显示模板选择弹窗\n\t\t\t\tshowEditor: false, // 是否显示内容编辑弹窗\n\t\t\t\tdeviceInfo: {\n\t\t\t\t\tname: '设备名称',\n\t\t\t\t\tid: '12345678',\n\t\t\t\t\tmac: '00:11:22:33:44:55',\n\t\t\t\t\tstatus: 'connected'\n\t\t\t\t},\n\t\t\t\ttemplates: [],\n\t\t\t\tselectedTemplate: null,\n\t\t\t\tcurrentTemplate: null,\n\t\t\t\tloading: false,\n\t\t\t\teditFields: [\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '姓名',\n\t\t\t\t\t\tvalue: '',\n\t\t\t\t\t\tplaceholder: '请输入姓名'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '职称',\n\t\t\t\t\t\tvalue: '',\n\t\t\t\t\t\tplaceholder: '请输入职称'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '公司名称',\n\t\t\t\t\t\tvalue: '',\n\t\t\t\t\t\tplaceholder: '请输入公司名称'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '后屏信息1',\n\t\t\t\t\t\tvalue: '',\n\t\t\t\t\t\tplaceholder: '请输入后屏信息1'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '后屏信息2',\n\t\t\t\t\t\tvalue: '',\n\t\t\t\t\t\tplaceholder: '请输入后屏信息2'\n\t\t\t\t\t}\n\t\t\t\t]\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\t// 获取设备信息\n\t\t\tif (options.deviceId) {\n\t\t\t\tthis.deviceInfo.id = options.deviceId;\n\t\t\t\t// 获取设备详情\n\t\t\t\tthis.getDeviceInfo(options.deviceId);\n\t\t\t\t// 获取设备当前使用的模板\n\t\t\t\tthis.getDeviceTemplate(options.deviceId);\n\t\t\t}\n\t\t\t\n\t\t\t// 获取所有模板列表\n\t\t\tthis.getTemplateList();\n\t\t\t\n\t\t\t// 判断平台\n\t\t\t// #ifdef MP\n\t\t\tthis.isMP = true;\n\t\t\t// #endif\n\t\t},\n\t\tmethods: {\n\t\t\tgoBack() {\n\t\t\t\tuni.navigateBack();\n\t\t\t},\n\t\t\t\n\t\t\t// 获取设备详情\n\t\t\tgetDeviceInfo(deviceId) {\n\t\t\t\t// 这里应该调用API获取设备详情\n\t\t\t\t// 模拟API调用\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.deviceInfo = {\n\t\t\t\t\t\tname: '电子桌牌-' + deviceId,\n\t\t\t\t\t\tid: deviceId,\n\t\t\t\t\t\tmac: '00:11:22:33:44:55',\n\t\t\t\t\t\tstatus: 'connected'\n\t\t\t\t\t};\n\t\t\t\t}, 500);\n\t\t\t},\n\t\t\t\n\t\t\t// 获取模板列表\n\t\t\tgetTemplateList() {\n\t\t\t\tthis.loading = true;\n\t\t\t\t// 从本地temp.json文件加载模板数据\n\t\t\t\tuni.request({\n\t\t\t\t\turl: '/pages/public/tempjson/temp.json',\n\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.data && res.data.code === 200 && res.data.data) {\n\t\t\t\t\t\t\t// 转换数据格式\n\t\t\t\t\t\t\tthis.templates = res.data.data.map(item => {\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\tid: item.id,\n\t\t\t\t\t\t\t\t\tname: item.template.name || `模板${item.id}`,\n\t\t\t\t\t\t\t\t\timage: item.template.path || '/static/images/public_template/33882e8f90e990a4f1f140011e7a58ce.png',\n\t\t\t\t\t\t\t\t\tfrontImage: '/static/images/bamboo.svg',\n\t\t\t\t\t\t\t\t\tbackImage: '/static/images/lantern.svg',\n\t\t\t\t\t\t\t\t\tbackground: item.background,\n\t\t\t\t\t\t\t\t\ttype: 'chinese',\n\t\t\t\t\t\t\t\t\tfields: ['姓名', '职称', '公司名称', '后屏信息1', '后屏信息2']\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 如果没有选中模板，默认选择第一个\n\t\t\t\t\t\t\tif (!this.selectedTemplate && this.templates.length > 0) {\n\t\t\t\t\t\t\t\tthis.selectedTemplate = this.templates[0].id;\n\t\t\t\t\t\t\t\tthis.currentTemplate = this.templates[0];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconsole.error('模板数据格式不正确');\n\t\t\t\t\t\t\t// 使用默认模拟数据\n\t\t\t\t\t\t\tthis.useDefaultTemplates();\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('获取模板列表失败', err);\n\t\t\t\t\t\t// 使用默认模拟数据\n\t\t\t\t\t\tthis.useDefaultTemplates();\n\t\t\t\t\t},\n\t\t\t\t\tcomplete: () => {\n\t\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 使用默认模拟数据\n\t\t\tuseDefaultTemplates() {\n\t\t\t\tthis.templates = [\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 1,\n\t\t\t\t\t\tname: '中国风红色',\n\t\t\t\t\t\timage: '/static/images/public_template/33882e8f90e990a4f1f140011e7a58ce.png',\n\t\t\t\t\t\tfrontImage: '/static/images/bamboo.svg',\n\t\t\t\t\t\tbackImage: '/static/images/lantern.svg',\n\t\t\t\t\t\tbackground: 'http://www.chuantiba.com/api/storage/public/1633881600/33882e8f90e990a4f1f140011e7a58ce.png',\n\t\t\t\t\t\ttype: 'chinese',\n\t\t\t\t\t\tfields: ['姓名', '职称', '公司名称', '后屏信息1', '后屏信息2']\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 2,\n\t\t\t\t\t\tname: '中国风黑色',\n\t\t\t\t\t\timage: '/static/images/public_template/f2e05295636f1b849b791a21b5548e30.png',\n\t\t\t\t\t\tfrontImage: '/static/images/bamboo.svg',\n\t\t\t\t\t\tbackImage: '/static/images/lantern.svg',\n\t\t\t\t\t\tbackground: 'http://www.chuantiba.com/api/storage/public/1633881600/f2e05295636f1b849b791a21b5548e30.png',\n\t\t\t\t\t\ttype: 'chinese',\n\t\t\t\t\t\tfields: ['姓名', '职称', '公司名称', '后屏信息1', '后屏信息2']\n\t\t\t\t\t}\n\t\t\t\t];\n\t\t\t\t\n\t\t\t\t// 如果没有选中模板，默认选择第一个\n\t\t\t\tif (!this.selectedTemplate && this.templates.length > 0) {\n\t\t\t\t\tthis.selectedTemplate = this.templates[0].id;\n\t\t\t\t\tthis.currentTemplate = this.templates[0];\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 获取设备当前使用的模板\n\t\t\tgetDeviceTemplate(deviceId) {\n\t\t\t\t// 调用API获取设备当前使用的模板\n\t\t\t\tfetchDeviceTemplate(deviceId)\n\t\t\t\t\t.then(res => {\n\t\t\t\t\t\tif (res.code === 200 && res.data) {\n\t\t\t\t\t\t\tthis.selectedTemplate = res.data.templateId;\n\t\t\t\t\t\t\tthis.screenMode = res.data.screenMode || 'same';\n\t\t\t\t\t\t\t// 更新编辑字段\n\t\t\t\t\t\t\tif (res.data.content && res.data.content.length > 0) {\n\t\t\t\t\t\t\t\tres.data.content.forEach((item, index) => {\n\t\t\t\t\t\t\t\t\tif (this.editFields[index]) {\n\t\t\t\t\t\t\t\t\t\tthis.editFields[index].value = item.value;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\tconsole.error('获取设备模板失败', err);\n\t\t\t\t\t});\n\t\t\t},\n\t\t\t// 切换屏幕模式\n\t\t\tswitchScreenMode(mode) {\n\t\t\t\tthis.screenMode = mode;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: mode === 'same' ? '已选择前后屏相同' : '已选择前后屏不同',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 1500\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 显示模板选择器\n\t\t\tshowTemplateSelector() {\n\t\t\t\tthis.showTemplates = true;\n\t\t\t},\n\t\t\t// 关闭模板选择器\n\t\t\tcloseTemplateSelector() {\n\t\t\t\tthis.showTemplates = false;\n\t\t\t},\n\t\t\t// 显示内容编辑器\n\t\t\tshowContentEditor() {\n\t\t\t\t// 从temp.json加载模板数据\n\t\t\t\tuni.request({\n\t\t\t\t\turl: '/pages/public/tempjson/temp.json',\n\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.data && res.data.code === 200 && res.data.data && res.data.data.length > 0) {\n\t\t\t\t\t\t\t// 获取第一个模板的ID和背景\n\t\t\t\t\t\t\tconst templateId = res.data.data[0].id;\n\t\t\t\t\t\t\tconst templateBackground = res.data.data[0].background;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 更新当前模板和选中模板\n\t\t\t\t\t\t\tif (!this.currentTemplate) {\n\t\t\t\t\t\t\t\tthis.currentTemplate = {\n\t\t\t\t\t\t\t\t\tid: templateId,\n\t\t\t\t\t\t\t\t\tbackground: templateBackground,\n\t\t\t\t\t\t\t\t\tfrontImage: '/static/images/bamboo.svg',\n\t\t\t\t\t\t\t\t\tbackImage: '/static/images/lantern.svg',\n\t\t\t\t\t\t\t\t\ttype: 'chinese',\n\t\t\t\t\t\t\t\t\tfields: ['姓名', '职称', '公司名称', '后屏信息1', '后屏信息2']\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// 更新现有模板的背景\n\t\t\t\t\t\t\t\tthis.currentTemplate.background = templateBackground;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tthis.selectedTemplate = templateId;\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('加载模板数据失败', err);\n\t\t\t\t\t},\n\t\t\t\t\tcomplete: () => {\n\t\t\t\t\t\t// 显示编辑器弹窗\n\t\t\t\t\t\tthis.showEditor = true;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 关闭内容编辑器\n\t\t\tcloseContentEditor() {\n\t\t\t\tthis.showEditor = false;\n\t\t\t},\n\t\t\t// 保存编辑内容\n\t\t\tsaveContent() {\n\t\t\t\tthis.showEditor = false;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '内容已保存',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 选择模板\n\t\t\tselectTemplate(template) {\n\t\t\t\tthis.selectedTemplate = template.id;\n\t\t\t\tthis.currentTemplate = template;\n\t\t\t\tthis.showTemplates = false;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: `已选择${template.name}模板`,\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 根据模板更新编辑字段\n\t\t\t\tif (template.fields && template.fields.length > 0) {\n\t\t\t\t\tconst newEditFields = [];\n\t\t\t\t\ttemplate.fields.forEach((field, index) => {\n\t\t\t\t\t\t// 保留已有的值\n\t\t\t\t\t\tconst existingValue = this.editFields[index] ? this.editFields[index].value : '';\n\t\t\t\t\t\tnewEditFields.push({\n\t\t\t\t\t\t\tlabel: field,\n\t\t\t\t\t\t\tvalue: existingValue,\n\t\t\t\t\t\t\tplaceholder: `请输入${field}`\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t\tthis.editFields = newEditFields;\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 确认投屏\n\t\t\tconfirmScreen() {\n\t\t\t\t// 验证表单\n\t\t\t\tlet isValid = true;\n\t\t\t\tfor (let field of this.editFields) {\n\t\t\t\t\tif (!field.value) {\n\t\t\t\t\t\tisValid = false;\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: `请输入${field.label}`,\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (isValid) {\n\t\t\t\t\t// 构建投屏数据\n\t\t\t\t\tconst screenData = {\n\t\t\t\t\t\tdeviceId: this.deviceInfo.id,\n\t\t\t\t\t\ttemplateId: this.selectedTemplate,\n\t\t\t\t\t\tscreenMode: this.screenMode,\n\t\t\t\t\t\tcontent: this.editFields.map(field => ({\n\t\t\t\t\t\t\tlabel: field.label,\n\t\t\t\t\t\t\tvalue: field.value\n\t\t\t\t\t\t}))\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\t// 发送投屏请求\n\t\t\t\t\tconsole.log('投屏数据:', screenData);\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '投屏中...'\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 模拟投屏请求\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '投屏成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 投屏成功后返回\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t}, 2000);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n.container {\n\tflex: 1;\n\tbackground-color: #f5f5f5;\n\tbackground-image: url('/static/images/bg.svg');\n\tbackground-size: cover;\n\tbackground-position: center;\n}\n\n.navbar {\n\tflex-direction: row;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 20rpx 40rpx;\n\tpadding-top: calc(20rpx + var(--status-bar-height));\n\tbackground-color: transparent;\n}\n\n.left-capsule {\n\twidth: 80rpx;\n}\n\n.capsule-btn {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tborder-radius: 30rpx;\n\tbackground-color: rgba(255, 255, 255, 0.8);\n\tjustify-content: center;\n\talign-items: center;\n}\n\n.nav-title {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\tcolor: #ffffff;\n}\n\n.right-capsule {\n\twidth: 80rpx;\n}\n\n.content {\n\tpadding: 30rpx;\n\tflex: 1;\n}\n\n/* 屏幕模式选择标签 */\n.screen-mode-tabs {\n\tbackground-color: #8B0000; /* 深红色背景 */\n\tborder-radius: 8rpx;\n\toverflow: hidden;\n\tmargin-bottom: 30rpx;\n}\n\n.tab-row {\n\tdisplay: flex;\n\tflex-direction: row;\n\twidth: 100%;\n}\n\n.tab-item {\n\theight: 80rpx;\n\tjustify-content: center;\n\talign-items: center;\n\tdisplay: flex;\n}\n\n.tab-item text {\n\tcolor: #ffffff;\n\tfont-size: 28rpx;\n}\n\n.tab-item.active {\n\tbackground-color: #FF0000; /* 鲜红色 */\n}\n\n/* 预览区域 */\n.preview-area {\n\tmargin-bottom: 30rpx;\n}\n\n.preview-card {\n\tbackground-color: #ffffff;\n\t// border-radius: 16rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n\t//margin-bottom: 20rpx;\n}\n\n.template-preview {\n\tpadding: 20rpx;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n/* 前后屏不同模式的卡片布局 */\n.preview-cards {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n/* 中国风模板样式 */\n.chinese-style-template {\n\twidth: 100%;\n\theight: 400rpx;\n\tposition: relative;\n\tborder: 2rpx solid #000000;\n\tborder-radius: 8rpx;\n\toverflow: hidden;\n\tbackground-color: #ffffff;\n\tbackground-size: cover;\n\tbackground-position: center;\n\tbackground-repeat: no-repeat;\n}\n\n/* 前后屏标签 */\n.screen-tag {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 60rpx;\n\theight: 60rpx;\n\tbackground-color: #8B0000;\n\tcolor: #ffffff;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tborder-bottom-right-radius: 8rpx;\n}\n \n.template-content {\n\twidth: 100%;\n\theight: 100%;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 40rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n\tbackground-color: rgba(255, 255, 255, 0.7);\n\tborder-radius: 8rpx;\n\tmargin: 20rpx;\n}\n\n/* 姓名和职称容器 */\n.name-title-container {\n\tdisplay: flex;\n\tflex-direction: row;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-bottom: 20rpx;\n}\n\n.for-front-screen {\n\t// display: flex;\n\t// flex-direction: column;\n\twidth: 100%; /* 或指定具体的宽度 */\n    height: auto; /* 保持图片的原始比例 */\n    object-fit: cover; /* 覆盖容器，可能会裁剪图片 */\n}\n\n\n.content-divider {\n\tfont-size: 60rpx;\n\tfont-weight: bold;\n\tmargin: 0 20rpx;\n}\n\n.content-title {\n\tfont-size: 60rpx;\n\tfont-weight: bold;\n\ttext-shadow: 1rpx 1rpx 3rpx rgba(0, 0, 0, 0.3);\n}\n\n.company-name {\n\tfont-size: 36rpx;\n\tcolor: #FF0000;\n\tbackground: linear-gradient(to right, #FF0000, #000000);\n\t-webkit-background-clip: text;\n\t-webkit-text-fill-color: transparent;\n\tpadding: 10rpx 0;\n\ttext-shadow: 0 0 2rpx rgba(255, 255, 255, 0.5);\n}\n\n.company-name-small {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tmargin-top: 10rpx;\n\tfont-weight: bold;\n\ttext-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.8);\n}\n\n.content-subtitle {\n\tfont-size: 36rpx;\n\tcolor: #333;\n\tfont-weight: bold;\n\ttext-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.8);\n}\n\n/* 底部操作按钮 */\n.operation-buttons {\n\tmargin-bottom: 30rpx;\n\tbackground-color: #ffffff;\n\t// border-radius: 16rpx;\n\tpadding: 20rpx;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n\tposition: relative;\n}\n\n.op-button-row {\n\tdisplay: flex;\n\tflex-direction: row;\n\twidth: 100%;\n\tposition: relative;\n}\n\n/* 删除button-divider样式 */\n\n.bottom-gradient-line {\n\theight: 4rpx;\n\twidth: 100%;\n\tbackground: linear-gradient(to right, #8B0000, #000000);\n\tposition: absolute;\n\tbottom: 0;\n\tleft: 0;\n\tborder-bottom-left-radius: 16rpx;\n\tborder-bottom-right-radius: 16rpx;\n}\n.op-button {\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 20rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.op-icon {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tmargin-bottom: 10rpx;\n}\n\n.op-text {\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n/* 底部确认按钮 */\n.footer {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground-color: transparent;\n\tpadding: 20rpx 30rpx;\n\t// padding-bottom: calc(20rpx + env(safe-area-inset-bottom));\n\tdisplay: flex;\n\tjustify-content: center;\n}\n\n.confirm-button {\n\tbackground-color: #8B0000; /* 深红色 */\n\theight: 90rpx;\n\tborder-radius: 45rpx;\n\tjustify-content: center;\n\talign-items: center;\n\tbox-shadow: 0 4rpx 10rpx rgba(139, 0, 0, 0.3);\n\tdisplay: flex;\n\twidth: 80%;\n}\n\n.confirm-button text {\n\tcolor: #ffffff;\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\talign-items: center;\n}\n\n/* 弹窗样式 */\n.template-popup, .editor-popup {\n\tpadding: 30rpx;\n\tbackground-color: #ffffff;\n\tborder-top-left-radius: 20rpx;\n\tborder-top-right-radius: 20rpx;\n}\n\n.popup-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tmargin-bottom: 30rpx;\n\ttext-align: center;\n}\n\n.template-scroll {\n\twidth: 100%;\n\theight: 300rpx;\n}\n\n.template-list {\n\tflex-direction: row;\n\tpadding: 10rpx 0;\n}\n\n.template-item {\n\twidth: 200rpx;\n\tmargin-right: 20rpx;\n\talign-items: center;\n}\n\n.template-image {\n\twidth: 180rpx;\n\theight: 240rpx;\n\tborder-radius: 8rpx;\n\tborder: 2rpx solid #eee;\n}\n\n.template-item.active .template-image {\n\tborder: 2rpx solid #8B0000;\n}\n\n.template-name {\n\tfont-size: 24rpx;\n\tmargin-top: 10rpx;\n\ttext-align: center;\n}\n\n.edit-form {\n\tmargin-top: 20rpx;\n\tmargin-bottom: 30rpx;\n}\n\n.form-item {\n\tmargin-bottom: 20rpx;\n}\n\n.form-label {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tmargin-bottom: 10rpx;\n}\n\n.form-input {\n\theight: 80rpx;\n\tborder: 1px solid #ddd;\n\tborder-radius: 8rpx;\n\tpadding: 0 20rpx;\n\tfont-size: 28rpx;\n}\n\n.popup-button {\n\tbackground-color: #8B0000; /* 深红色 */\n\theight: 80rpx;\n\tborder-radius: 40rpx;\n\tjustify-content: center;\n\talign-items: center;\n}\n\n.popup-button text {\n\tcolor: #ffffff;\n\tfont-size: 30rpx;\n\tfont-weight: bold;\n}\n</style>", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./batch-screen.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./batch-screen.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751873716792\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}