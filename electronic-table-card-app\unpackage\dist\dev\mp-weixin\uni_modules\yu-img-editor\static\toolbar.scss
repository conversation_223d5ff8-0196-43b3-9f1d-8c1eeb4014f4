.yu-toolbar-wrapper {
	.yu-toolbar-mask {
		position: fixed;
		left: 0;
		top: 0;
		bottom: 0;
		right: 0;
		z-index: -1;
		background: rgba(0, 0, 0, 0.2);
		opacity: 0;
		visibility: hidden;
		transition: all .3s;
		&.show {
			z-index: 1;
			opacity: 1;
			visibility: visible;
		}
	}

	.yu-toolbar-dialog {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 2;
		background-color: #fff;
		border-radius: 10px 10px 0 0;
		transition: all .3s;
		transform: translateY(100%);
		opacity: 0;
		visibility: hidden;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);

		&.show {
			opacity: 1;
			visibility: visible;
			transform: translateY(0);
		}
	}

	.yu-tool-tab {
		display: flex;
		align-items: center;
		gap: 10px;
		padding: 15px 15px 5px;

		.list {
			flex: 1;
			text-align: center;
			color: #666;
			line-height: 0;

			&.list-active {
				color: #0754c8;

				.text {
					color: #0754c8;
				}
			}

			.text {
				padding-top: 3px;
				font-size: 12px;
				line-height: 1.5;
				color: #999;
			}
		}
	}

	.yu-tool-content {
		max-height: 100vw;
		min-height: 160px;
		overflow: hidden;
		.bg-settting{
			padding-top: 12px;
			padding-bottom: 4px;
		}

		.tool-title{
			position: relative;
			font-size: 13px;
			line-height: 1;
			color: #999;
			padding-left: 8px;
			padding-top: 8px;
			padding-bottom: 8px;
			margin: 0 15px;
			&::after{
				content:"";
				position: absolute;
				left:0;
				top: 50%;
				transform: translateY(-50%);
				z-index: 1;
				width: 4px;
				height: 11px;
				border-radius: 10px;
				background-color: #3565ee;
			}
		}

		.color-wrapper{
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			gap: 8px;
			padding: 12px 15px;
			.color-list{
				width: calc(10% - 7.2px);
				aspect-ratio: 1 / 1;
				color: #666;
				height: auto;
				text-align: center;
				font-size: 14px;
				background-color: #f5f6f7;
				box-sizing: border-box;
				border-radius: 5px;
				display: flex;
				align-items: center;
				justify-content: center;
				border-width: 1px;
				border-style: solid;
				border-color: transparent;
			}
		}

		.image-wrapper{
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			gap: 10px;
			padding: 12px 15px;

			.image-list{
				width: calc(20% - 8px);
				color: #666;
				line-height: 0;
				&.image-add{
					aspect-ratio: 1 / 1;
					height: auto;
					display: flex;
					align-items: center;
					justify-content: center;
					border: 1px dashed #ccc;;
					border-radius: 5px;
					box-sizing: border-box;
				}
				.img{
					width: 100%;
					aspect-ratio: 1 / 1;
					height: auto;
					border-radius: 5px;
				}
			}
		}

		.font-setting {
			display: flex;
			flex-direction: column;
			padding: 15px;
			gap: 12px;

			.font-item {
				background-color: #f5f6f7;
				padding: 4px 10px;
				gap: 6px;
				display: flex;
				align-items: center;
				border-radius: 5px;

				.label {
					width:50px;
					font-size: 15px;
					line-height: 24px;
					color: #808080;
				}

				.content {
					flex: 1;
					width: 0;
					&.content-inp{
						padding: 8px;
						line-height: 0;
					}
					.inp {
						width: 100%;
						font-size: 15px;
						line-height: 1.4;
					}
				}
				.content-icon {
					display: flex;
					align-items: center;
					// justify-content: space-between;
					gap: 10px;
					padding: 2px 0;
					.icon-list{
						width: 36px;
						height: 36px;
						display: flex;
						align-items: center;
						justify-content: center;
						background-color: #fff;
						color: #666;
						border-radius: 5px;
						line-height: 0;
						&.icon-list-active{
							color: #3565ee;
							.text{
								color: #3565ee;
							}
						}
					}
				}
			}
		}

		.font-family-list {
			display: flex;
			flex-direction: column;
			gap: 12px;
			padding: 15px;
			height: 100%;
			overflow: auto;
			.list {
				background-color: #f5f6f7;
				padding: 4px 10px;
				gap: 6px;
				font-size: 15px;
				color: #666;
				height: 32px;
				display: flex;
				align-items: center;
				border-radius: 5px;

				&.list-active {
					color: #3565ee;
					background-color: #f4f7ff;
				}
			}
		}

		.font-level {
			padding-top: 20px;
		}


	}

	.yu-tool-layer{
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		gap: 16px 12px;
		padding: 18px 15px 15px;
		.layer-list{
			width: calc(25% - 9px);
			text-align: center;
			color: #666;
			line-height: 0;
			&.layer-list-active{
				color: #3565ee;
				.text{
					color: #3565ee;
				}
			}
			.text{
				padding-top: 3px;
				font-size: 12px;
				line-height: 1.5;
				color: #999;
			}
		}
	}

	.yu-tool-title {
		display: flex;
		align-items: center;
		gap: 10px;
		padding: 4px;
		.title{
			flex: 1;
			text-align: center;
			font-size: inherit;
			line-height: 1.5;
			font-weight: 500;
			color: #333;
		}
		.icon-wrapper {
			padding: 8px;
			width: 24px;
			height: 24px;
			line-height: 0;
			.icon{
				width: 24px;
				height: 24px;
			}
		}
	}
}
