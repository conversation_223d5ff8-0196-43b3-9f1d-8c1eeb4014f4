@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  position: relative;
  min-height: 100vh;
  background-image: url("http://14.103.146.84:8890/i/2025/06/12/login-bg.png");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  overflow-x: hidden;
}
.status-bar {
  width: 100%;
  background: transparent;
}
.custom-navbar {
  position: relative;
  z-index: 100;
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 40rpx;
  height: 88rpx;
}
.navbar-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
}
.navbar-right {
  display: flex;
  align-items: center;
}
.weather-info {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
}
.weather-text {
  font-size: 24rpx;
  color: #FFFFFF;
}
.content {
  padding: 40rpx 30rpx;
  padding-bottom: 120rpx;
}
/* 欢迎卡片 */
.welcome-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  margin-bottom: 40rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
}
.welcome-text {
  flex: 1;
}
.greeting {
  font-size: 48rpx;
  font-weight: bold;
  color: #2C3E50;
  display: block;
  margin-bottom: 8rpx;
}
.welcome-desc {
  font-size: 28rpx;
  color: #666;
  display: block;
}
.welcome-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.icon-bluetooth {
  font-size: 40rpx;
}
.card-decoration {
  position: absolute;
  right: -20rpx;
  top: -20rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-radius: 50rpx;
}
/* 快速功能区 */
.quick-actions {
  margin-bottom: 40rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #FFFFFF;
  margin-bottom: 24rpx;
  display: block;
}
.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}
.action-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}
.action-item:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}
.action-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}
.action-icon.single {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.action-icon.batch {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}
.action-icon.template {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}
.action-icon.meeting {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}
.action-icon .icon {
  font-size: 36rpx;
}
.action-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
/* 统计信息卡片 */
.stats-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}
.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}
.stats-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 8rpx;
  display: block;
}
.stat-label {
  font-size: 24rpx;
  color: #666;
  display: block;
}
.stat-divider {
  width: 2rpx;
  height: 60rpx;
  background: #eee;
  margin: 0 20rpx;
}
/* 最近使用 */
.recent-section {
  margin-bottom: 40rpx;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.more-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}
.recent-list {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}
.recent-item {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}
.recent-item:last-child {
  border-bottom: none;
}
.recent-item:active {
  background-color: #f8f9fa;
}
.recent-icon {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.recent-icon .icon {
  font-size: 28rpx;
}
.recent-info {
  flex: 1;
}
.recent-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 4rpx;
  display: block;
}
.recent-time {
  font-size: 24rpx;
  color: #999;
  display: block;
}
.arrow {
  font-size: 32rpx;
  color: #ccc;
}
/* 背景装饰 */
.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
}
.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
}
.circle1 {
  width: 300rpx;
  height: 300rpx;
  top: 20%;
  right: -100rpx;
  -webkit-animation: float1 20s infinite ease-in-out;
          animation: float1 20s infinite ease-in-out;
}
.circle2 {
  width: 200rpx;
  height: 200rpx;
  bottom: 30%;
  left: -50rpx;
  -webkit-animation: float2 15s infinite ease-in-out;
          animation: float2 15s infinite ease-in-out;
}
.circle3 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  right: 20%;
  -webkit-animation: float3 25s infinite ease-in-out;
          animation: float3 25s infinite ease-in-out;
}
@-webkit-keyframes float1 {
0%, 100% {
    -webkit-transform: translateY(0px) rotate(0deg);
            transform: translateY(0px) rotate(0deg);
}
50% {
    -webkit-transform: translateY(-30rpx) rotate(180deg);
            transform: translateY(-30rpx) rotate(180deg);
}
}
@keyframes float1 {
0%, 100% {
    -webkit-transform: translateY(0px) rotate(0deg);
            transform: translateY(0px) rotate(0deg);
}
50% {
    -webkit-transform: translateY(-30rpx) rotate(180deg);
            transform: translateY(-30rpx) rotate(180deg);
}
}
@-webkit-keyframes float2 {
0%, 100% {
    -webkit-transform: translateX(0px) rotate(0deg);
            transform: translateX(0px) rotate(0deg);
}
50% {
    -webkit-transform: translateX(20rpx) rotate(-180deg);
            transform: translateX(20rpx) rotate(-180deg);
}
}
@keyframes float2 {
0%, 100% {
    -webkit-transform: translateX(0px) rotate(0deg);
            transform: translateX(0px) rotate(0deg);
}
50% {
    -webkit-transform: translateX(20rpx) rotate(-180deg);
            transform: translateX(20rpx) rotate(-180deg);
}
}
@-webkit-keyframes float3 {
0%, 100% {
    -webkit-transform: translate(0px, 0px) rotate(0deg);
            transform: translate(0px, 0px) rotate(0deg);
}
33% {
    -webkit-transform: translate(20rpx, -20rpx) rotate(120deg);
            transform: translate(20rpx, -20rpx) rotate(120deg);
}
66% {
    -webkit-transform: translate(-20rpx, 10rpx) rotate(240deg);
            transform: translate(-20rpx, 10rpx) rotate(240deg);
}
}
@keyframes float3 {
0%, 100% {
    -webkit-transform: translate(0px, 0px) rotate(0deg);
            transform: translate(0px, 0px) rotate(0deg);
}
33% {
    -webkit-transform: translate(20rpx, -20rpx) rotate(120deg);
            transform: translate(20rpx, -20rpx) rotate(120deg);
}
66% {
    -webkit-transform: translate(-20rpx, 10rpx) rotate(240deg);
            transform: translate(-20rpx, 10rpx) rotate(240deg);
}
}
