<view class="container data-v-35706aac"><view class="logo-container data-v-35706aac"><image class="logo data-v-35706aac" src="http://14.103.146.84:8890/i/2025/06/12/logo.png" mode="aspectFit"></image></view><view class="form-container data-v-35706aac"><view class="input-group data-v-35706aac"><vol-form vue-id="789d6bda-1" formFields="{{formData}}" formOptions="{{formOptions}}" labelWidth="{{0}}" padding="0" class="data-v-35706aac" bind:__l="__l"></vol-form></view><view class="verification-group data-v-35706aac"><view class="verification-input data-v-35706aac"><vol-form vue-id="789d6bda-2" formFields="{{codeData}}" formOptions="{{codeOptions}}" labelWidth="{{0}}" padding="0" class="data-v-35706aac" bind:__l="__l"></vol-form></view><view data-event-opts="{{[['tap',[['getVerificationCode',['$event']]]]]}}" class="{{['get-code-btn','data-v-35706aac',(countdown>0)?'disabled':'']}}" bindtap="__e"><text class="get-code-text data-v-35706aac">{{countdown>0?countdown+'s':'获取验证码'}}</text></view></view><view class="reset-btn-container data-v-35706aac"><view data-event-opts="{{[['tap',[['resetPassword',['$event']]]]]}}" class="reset-btn data-v-35706aac" bindtap="__e"><text class="reset-text data-v-35706aac">修改密码</text></view></view></view><view class="back-container data-v-35706aac"><text data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="back-text data-v-35706aac" bindtap="__e">返回登录</text></view></view>