<template>
	<view class="container">
		<view class="logo-container">
			<image src="http://*************:8890/i/2025/06/12/logo.png" class="logo" mode="aspectFit"></image>
		</view>
		
		<view class="form-container">
			<view class="input-group">
				<vol-form :formFields="formData" :formOptions="formOptions" :labelWidth="0" padding="0">
				</vol-form>
			</view>

			<view class="verification-group">
				<view class="verification-input">
					<vol-form :formFields="codeData" :formOptions="codeOptions" :labelWidth="0" padding="0">
					</vol-form>
				</view>
				<view class="get-code-btn" @click="getVerificationCode" :class="{'disabled': countdown > 0}">
					<text class="get-code-text">{{countdown > 0 ? countdown + 's' : '获取验证码'}}</text>
				</view>
			</view>

			<view class="reset-btn-container">
				<view class="reset-btn" @click="resetPassword">
					<text class="reset-text">修改密码</text>
				</view>
			</view>
		</view>
		
		<view class="back-container">
			<text class="back-text" @click="goBack">返回登录</text>
		</view>
	</view>
</template>

<script>
	// 添加平台检测
	const getPlatform = () => {
		let platform = '';
		// #ifdef APP-PLUS
		platform = 'APP-PLUS';
		// #endif
		// #ifdef APP-PLUS-NVUE
		platform = 'APP-NVUE';
		// #endif
		// #ifdef H5
		platform = 'H5';
		// #endif
		// #ifdef MP-WEIXIN
		platform = 'MP-WEIXIN';
		// #endif
		// #ifdef MP-ALIPAY
		platform = 'MP-ALIPAY';
		// #endif
		// #ifdef MP-BAIDU
		platform = 'MP-BAIDU';
		// #endif
		// #ifdef MP-TOUTIAO
		platform = 'MP-TOUTIAO';
		// #endif
		// #ifdef MP-QQ
		platform = 'MP-QQ';
		// #endif
		console.log('页面运行平台:', platform);
		return platform;
	};
	
	export default {
		data() {
			return {
				countdown: 0,
				timer: null,
				formData: {
					phone: '',
					newPassword: '',
					confirmPassword: ''
				},
				codeData: {
					verificationCode: ''
				},
				formOptions: [
					{
						field: 'phone',
						title: '手机号',
						type: 'text',
						placeholder: '请输入手机号'
					},
					{
						field: 'newPassword',
						title: '新密码',
						type: 'password',
						placeholder: '请输入新密码'
					},
					{
						field: 'confirmPassword',
						title: '确认密码',
						type: 'password',
						placeholder: '请确认新密码'
					}
				],
				codeOptions: [
					{
						field: 'verificationCode',
						title: '验证码',
						type: 'text',
						placeholder: '请输入验证码'
					}
				],
				currentPlatform: getPlatform()
			}
		},
		created() {
			// 添加平台兼容性处理
			if (this.currentPlatform !== 'MP-WEIXIN' && typeof wx === 'undefined') {
				console.log('当前平台不支持wx对象，将使用uni API替代');
			}
		},
		methods: {
			getVerificationCode() {
				// 如果正在倒计时，不允许重复点击
				if (this.countdown > 0) {
					return;
				}

				// 获取验证码逻辑
				console.log('获取验证码', this.formData.phone);
				if (!this.formData.phone) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					});
					return;
				}

				// 开始倒计时
				this.startCountdown();

				// 这里添加发送验证码的API调用
				uni.showToast({
					title: '验证码已发送',
					icon: 'success'
				});
			},
			startCountdown() {
				this.countdown = 60;
				this.timer = setInterval(() => {
					this.countdown--;
					if (this.countdown <= 0) {
						clearInterval(this.timer);
						this.timer = null;
					}
				}, 1000);
			},
			resetPassword() {
				// 重置密码逻辑
				console.log('重置密码', this.formData.phone, this.formData.newPassword, this.formData.confirmPassword, this.codeData.verificationCode);
				if (!this.formData.phone || !this.formData.newPassword || !this.formData.confirmPassword || !this.codeData.verificationCode) {
					uni.showToast({
						title: '请填写完整信息',
						icon: 'none'
					});
					return;
				}
				if (this.formData.newPassword !== this.formData.confirmPassword) {
					uni.showToast({
						title: '两次密码输入不一致',
						icon: 'none'
					});
					return;
				}
				// 这里添加重置密码的API调用
				uni.showToast({
					title: '密码修改成功',
					icon: 'success'
				});
				// 延迟跳转到登录页面
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			},
			goBack() {
				// 返回登录页面
				uni.navigateBack();
			}
		},
		beforeDestroy() {
			// 清理定时器
			if (this.timer) {
				clearInterval(this.timer);
				this.timer = null;
			}
		}
	}
</script>

<style scoped>
.container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: space-between;
	height: 100vh;
	background-image: url('http://*************:8890/i/2025/06/12/layout_ornament.png');
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;
	padding: 0 20px;
	box-sizing: border-box;
	overflow: hidden;
}

.logo-container {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 50px;
	margin-bottom: 40px;
}

.logo {
	width: 100px;
	height: 100px;
}

.form-container {
	flex: 1;
	width: 100%;
	max-width: 350px;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.input-group {
	width: 100%;
	margin-bottom: 20px;
}

.title-container {
	margin-bottom: 30px;
}

.title-text {
	color: #2C1810;
	font-size: 28px;
	font-weight: 900;
	letter-spacing: 4px;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 1px 1px 2px rgba(139,69,19,0.4);
	text-align: center;
}

.verification-group {
	width: 100%;
	margin-bottom: 30px;
	display: flex;
	flex-direction: row;
	align-items: flex-end;
	gap: 12px;
}

.verification-input {
	flex: 1;
}

.get-code-btn {
	width: 110px;
	height: 48px;
	background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(139, 69, 19, 0.2);
	border: 1px solid rgba(139, 69, 19, 0.3);
	transition: all 0.3s ease;
}

.get-code-btn:active {
	transform: translateY(1px);
	box-shadow: 0 1px 4px rgba(139, 69, 19, 0.3);
}

.get-code-btn.disabled {
	background: rgba(139, 69, 19, 0.3);
	color: rgba(255, 255, 255, 0.6);
	cursor: not-allowed;
}

.get-code-text {
	color: #FFFFFF;
	font-size: 13px;
	font-weight: 600;
	letter-spacing: 0.5px;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

.reset-btn-container {
	width: 100%;
	margin-top: 30px;
	margin-bottom: 20px;
}

.reset-btn {
	width: 100%;
	height: 52px;
	background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
	border-radius: 26px;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	overflow: hidden;
	box-shadow: 0 4px 16px rgba(139, 69, 19, 0.3);
	border: none;
	transition: all 0.3s ease;
}

.reset-btn:active {
	transform: translateY(2px);
	box-shadow: 0 2px 8px rgba(139, 69, 19, 0.4);
}

.reset-text {
	color: #FFFFFF;
	font-size: 18px;
	font-weight: 600;
	letter-spacing: 2px;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
	position: relative;
	z-index: 10;
}

.back-container {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 40px;
	padding: 10px;
}

.back-text {
	color: #8B4513;
	font-size: 16px;
	font-weight: 500;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
	letter-spacing: 1px;
	text-decoration: underline;
	text-decoration-color: rgba(139, 69, 19, 0.5);
}
</style>
