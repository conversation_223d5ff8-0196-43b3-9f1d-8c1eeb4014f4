<view class="container"><view class="room-header"><view data-event-opts="{{[['tap',[['showRoomSelector',['$event']]]]]}}" class="room-info" bindtap="__e"><text class="room-name">{{"会议室名称："+roomName}}</text></view><block wx:if="{{hasPeople}}"><view class="select-all"><checkbox checked="{{isAllSelected}}" color="#8B0000" data-event-opts="{{[['tap',[['toggleSelectAll',['$event']]]]]}}" bindtap="__e"></checkbox><text class="select-all-text">全选</text></view></block></view><block wx:if="{{!hasPeople}}"><view class="empty-state"><view class="empty-content"><view data-event-opts="{{[['tap',[['addPerson',['$event']]]]]}}" class="action-button add-person" bindtap="__e"><image class="button-bg" src="http://14.103.146.84:8890/i/2025/06/12/btn.png" mode="aspectFill"></image><text class="action-text">新增人员</text></view><view data-event-opts="{{[['tap',[['batchImport',['$event']]]]]}}" class="action-button batch-import" bindtap="__e"><image class="button-bg" src="http://14.103.146.84:8890/i/2025/06/12/btn.png" mode="aspectFill"></image><text class="action-text">批量导入人员</text></view></view></view></block><block wx:if="{{hasPeople}}"><view class="people-list"><block wx:for="{{peopleList}}" wx:for-item="person" wx:for-index="index" wx:key="index"><view class="person-item"><view class="person-header"><text class="desk-number">{{"桌牌编号："+person.deskNumber}}</text><checkbox checked="{{person.selected}}" color="#8B0000" data-event-opts="{{[['tap',[['togglePersonSelect',['$0'],[[['peopleList','',index]]]]]]]}}" bindtap="__e"></checkbox></view><view class="person-details"><view class="detail-row"><view class="detail-item"><text class="detail-label">姓名：</text><text class="detail-value">{{person.name}}</text></view><view class="detail-item"><text class="detail-label">职称：</text><text class="detail-value">{{person.title}}</text></view></view><view class="detail-row"><view class="detail-item"><text class="detail-label">工号：</text><text class="detail-value">{{person.employeeId}}</text></view><view class="detail-item"><text class="detail-label">公司名称：</text><text class="detail-value">{{person.company}}</text></view></view></view><view class="person-actions"><view data-event-opts="{{[['tap',[['deletePerson',['$0'],[[['peopleList','',index]]]]]]]}}" class="action-btn delete-btn" bindtap="__e"><text>删除人员</text></view><view data-event-opts="{{[['tap',[['editPerson',['$0'],[[['peopleList','',index]]]]]]]}}" class="action-btn edit-btn" bindtap="__e"><text>编辑人员</text></view></view></view></block></view></block><block wx:if="{{hasPeople}}"><view class="floating-buttons"><view data-event-opts="{{[['tap',[['addPerson',['$event']]]]]}}" class="float-btn add-btn" bindtap="__e"><text class="float-btn-text">+</text></view><view data-event-opts="{{[['tap',[['importExcel',['$event']]]]]}}" class="float-btn excel-btn" bindtap="__e"><text class="excel-text">EXL</text></view></view></block><block wx:if="{{hasPeople&&hasSelected}}"><view class="bottom-action"><view data-event-opts="{{[['tap',[['deleteSelected',['$event']]]]]}}" class="delete-selected-btn" bindtap="__e"><text>删除选中</text></view></view></block><block wx:if="{{showDeskModal}}"><view data-event-opts="{{[['tap',[['closeDeskModal',['$event']]]]]}}" class="desk-modal" bindtap="__e"><view data-event-opts="{{[['tap',[['stopPropagation',['$event']]]]]}}" class="desk-modal-content" catchtap="__e"><view class="modal-header"><text class="modal-title">选择桌牌编号</text><text data-event-opts="{{[['tap',[['closeDeskModal',['$event']]]]]}}" class="close-btn" bindtap="__e">×</text></view><view class="desk-list"><block wx:for="{{$root.l0}}" wx:for-item="desk" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['selectDesk',['$0'],[[['availableDeskList','id',desk.$orig.id]]]]]]]}}" class="{{['desk-option',(desk.$orig.status!=='available')?'disabled':'']}}" bindtap="__e"><view class="desk-info"><text class="desk-number">{{desk.$orig.number}}</text><text class="desk-gateway">{{desk.$orig.gatewayName}}</text><text class="{{['desk-status',desk.$orig.status]}}">{{desk.m0}}</text></view><block wx:if="{{currentPerson.deskNumber===desk.$orig.number}}"><view class="desk-check">✓</view></block></view></block></view></view></view></block><block wx:if="{{showRoomModal}}"><view data-event-opts="{{[['tap',[['closeRoomModal',['$event']]]]]}}" class="room-modal" bindtap="__e"><view data-event-opts="{{[['tap',[['stopPropagation',['$event']]]]]}}" class="room-modal-content" catchtap="__e"><view class="modal-header"><text class="modal-title">选择会议室</text><view data-event-opts="{{[['tap',[['closeRoomModal',['$event']]]]]}}" class="close-btn" bindtap="__e"><text>×</text></view></view><view class="room-list"><block wx:for="{{roomList}}" wx:for-item="room" wx:for-index="__i1__" wx:key="id"><view data-event-opts="{{[['tap',[['selectRoom',['$0'],[[['roomList','id',room.id]]]]]]]}}" class="room-option" bindtap="__e"><text class="room-option-name">{{room.name}}</text><block wx:if="{{room.name===roomName}}"><view class="room-option-check"><text>✓</text></view></block></view></block></view></view></view></block><block wx:if="{{showPersonModal}}"><view data-event-opts="{{[['tap',[['closePersonModal',['$event']]]]]}}" class="person-modal" bindtap="__e"><view data-event-opts="{{[['tap',[['stopPropagation',['$event']]]]]}}" class="person-modal-content" catchtap="__e"><view class="modal-header"><text class="modal-title">{{personModalType==='add'?'新增人员':'编辑人员'}}</text><view data-event-opts="{{[['tap',[['closePersonModal',['$event']]]]]}}" class="close-btn" bindtap="__e"><text>×</text></view></view><view class="person-form"><view class="form-item"><text class="form-label">桌牌编号：</text><view data-event-opts="{{[['tap',[['showDeskSelector',['$event']]]]]}}" class="form-select" bindtap="__e"><text class="{{['select-text',(!currentPerson.deskNumber)?'placeholder':'']}}">{{''+(currentPerson.deskNumber||'请选择桌牌编号')+''}}</text><text class="select-arrow">▼</text></view></view><view class="form-item"><text class="form-label">姓名</text><input class="form-input" placeholder="例如：姓名" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['currentPerson']]]]]}}" value="{{currentPerson.name}}" bindinput="__e"/></view><view class="form-item"><text class="form-label">工号</text><input class="form-input" placeholder="例如：编号" data-event-opts="{{[['input',[['__set_model',['$0','employeeId','$event',[]],['currentPerson']]]]]}}" value="{{currentPerson.employeeId}}" bindinput="__e"/></view><view class="form-item"><text class="form-label">职称</text><input class="form-input" placeholder="例如：职称" data-event-opts="{{[['input',[['__set_model',['$0','title','$event',[]],['currentPerson']]]]]}}" value="{{currentPerson.title}}" bindinput="__e"/></view><view class="form-item"><text class="form-label">公司名称</text><input class="form-input" placeholder="例如：公司" data-event-opts="{{[['input',[['__set_model',['$0','company','$event',[]],['currentPerson']]]]]}}" value="{{currentPerson.company}}" bindinput="__e"/></view></view><view class="modal-actions"><view data-event-opts="{{[['tap',[['closePersonModal',['$event']]]]]}}" class="action-btn cancel-btn" bindtap="__e"><text>取消</text></view><view data-event-opts="{{[['tap',[['confirmPerson',['$event']]]]]}}" class="action-btn confirm-btn" bindtap="__e"><text>确定</text></view></view></view></view></block></view>