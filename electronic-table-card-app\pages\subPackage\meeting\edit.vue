<template>
	<view class="container">
		<!-- 主内容区域 -->
		<view class="content">
			<view class="edit-form">
				<view class="form-title">
					<text>{{ isEdit ? '编辑会议室' : '添加会议室' }}</text>
				</view>
				
				<view class="form-item">
					<text class="form-label">会议室名称</text>
					<input class="form-input" v-model="roomInfo.name" placeholder="请输入会议室名称" />
				</view>
				
				<view class="form-item">
					<text class="form-label">设备型号</text>
					<input class="form-input" v-model="roomInfo.model" placeholder="请输入设备型号" />
				</view>
				
				<view class="form-item">
					<text class="form-label">设备版本</text>
					<input class="form-input" v-model="roomInfo.version" placeholder="请输入设备版本" />
				</view>
				
				<view class="form-item">
					<text class="form-label">设备类型</text>
					<input class="form-input" v-model="roomInfo.type" placeholder="请输入设备类型" />
				</view>
				
				<view class="form-item">
					<text class="form-label">设备编号</text>
					<input class="form-input" v-model="roomInfo.code" placeholder="请输入设备编号" />
				</view>
				
				<view class="form-item">
					<text class="form-label">设备频道</text>
					<input class="form-input" v-model="roomInfo.channel" placeholder="请输入设备频道" />
				</view>
				
				<view class="form-item">
					<text class="form-label">设备状态</text>
					<view class="status-selector">
						<view class="status-option" :class="{active: roomInfo.status === 'active'}" @click="roomInfo.status = 'active'">
							<text>启用</text>
						</view>
						<view class="status-option" :class="{active: roomInfo.status === 'inactive'}" @click="roomInfo.status = 'inactive'">
							<text>禁用</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="footer">
			<view class="button-group">
				<view class="cancel-button" @click="cancelEdit">
					<text>取消</text>
				</view>
				<view class="save-button" @click="saveRoom">
					<text>保存</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isEdit: false,
				roomId: '',
				roomInfo: {
					name: '',
					model: '',
					version: '',
					type: '',
					code: '',
					channel: '',
					status: 'active'
				}
			}
		},
		onLoad(options) {
			if (options.id) {
				this.isEdit = true;
				this.roomId = options.id;
				// 获取会议室详情
				this.getRoomDetail(options.id);
			}
		},
		methods: {
			// 获取会议室详情
			getRoomDetail(id) {
				// 这里应该调用API获取会议室详情
				// 目前使用模拟数据
				const mockData = {
					'1': {
						id: '1',
						name: '1号会议室',
						model: '未绑定',
						version: '未绑定',
						type: '未绑定',
						code: '未绑定',
						channel: '未绑定',
						status: 'active'
					},
					'2': {
						id: '2',
						name: '121212',
						model: '未绑定',
						version: '未绑定',
						type: '未绑定',
						code: '未绑定',
						channel: '未绑定',
						status: 'inactive'
					}
				};
				
				if (mockData[id]) {
					// this.roomInfo = { ...mockData[id] };
					// 使用传统方式复制对象
					const roomData = mockData[id];
					for (let key in roomData) {
						if (roomData.hasOwnProperty(key)) {
							this.roomInfo[key] = roomData[key];
						}
					}
				}
			},
			
			// 取消编辑
			cancelEdit() {
				uni.navigateBack();
			},
			
			// 保存会议室信息
			saveRoom() {
				// 表单验证
				if (!this.roomInfo.name) {
					uni.showToast({
						title: '请输入会议室名称',
						icon: 'none'
					});
					return;
				}
				
				// 构建保存数据
				const saveData = {};
				// 复制表单数据
				for (let key in this.roomInfo) {
					if (this.roomInfo.hasOwnProperty(key)) {
						saveData[key] = this.roomInfo[key];
					}
				}
				// 添加ID
				saveData.id = this.isEdit ? this.roomId : new Date().getTime().toString();
				
				console.log('保存会议室信息:', saveData);
				
				// 这里应该调用API保存会议室信息
				// 模拟保存成功
				uni.showToast({
					title: this.isEdit ? '编辑成功' : '添加成功',
					icon: 'success'
				});
				
				// 返回上一页
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		}
	}
</script>

<style lang="scss">
.container {
	flex: 1;
	background-color: #f5f5f5;
	background-image: url('/static/images/bg.svg');
	background-size: cover;
	background-position: center;
}

.content {
	padding: 30rpx;
	flex: 1;
	padding-bottom: 150rpx; /* 为底部按钮留出空间 */
}

/* 编辑表单样式 */
.edit-form {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.form-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
	text-align: center;
}

.form-item {
	margin-bottom: 30rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 10rpx;
	display: block;
}

.form-input {
	height: 80rpx;
	border: 1px solid #ddd;
	border-radius: 8rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
	background-color: #f9f9f9;
}

/* 状态选择器样式 */
.status-selector {
	display: flex;
	flex-direction: row;
}

.status-option {
	flex: 1;
	height: 80rpx;
	border: 1px solid #ddd;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: #f9f9f9;
}

.status-option:first-child {
	border-top-left-radius: 8rpx;
	border-bottom-left-radius: 8rpx;
}

.status-option:last-child {
	border-top-right-radius: 8rpx;
	border-bottom-right-radius: 8rpx;
	border-left: none;
}

.status-option.active {
	background-color: #8B0000;
}

.status-option.active text {
	color: #ffffff;
}

.status-option text {
	font-size: 28rpx;
	color: #333;
}

/* 底部按钮样式 */
.footer {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: transparent;
	padding: 20rpx 30rpx;
	display: flex;
	justify-content: center;
}

.button-group {
	display: flex;
	flex-direction: row;
	width: 100%;
}

.cancel-button, .save-button {
	height: 90rpx;
	border-radius: 45rpx;
	justify-content: center;
	align-items: center;
	display: flex;
	flex: 1;
	margin: 0 15rpx;
}

.cancel-button {
	background-color: #f5f5f5;
	border: 1px solid #ddd;
}

.cancel-button text {
	color: #666;
	font-size: 32rpx;
}

.save-button {
	background-color: #8B0000; /* 深红色 */
	box-shadow: 0 4rpx 10rpx rgba(139, 0, 0, 0.3);
}

.save-button text {
	color: #ffffff;
	font-size: 32rpx;
	font-weight: bold;
}
</style>