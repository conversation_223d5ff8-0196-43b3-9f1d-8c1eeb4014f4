.container.data-v-80842834 {
  position: relative;
  min-height: 100vh;
  background-image: url('http://14.103.146.84:8890/i/2025/06/12/login-bg.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  overflow-x: hidden;
}
.user-info.data-v-80842834 {
  display: flex;
  align-items: center;
  padding: 150rpx 40rpx 60rpx 60rpx;
  position: relative;
}
.user-info .u-img.data-v-80842834 {
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  border: 2rpx solid #eb8585;
}
.user-info .u-text.data-v-80842834 {
  flex: 1;
  color: #eb8585;
  padding: 26rpx 30rpx;
}
.user-info .username.data-v-80842834 {
  font-weight: bolder;
  font-family: 黑体;
}
.user-info .small-text.data-v-80842834 {
  font-size: 24rpx;
  padding-top: 10rpx;
}
.user-info .u-icon-setting.data-v-80842834 {
  width: 30rpx;
  color: #FFFFFF;
}
.u-menu-list.data-v-80842834 {
  margin: 20rpx;
}
.u-menu-list .u-menu-item.data-v-80842834 {
  display: flex;
  padding: 30rpx 20rpx;
  border-bottom: 1px solid #f7f7f7;
  align-items: center;
}
.u-menu-list .u-menu-item .u-menu-icon.data-v-80842834 {
  padding-top: 8rpx;
  padding-right: 20rpx;
}
.u-menu-list .u-menu-item .u-menu-text.data-v-80842834 {
  flex: 1;
  color: #5e5e5e;
}
.u-menu-list .u-menu-item .u-menu-icon-rigth.data-v-80842834 {
  width: 30rpx;
}

