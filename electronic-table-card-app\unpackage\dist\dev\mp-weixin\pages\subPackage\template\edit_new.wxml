<view class="template-editor data-v-4aed2b70"><view class="preview-container data-v-4aed2b70"><canvas class="preview-canvas data-v-4aed2b70" style="{{'width:'+(canvasWidth+'px')+';'+('height:'+(canvasHeight+'px')+';')}}" canvas-id="templateCanvas" canvas-width="{{canvasWidth}}" canvas-height="{{canvasHeight}}" data-event-opts="{{[['touchstart',[['onCanvasTouchStart',['$event']]]],['touchmove',[['onCanvasTouchMove',['$event']]]],['touchend',[['onCanvasTouchEnd',['$event']]]]]}}" bindtouchstart="__e" bindtouchmove="__e" bindtouchend="__e"></canvas><block wx:if="{{selectedElement&&showElementButtons}}"><view class="element-buttons data-v-4aed2b70" style="{{'left:'+(elementButtonsPosition.x+'px')+';'+('top:'+(elementButtonsPosition.y+'px')+';')}}"><block wx:if="{{selectedElement.type==='text'}}"><view data-event-opts="{{[['tap',[['editSelectedElement',['$event']]]]]}}" class="element-btn edit-btn data-v-4aed2b70" bindtap="__e"><text class="btn-icon data-v-4aed2b70">✏️</text></view><view data-event-opts="{{[['tap',[['deleteSelectedElement',['$event']]]]]}}" class="element-btn delete-btn data-v-4aed2b70" bindtap="__e"><text class="btn-icon data-v-4aed2b70">🗑️</text></view></block><block wx:if="{{selectedElement.type==='image'}}"><view data-event-opts="{{[['tap',[['zoomInImage',['$event']]]]]}}" class="element-btn zoom-in-btn data-v-4aed2b70" catchtap="__e"><text class="btn-icon data-v-4aed2b70">+</text></view><view data-event-opts="{{[['tap',[['zoomOutImage',['$event']]]]]}}" class="element-btn zoom-out-btn data-v-4aed2b70" catchtap="__e"><text class="btn-icon data-v-4aed2b70">-</text></view><view data-event-opts="{{[['tap',[['rotateImage',['$event']]]]]}}" class="element-btn rotate-btn data-v-4aed2b70" catchtap="__e"><text class="btn-icon data-v-4aed2b70">🔄</text></view><view data-event-opts="{{[['tap',[['deleteSelectedElement',['$event']]]]]}}" class="element-btn delete-btn data-v-4aed2b70" catchtap="__e"><text class="btn-icon data-v-4aed2b70">🗑️</text></view></block></view></block></view><view class="template-settings data-v-4aed2b70"><view class="setting-row data-v-4aed2b70"><text class="setting-label data-v-4aed2b70">模板名称</text><input class="template-name-input data-v-4aed2b70" placeholder="新增模板" data-event-opts="{{[['input',[['__set_model',['','templateName','$event',[]]]]]]}}" value="{{templateName}}" bindinput="__e"/></view><view class="setting-row data-v-4aed2b70"><text class="setting-label data-v-4aed2b70">对象插入</text><view class="insert-options data-v-4aed2b70"><view data-event-opts="{{[['tap',[['setInsertType',['template-text']]]]]}}" class="{{['insert-btn','data-v-4aed2b70',(insertType==='template-text')?'active':'']}}" bindtap="__e">模板文字</view><view data-event-opts="{{[['tap',[['setInsertType',['fixed-text']]]]]}}" class="{{['insert-btn','data-v-4aed2b70',(insertType==='fixed-text')?'active':'']}}" bindtap="__e">固定文字</view><view data-event-opts="{{[['tap',[['setInsertType',['image']]]]]}}" class="{{['insert-btn','data-v-4aed2b70',(insertType==='image')?'active':'']}}" bindtap="__e">图片</view></view></view></view><view class="background-settings data-v-4aed2b70"><view class="setting-row data-v-4aed2b70"><text class="setting-label data-v-4aed2b70">桌牌类型</text><view class="background-options data-v-4aed2b70"><view data-event-opts="{{[['tap',[['setCardType',['three-color']]]]]}}" class="{{['bg-btn','data-v-4aed2b70',(cardType==='three-color')?'active':'']}}" bindtap="__e">三色桌牌</view><view data-event-opts="{{[['tap',[['setCardType',['six-color']]]]]}}" class="{{['bg-btn','data-v-4aed2b70',(cardType==='six-color')?'active':'']}}" bindtap="__e">六色桌牌</view></view></view></view><view class="background-settings data-v-4aed2b70"><view class="setting-row data-v-4aed2b70"><text class="setting-label data-v-4aed2b70">背景选择</text><view class="background-options data-v-4aed2b70"><view data-event-opts="{{[['tap',[['setBackgroundType',['select']]]]]}}" class="{{['bg-btn','data-v-4aed2b70',(backgroundType==='select')?'active':'']}}" bindtap="__e">选择背景</view><view data-event-opts="{{[['tap',[['setBackgroundType',['solid']]]]]}}" class="{{['bg-btn','data-v-4aed2b70',(backgroundType==='solid')?'active':'']}}" bindtap="__e">纯色背景</view><view data-event-opts="{{[['tap',[['setBackgroundType',['clear']]]]]}}" class="{{['bg-btn','data-v-4aed2b70',(backgroundType==='clear')?'active':'']}}" bindtap="__e">清除背景</view></view></view></view><view class="background-settings data-v-4aed2b70"><view class="setting-row data-v-4aed2b70"><text class="setting-label data-v-4aed2b70">批量操作</text><view class="background-options data-v-4aed2b70"><view data-event-opts="{{[['tap',[['centerAllElements',['$event']]]]]}}" class="bg-btn data-v-4aed2b70" bindtap="__e">垂直水平居中</view><view data-event-opts="{{[['tap',[['clearAllElements',['$event']]]]]}}" class="bg-btn danger-btn data-v-4aed2b70" bindtap="__e">清空画布</view></view></view></view><view class="save-container data-v-4aed2b70"><view data-event-opts="{{[['tap',[['openPreview',['$event']]]]]}}" class="preview-btn data-v-4aed2b70" bindtap="__e"><text class="btn-icon data-v-4aed2b70">👁</text><text class="btn-text data-v-4aed2b70">预览</text></view><view data-event-opts="{{[['tap',[['saveTemplate',['$event']]]]]}}" class="save-btn data-v-4aed2b70" bindtap="__e"><text class="btn-icon data-v-4aed2b70">💾</text><text class="btn-text data-v-4aed2b70">保存</text></view></view><u-popup vue-id="12b8c396-1" show="{{showTemplatePopup}}" mode="bottom" round="{{16}}" safeAreaInsetBottom="{{true}}" closeable="{{true}}" data-event-opts="{{[['^close',[['onTemplatePopupClose']]]]}}" bind:close="__e" class="data-v-4aed2b70" bind:__l="__l" vue-slots="{{['default']}}"><view class="template-popup-content data-v-4aed2b70"><view class="popup-title data-v-4aed2b70">添加模版文字</view><view class="template-field data-v-4aed2b70"><text class="field-label data-v-4aed2b70">姓名</text><input class="field-input data-v-4aed2b70" placeholder="请输入姓名" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['templateFields']]]]]}}" value="{{templateFields.name}}" bindinput="__e"/></view><view class="template-field data-v-4aed2b70"><text class="field-label data-v-4aed2b70">职位</text><input class="field-input data-v-4aed2b70" placeholder="请输入职位" data-event-opts="{{[['input',[['__set_model',['$0','position','$event',[]],['templateFields']]]]]}}" value="{{templateFields.position}}" bindinput="__e"/></view><view class="template-field data-v-4aed2b70"><text class="field-label data-v-4aed2b70">公司</text><input class="field-input data-v-4aed2b70" placeholder="请输入公司" data-event-opts="{{[['input',[['__set_model',['$0','company','$event',[]],['templateFields']]]]]}}" value="{{templateFields.company}}" bindinput="__e"/></view><view class="template-field data-v-4aed2b70"><text class="field-label data-v-4aed2b70">其他信息</text><input class="field-input data-v-4aed2b70" placeholder="请输入其他信息" data-event-opts="{{[['input',[['__set_model',['$0','other','$event',[]],['templateFields']]]]]}}" value="{{templateFields.other}}" bindinput="__e"/></view><view class="popup-btns data-v-4aed2b70"><view data-event-opts="{{[['tap',[['onTemplatePopupClose',['$event']]]]]}}" class="popup-btn cancel data-v-4aed2b70" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['confirmAddTemplateText',['$event']]]]]}}" class="popup-btn confirm data-v-4aed2b70" bindtap="__e">确认</view></view></view></u-popup><u-popup vue-id="12b8c396-2" show="{{showPreview}}" mode="center" round="{{16}}" safeAreaInsetBottom="{{true}}" closeable="{{true}}" data-event-opts="{{[['^close',[['closePreview']]]]}}" bind:close="__e" class="data-v-4aed2b70" bind:__l="__l" vue-slots="{{['default']}}"><view class="preview-popup-content data-v-4aed2b70"><view class="popup-title data-v-4aed2b70">模板预览</view><view class="preview-image-container data-v-4aed2b70"><block wx:if="{{previewImagePath}}"><image class="preview-image data-v-4aed2b70" src="{{previewImagePath}}" mode="aspectFit"></image></block><block wx:else><block wx:if="{{previewLoading}}"><view class="preview-loading data-v-4aed2b70"><text class="data-v-4aed2b70">生成预览中...</text></view></block><block wx:else><view class="preview-error data-v-4aed2b70"><text class="data-v-4aed2b70">预览生成失败</text></view></block></block></view><view class="popup-btns data-v-4aed2b70"><view data-event-opts="{{[['tap',[['closePreview',['$event']]]]]}}" class="popup-btn cancel data-v-4aed2b70" bindtap="__e">关闭</view><block wx:if="{{previewImagePath}}"><view data-event-opts="{{[['tap',[['savePreviewImage',['$event']]]]]}}" class="popup-btn confirm data-v-4aed2b70" bindtap="__e">保存图片</view></block></view></view></u-popup><u-popup vue-id="12b8c396-3" show="{{showPropertyPopup}}" mode="bottom" round="{{16}}" safeAreaInsetBottom="{{true}}" closeable="{{true}}" data-event-opts="{{[['^close',[['closePropertyPopup']]]]}}" bind:close="__e" class="data-v-4aed2b70" bind:__l="__l" vue-slots="{{['default']}}"><view class="property-popup-content data-v-4aed2b70"><view class="property-title data-v-4aed2b70">属性设置</view><scroll-view class="popup-scroll-content data-v-4aed2b70" scroll-y="true"><block wx:if="{{editingElement}}"><view class="edit-form-container data-v-4aed2b70"><view class="edit-form data-v-4aed2b70"><block wx:if="{{editingElement.type==='text'}}"><view class="property-row data-v-4aed2b70"><text class="property-label data-v-4aed2b70">文字内容</text><input class="text-input data-v-4aed2b70" placeholder="请输入文字内容" data-event-opts="{{[['input',[['__set_model',['$0','text','$event',[]],['editingElement']],['onTextChange',['$event']]]]]}}" value="{{editingElement.text}}" bindinput="__e"/></view></block><block wx:if="{{editingElement.type==='text'}}"><view class="property-row data-v-4aed2b70"><text class="property-label data-v-4aed2b70">文字字体</text><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="property-picker data-v-4aed2b70" bindtap="__e"><view class="picker-value data-v-4aed2b70">{{(editingElement.fontFamily||fontFamilies[0])+''}}<text class="picker-arrow data-v-4aed2b70">▼</text></view></view></view></block><u-picker vue-id="{{('12b8c396-4')+','+('12b8c396-3')}}" show="{{showFontPicker}}" columns="{{[fontFamilies]}}" defaultIndex="{{[$root.m0]}}" title="选择字体" data-event-opts="{{[['^confirm',[['onFontPickerConfirm']]],['^cancel',[['e1']]],['^close',[['e2']]]]}}" bind:confirm="__e" bind:cancel="__e" bind:close="__e" class="data-v-4aed2b70" bind:__l="__l"></u-picker><block wx:if="{{editingElement.type==='text'}}"><view class="property-row data-v-4aed2b70"><text class="property-label data-v-4aed2b70">文字颜色</text><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="property-picker data-v-4aed2b70" bindtap="__e"><view class="picker-value data-v-4aed2b70"><view class="color-preview-small data-v-4aed2b70" style="{{'background-color:'+(editingElement.color)+';'}}"></view><text class="color-name data-v-4aed2b70">{{$root.m1}}</text><text class="picker-arrow data-v-4aed2b70">▼</text></view></view></view></block><u-picker vue-id="{{('12b8c396-5')+','+('12b8c396-3')}}" show="{{showColorPicker}}" columns="{{[colorOptions]}}" defaultIndex="{{[$root.m2]}}" title="选择颜色" data-event-opts="{{[['^confirm',[['onColorPickerConfirm']]],['^cancel',[['e4']]],['^close',[['e5']]]]}}" bind:confirm="__e" bind:cancel="__e" bind:close="__e" class="data-v-4aed2b70" bind:__l="__l"></u-picker><block wx:if="{{editingElement.type==='text'}}"><view class="property-row data-v-4aed2b70"><text class="property-label data-v-4aed2b70">文字字形</text><view class="style-buttons data-v-4aed2b70"><view data-event-opts="{{[['tap',[['toggleFontWeight']]]]}}" class="{{['style-btn','data-v-4aed2b70',(editingElement.fontWeight==='bold')?'active':'']}}" bindtap="__e"><text class="style-text data-v-4aed2b70">B</text></view><view data-event-opts="{{[['tap',[['toggleFontStyle']]]]}}" class="{{['style-btn','data-v-4aed2b70',(editingElement.fontStyle==='italic')?'active':'']}}" bindtap="__e"><text class="style-text data-v-4aed2b70">/</text></view><view data-event-opts="{{[['tap',[['toggleTextDecoration']]]]}}" class="{{['style-btn','data-v-4aed2b70',(editingElement.textDecoration==='underline')?'active':'']}}" bindtap="__e"><text class="style-text data-v-4aed2b70">U</text></view><view data-event-opts="{{[['tap',[['toggleStrikethrough']]]]}}" class="{{['style-btn','data-v-4aed2b70',(editingElement.textDecoration==='line-through')?'active':'']}}" bindtap="__e"><text class="style-text data-v-4aed2b70">S</text></view></view></view></block><block wx:if="{{editingElement.type==='text'}}"><view class="property-row data-v-4aed2b70"><text class="property-label data-v-4aed2b70">文字字号</text><view class="slider-container data-v-4aed2b70"><u-slider vue-id="{{('12b8c396-6')+','+('12b8c396-3')}}" min="{{12}}" max="{{72}}" step="{{1}}" value="{{editingElement.fontSize}}" showValue="{{true}}" data-event-opts="{{[['^change',[['onFontSizeChange']]]]}}" bind:change="__e" class="data-v-4aed2b70" bind:__l="__l"></u-slider></view></view></block></view></view></block></scroll-view></view></u-popup></view>