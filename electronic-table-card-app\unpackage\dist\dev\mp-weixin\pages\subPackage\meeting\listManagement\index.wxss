@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  flex: 1;
  display: flex;
  flex-direction: column;
}
/* 会议室信息头部 */
.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}
.room-info {
  flex: 1;
  cursor: pointer;
  padding: 10rpx;
  border-radius: 8rpx;
  transition: background-color 0.3s;
}
.room-info:hover {
  background-color: #f5f5f5;
}
.room-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}
.switch-hint {
  font-size: 24rpx;
  color: #999999;
  margin-top: 5rpx;
}
.select-all {
  display: flex;
  align-items: center;
}
.select-all-text {
  font-size: 28rpx;
  color: #333333;
  margin-left: 10rpx;
}
/* 空状态样式 */
.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 200rpx);
  /* 确保有足够高度进行垂直居中 */
}
.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 40rpx;
  width: 100%;
  max-width: 500rpx;
  /* 限制最大宽度 */
  margin: 0 auto;
  /* 水平居中 */
}
.action-button {
  width: 500rpx;
  height: 100rpx;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  position: relative;
  background: transparent;
}
.action-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}
.button-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  border-radius: 50rpx;
}
.action-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  position: relative;
  z-index: 2;
}
/* 人员列表样式 */
.people-list {
  padding: 20rpx;
  flex: 1;
}
.person-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.person-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.desk-number {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
}
.person-details {
  padding: 30rpx;
}
.detail-row {
  display: flex;
  margin-bottom: 20rpx;
}
.detail-row:last-child {
  margin-bottom: 0;
}
.detail-item {
  flex: 1;
  display: flex;
  align-items: center;
}
.detail-label {
  font-size: 26rpx;
  color: #666666;
  width: 126rpx;
}
.detail-value {
  font-size: 26rpx;
  color: #333333;
  flex: 1;
}
.person-actions {
  display: flex;
  justify-content: flex-end;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  gap: 15rpx;
}
.action-btn {
  padding: 0 25rpx;
  height: 56rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}
.delete-btn {
  background-color: #ffffff;
  border: 1rpx solid #cccccc;
  color: #555555;
}
.edit-btn {
  background-color: #8B0000;
  color: #ffffff;
}
/* 浮动按钮样式 */
.floating-buttons {
  position: fixed;
  right: 30rpx;
  bottom: 200rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  z-index: 100;
}
.float-btn {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}
.add-btn {
  background-color: #8B0000;
}
.float-btn-text {
  font-size: 40rpx;
  font-weight: 300;
  color: #ffffff;
}
.excel-btn {
  background-color: #8B0000;
}
.excel-text {
  font-size: 20rpx;
  font-weight: 600;
  color: #ffffff;
}
/* 底部删除按钮 */
.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  z-index: 99;
}
.delete-selected-btn {
  width: 100%;
  height: 80rpx;
  background-color: #8B0000;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(139, 0, 0, 0.3);
}
/* 会议室选择弹窗样式 */
.room-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 1000;
  transition: all 0.3s ease;
}
.room-modal-content {
  width: 100%;
  max-height: 70vh;
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.2);
  -webkit-transform: translateY(0);
          transform: translateY(0);
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  -webkit-animation: slideUp 0.3s ease;
          animation: slideUp 0.3s ease;
}
@-webkit-keyframes slideUp {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes slideUp {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.close-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #666666;
  font-size: 36rpx;
  cursor: pointer;
}
.close-btn:hover {
  background-color: #e0e0e0;
}
.room-list {
  max-height: 600rpx;
  overflow-y: auto;
}
.room-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}
.room-option:last-child {
  border-bottom: none;
}
.room-option:hover {
  background-color: #f8f8f8;
}
.room-option-name {
  font-size: 30rpx;
  color: #333333;
}
.room-option-check {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  background-color: #8B0000;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}
/* 新增/编辑人员弹窗样式 */
.person-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  transition: all 0.3s ease;
}
.person-modal-content {
  width: 90%;
  max-width: 700rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
  -webkit-transform: scale(1);
          transform: scale(1);
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  -webkit-animation: modalShow 0.3s ease;
          animation: modalShow 0.3s ease;
}
@-webkit-keyframes modalShow {
from {
    -webkit-transform: scale(0.8);
            transform: scale(0.8);
    opacity: 0;
}
to {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
}
}
@keyframes modalShow {
from {
    -webkit-transform: scale(0.8);
            transform: scale(0.8);
    opacity: 0;
}
to {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
}
}
.person-form {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}
.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}
.form-item:last-child {
  margin-bottom: 0;
}
.form-label {
  font-size: 28rpx;
  color: #666666;
  width: 160rpx;
  flex-shrink: 0;
}
.form-input {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  background-color: transparent;
  border: none;
  outline: none;
}
.form-input::-webkit-input-placeholder {
  color: #999999;
}
.form-input::placeholder {
  color: #999999;
}
.modal-actions {
  display: flex;
  justify-content: space-between;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  gap: 30rpx;
}
.modal-actions .action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}
.cancel-btn {
  background-color: #f5f5f5;
  color: #666666;
  border: 1rpx solid #e0e0e0;
}
.cancel-btn:hover {
  background-color: #e8e8e8;
}
.confirm-btn {
  background-color: #8B0000;
  color: #ffffff;
  border: 1rpx solid #8B0000;
}
.confirm-btn:hover {
  background-color: #A52A2A;
}
/* 桌牌选择下拉框样式 */
.form-select {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  background-color: transparent;
  border: none;
  outline: none;
  cursor: pointer;
  transition: all 0.3s ease;
}
.form-select:hover {
  background-color: #f0f0f0;
}
.select-text {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}
.select-text.placeholder {
  color: #999999;
}
.select-arrow {
  font-size: 20rpx;
  color: #666666;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
/* 桌牌选择弹窗样式 */
.desk-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 1002;
  transition: all 0.3s ease;
}
.desk-modal-content {
  width: 100%;
  max-width: 100%;
  max-height: 70vh;
  background-color: #ffffff;
  border-radius: 16rpx 16rpx 0 0;
  overflow: hidden;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.2);
  -webkit-transform: translateY(0);
          transform: translateY(0);
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  -webkit-animation: slideUpDesk 0.3s ease;
          animation: slideUpDesk 0.3s ease;
}
@-webkit-keyframes slideUpDesk {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes slideUpDesk {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
.desk-list {
  padding: 20rpx;
  max-height: 50vh;
  overflow-y: auto;
}
.desk-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  margin-bottom: 16rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}
.desk-option:hover {
  background-color: #e8e8e8;
}
.desk-option.disabled {
  background-color: #f5f5f5;
  opacity: 0.6;
  cursor: not-allowed;
}
.desk-option.disabled:hover {
  background-color: #f5f5f5;
}
.desk-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.desk-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.desk-gateway {
  font-size: 24rpx;
  color: #666666;
}
.desk-status {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  align-self: flex-start;
}
.desk-status.available {
  background-color: #e8f5e8;
  color: #4caf50;
}
.desk-status.bound {
  background-color: #fff3e0;
  color: #ff9800;
}
.desk-status.offline {
  background-color: #ffebee;
  color: #f44336;
}
.desk-check {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #8B0000;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}
