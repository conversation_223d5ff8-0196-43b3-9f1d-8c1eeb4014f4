{"version": 3, "sources": ["webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/uni_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue?1ac9", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/uni_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue?821f", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/uni_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue?8b21", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/uni_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue?aa80", "uni-app:///uni_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/uni_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue?93e0", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/uni_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue?ffde"], "names": ["name", "mixins", "data", "array12", "length", "aniAngel", "webviewHide", "loading", "computed", "otherBorderColor", "watch", "show", "mounted", "methods", "init", "setTimeout", "addEventListenerToWebview", "currentWebview"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACsC;;;AAGnG;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvBA;AAAA;AAAA;AAAA;AAA6uB,CAAgB,8rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC4DjwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA,eAkBA;EACAA;EACAC;EACAC;IACA;MACA;MACA;MACAC;QACAC;MACA;MACA;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;MACA;IACA;EACA;;EACAC;IACAC;MACA;IAAA;EASA;EACAC;IACA;EACA;EACAC;IACAC;MACAC,wBAOA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;QACA;MACA;MACAA;QACA;MACA;IACA;EA8BA;AACA;AAAA,2B;;;;;;;;;;;;;ACzLA;AAAA;AAAA;AAAA;AAAw5C,CAAgB,6vCAAG,EAAC,C;;;;;;;;;;;ACA56C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-loading-icon/u-loading-icon.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-loading-icon.vue?vue&type=template&id=0fe228ae&scoped=true&\"\nvar renderjs\nimport script from \"./u-loading-icon.vue?vue&type=script&lang=js&\"\nexport * from \"./u-loading-icon.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-loading-icon.vue?vue&type=style&index=0&id=0fe228ae&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0fe228ae\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-loading-icon.vue?vue&type=template&id=0fe228ae&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.show ? _vm.__get_style([_vm.$u.addStyle(_vm.customStyle)]) : null\n  var g0 = _vm.show && !_vm.webviewHide ? _vm.$u.addUnit(_vm.size) : null\n  var g1 = _vm.show && !_vm.webviewHide ? _vm.$u.addUnit(_vm.size) : null\n  var g2 = _vm.show && _vm.text ? _vm.$u.addUnit(_vm.textSize) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-loading-icon.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-loading-icon.vue?vue&type=script&lang=js&\"", "<template>\n\t<view\n\t\tclass=\"u-loading-icon\"\n\t\t:style=\"[$u.addStyle(customStyle)]\"\n\t\t:class=\"[vertical && 'u-loading-icon--vertical']\"\n\t\tv-if=\"show\"\n\t>\n\t\t<view\n\t\t\tv-if=\"!webviewHide\"\n\t\t\tclass=\"u-loading-icon__spinner\"\n\t\t\t:class=\"[`u-loading-icon__spinner--${mode}`]\"\n\t\t\tref=\"ani\"\n\t\t\t:style=\"{\n\t\t\t\tcolor: color,\n\t\t\t\twidth: $u.addUnit(size),\n\t\t\t\theight: $u.addUnit(size),\n\t\t\t\tborderTopColor: color,\n\t\t\t\tborderBottomColor: otherBorderColor,\n\t\t\t\tborderLeftColor: otherBorderColor,\n\t\t\t\tborderRightColor: otherBorderColor,\n\t\t\t\t'animation-duration': `${duration}ms`,\n\t\t\t\t'animation-timing-function': mode === 'semicircle' || mode === 'circle' ? timingFunction : ''\n\t\t\t}\"\n\t\t>\n\t\t\t<block v-if=\"mode === 'spinner'\">\n\t\t\t\t<!-- #ifndef APP-NVUE -->\n\t\t\t\t<view\n\t\t\t\t\tv-for=\"(item, index) in array12\"\n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\tclass=\"u-loading-icon__dot\"\n\t\t\t\t>\n\t\t\t\t</view>\n\t\t\t\t<!-- #endif -->\n\t\t\t\t<!-- #ifdef APP-NVUE -->\n\t\t\t\t<!-- 此组件内部图标部分无法设置宽高，即使通过width和height配置了也无效 -->\n\t\t\t\t<loading-indicator\n\t\t\t\t\tv-if=\"!webviewHide\"\n\t\t\t\t\tclass=\"u-loading-indicator\"\n\t\t\t\t\t:animating=\"true\"\n\t\t\t\t\t:style=\"{\n\t\t\t\t\t\tcolor: color,\n\t\t\t\t\t\twidth: $u.addUnit(size),\n\t\t\t\t\t\theight: $u.addUnit(size)\n\t\t\t\t\t}\"\n\t\t\t\t/>\n\t\t\t\t<!-- #endif -->\n\t\t\t</block>\n\t\t</view>\n\t\t<text\n\t\t\tv-if=\"text\"\n\t\t\tclass=\"u-loading-icon__text\"\n\t\t\t:style=\"{\n\t\t\t\tfontSize: $u.addUnit(textSize),\n\t\t\t\tcolor: textColor,\n\t\t\t}\"\n\t\t>{{text}}</text>\n\t</view>\n</template>\n\n<script>\n\timport props from './props.js';\n\t// #ifdef APP-NVUE\n\tconst animation = weex.requireModule('animation');\n\t// #endif\n\t/**\n\t * loading 加载动画\n\t * @description 警此组件为一个小动画，目前用在uView的loadmore加载更多和switch开关等组件的正在加载状态场景。\n\t * @tutorial https://www.uviewui.com/components/loading.html\n\t * @property {Boolean}\t\t\tshow\t\t\t是否显示组件  (默认 true)\n\t * @property {String}\t\t\tcolor\t\t\t动画活动区域的颜色，只对 mode = flower 模式有效（默认color['u-tips-color']）\n\t * @property {String}\t\t\ttextColor\t\t提示文本的颜色（默认color['u-tips-color']）\n\t * @property {Boolean}\t\t\tvertical\t\t文字和图标是否垂直排列 (默认 false )\n\t * @property {String}\t\t\tmode\t\t\t模式选择，见官网说明（默认 'circle' ）\n\t * @property {String | Number}\tsize\t\t\t加载图标的大小，单位px （默认 24 ）\n\t * @property {String | Number}\ttextSize\t\t文字大小（默认 15 ）\n\t * @property {String | Number}\ttext\t\t\t文字内容 \n\t * @property {String}\t\t\ttimingFunction\t动画模式 （默认 'ease-in-out' ）\n\t * @property {String | Number}\tduration\t\t动画执行周期时间（默认 1200）\n\t * @property {String}\t\t\tinactiveColor\tmode=circle时的暗边颜色 \n\t * @property {Object}\t\t\tcustomStyle\t\t定义需要用到的外部样式\n\t * @example <u-loading mode=\"circle\"></u-loading>\n\t */\n\texport default {\n\t\tname: 'u-loading-icon',\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// Array.form可以通过一个伪数组对象创建指定长度的数组\n\t\t\t\t// https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Array/from\n\t\t\t\tarray12: Array.from({\n\t\t\t\t\tlength: 12\n\t\t\t\t}),\n\t\t\t\t// 这里需要设置默认值为360，否则在安卓nvue上，会延迟一个duration周期后才执行\n\t\t\t\t// 在iOS nvue上，则会一开始默认执行两个周期的动画\n\t\t\t\taniAngel: 360, // 动画旋转角度\n\t\t\t\twebviewHide: false, // 监听webview的状态，如果隐藏了页面，则停止动画，以免性能消耗\n\t\t\t\tloading: false, // 是否运行中，针对nvue使用\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 当为circle类型时，给其另外三边设置一个更轻一些的颜色\n\t\t\t// 之所以需要这么做的原因是，比如父组件传了color为红色，那么需要另外的三个边为浅红色\n\t\t\t// 而不能是固定的某一个其他颜色(因为这个固定的颜色可能浅蓝，导致效果没有那么细腻良好)\n\t\t\totherBorderColor() {\n\t\t\t\tconst lightColor = uni.$u.colorGradient(this.color, '#ffffff', 100)[80]\n\t\t\t\tif (this.mode === 'circle') {\n\t\t\t\t\treturn this.inactiveColor ? this.inactiveColor : lightColor\n\t\t\t\t} else {\n\t\t\t\t\treturn 'transparent'\n\t\t\t\t}\n\t\t\t\t// return this.mode === 'circle' ? this.inactiveColor ? this.inactiveColor : lightColor : 'transparent'\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tshow(n) {\n\t\t\t\t// nvue中，show为true，且为非loading状态，就重新执行动画模块\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tif (n && !this.loading) {\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.startAnimate()\n\t\t\t\t\t}, 30)\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t}\n\t\t},\n\t\tmounted() {\n\t\t\tthis.init()\n\t\t},\n\t\tmethods: {\n\t\t\tinit() {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t\tthis.show && this.nvueAnimate()\n\t\t\t\t\t// #endif\n\t\t\t\t\t// #ifdef APP-PLUS \n\t\t\t\t\tthis.show && this.addEventListenerToWebview()\n\t\t\t\t\t// #endif\n\t\t\t\t}, 20)\n\t\t\t},\n\t\t\t// 监听webview的显示与隐藏\n\t\t\taddEventListenerToWebview() {\n\t\t\t\t// webview的堆栈\n\t\t\t\tconst pages = getCurrentPages()\n\t\t\t\t// 当前页面\n\t\t\t\tconst page = pages[pages.length - 1]\n\t\t\t\t// 当前页面的webview实例\n\t\t\t\tconst currentWebview = page.$getAppWebview()\n\t\t\t\t// 监听webview的显示与隐藏，从而停止或者开始动画(为了性能)\n\t\t\t\tcurrentWebview.addEventListener('hide', () => {\n\t\t\t\t\tthis.webviewHide = true\n\t\t\t\t})\n\t\t\t\tcurrentWebview.addEventListener('show', () => {\n\t\t\t\t\tthis.webviewHide = false\n\t\t\t\t})\n\t\t\t},\n\t\t\t// #ifdef APP-NVUE\n\t\t\tnvueAnimate() {\n\t\t\t\t// nvue下，非spinner类型时才需要旋转，因为nvue的spinner类型，使用了weex的\n\t\t\t\t// loading-indicator组件，自带旋转功能\n\t\t\t\tthis.mode !== 'spinner' && this.startAnimate()\n\t\t\t},\n\t\t\t// 执行nvue的animate模块动画\n\t\t\tstartAnimate() {\n\t\t\t\tthis.loading = true\n\t\t\t\tconst ani = this.$refs.ani\n\t\t\t\tif (!ani) return\n\t\t\t\tanimation.transition(ani, {\n\t\t\t\t\t// 进行角度旋转\n\t\t\t\t\tstyles: {\n\t\t\t\t\t\ttransform: `rotate(${this.aniAngel}deg)`,\n\t\t\t\t\t\ttransformOrigin: 'center center'\n\t\t\t\t\t},\n\t\t\t\t\tduration: this.duration,\n\t\t\t\t\ttimingFunction: this.timingFunction,\n\t\t\t\t\t// delay: 10\n\t\t\t\t}, () => {\n\t\t\t\t\t// 每次增加360deg，为了让其重新旋转一周\n\t\t\t\t\tthis.aniAngel += 360\n\t\t\t\t\t// 动画结束后，继续循环执行动画，需要同时判断webviewHide变量\n\t\t\t\t\t// nvue安卓，页面隐藏后依然会继续执行startAnimate方法\n\t\t\t\t\tthis.show && !this.webviewHide ? this.startAnimate() : this.loading = false\n\t\t\t\t})\n\t\t\t}\n\t\t\t// #endif\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\t$u-loading-icon-color: #c8c9cc !default;\n\t$u-loading-icon-text-margin-left:4px !default;\n\t$u-loading-icon-text-color:$u-content-color !default;\n\t$u-loading-icon-text-font-size:14px !default;\n\t$u-loading-icon-text-line-height:20px !default;\n\t$u-loading-width:30px !default;\n\t$u-loading-height:30px !default;\n\t$u-loading-max-width:100% !default;\n\t$u-loading-max-height:100% !default;\n\t$u-loading-semicircle-border-width: 2px !default;\n\t$u-loading-semicircle-border-color:transparent !default;\n\t$u-loading-semicircle-border-top-right-radius: 100px !default;\n\t$u-loading-semicircle-border-top-left-radius: 100px !default;\n\t$u-loading-semicircle-border-bottom-left-radius: 100px !default;\n\t$u-loading-semicircle-border-bottom-right-radiu: 100px !default;\n\t$u-loading-semicircle-border-style: solid !default;\n\t$u-loading-circle-border-top-right-radius: 100px !default;\n\t$u-loading-circle-border-top-left-radius: 100px !default;\n\t$u-loading-circle-border-bottom-left-radius: 100px !default;\n\t$u-loading-circle-border-bottom-right-radiu: 100px !default;\n\t$u-loading-circle-border-width:2px !default;\n\t$u-loading-circle-border-top-color:#e5e5e5 !default;\n\t$u-loading-circle-border-right-color:$u-loading-circle-border-top-color !default;\n\t$u-loading-circle-border-bottom-color:$u-loading-circle-border-top-color !default;\n\t$u-loading-circle-border-left-color:$u-loading-circle-border-top-color !default;\n\t$u-loading-circle-border-style:solid !default;\n\t$u-loading-icon-host-font-size:0px !default;\n\t$u-loading-icon-host-line-height:1 !default;\n\t$u-loading-icon-vertical-margin:6px 0 0 !default;\n\t$u-loading-icon-dot-top:0 !default;\n\t$u-loading-icon-dot-left:0 !default;\n\t$u-loading-icon-dot-width:100% !default;\n\t$u-loading-icon-dot-height:100% !default;\n\t$u-loading-icon-dot-before-width:2px !default;\n\t$u-loading-icon-dot-before-height:25% !default;\n\t$u-loading-icon-dot-before-margin:0 auto !default;\n\t$u-loading-icon-dot-before-background-color:currentColor !default;\n\t$u-loading-icon-dot-before-border-radius:40% !default;\n\n\t.u-loading-icon {\n\t\t/* #ifndef APP-NVUE */\n\t\t// display: inline-flex;\n\t\t/* #endif */\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tcolor: $u-loading-icon-color;\n\n\t\t&__text {\n\t\t\tmargin-left: $u-loading-icon-text-margin-left;\n\t\t\tcolor: $u-loading-icon-text-color;\n\t\t\tfont-size: $u-loading-icon-text-font-size;\n\t\t\tline-height: $u-loading-icon-text-line-height;\n\t\t}\n\n\t\t&__spinner {\n\t\t\twidth: $u-loading-width;\n\t\t\theight: $u-loading-height;\n\t\t\tposition: relative;\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\tbox-sizing: border-box;\n\t\t\tmax-width: $u-loading-max-width;\n\t\t\tmax-height: $u-loading-max-height;\n\t\t\tanimation: u-rotate 1s linear infinite;\n\t\t\t/* #endif */\n\t\t}\n\n\t\t&__spinner--semicircle {\n\t\t\tborder-width: $u-loading-semicircle-border-width;\n\t\t\tborder-color: $u-loading-semicircle-border-color;\n\t\t\tborder-top-right-radius: $u-loading-semicircle-border-top-right-radius;\n\t\t\tborder-top-left-radius: $u-loading-semicircle-border-top-left-radius;\n\t\t\tborder-bottom-left-radius: $u-loading-semicircle-border-bottom-left-radius;\n\t\t\tborder-bottom-right-radius: $u-loading-semicircle-border-bottom-right-radiu;\n\t\t\tborder-style: $u-loading-semicircle-border-style;\n\t\t}\n\n\t\t&__spinner--circle {\n\t\t\tborder-top-right-radius: $u-loading-circle-border-top-right-radius;\n\t\t\tborder-top-left-radius: $u-loading-circle-border-top-left-radius;\n\t\t\tborder-bottom-left-radius: $u-loading-circle-border-bottom-left-radius;\n\t\t\tborder-bottom-right-radius: $u-loading-circle-border-bottom-right-radiu;\n\t\t\tborder-width: $u-loading-circle-border-width;\n\t\t\tborder-top-color: $u-loading-circle-border-top-color;\n\t\t\tborder-right-color: $u-loading-circle-border-right-color;\n\t\t\tborder-bottom-color: $u-loading-circle-border-bottom-color;\n\t\t\tborder-left-color: $u-loading-circle-border-left-color;\n\t\t\tborder-style: $u-loading-circle-border-style;\n\t\t}\n\n\t\t&--vertical {\n\t\t\tflex-direction: column\n\t\t}\n\t}\n\n\t/* #ifndef APP-NVUE */\n\t:host {\n\t\tfont-size: $u-loading-icon-host-font-size;\n\t\tline-height: $u-loading-icon-host-line-height;\n\t}\n\n\t.u-loading-icon {\n\t\t&__spinner--spinner {\n\t\t\tanimation-timing-function: steps(12)\n\t\t}\n\n\t\t&__text:empty {\n\t\t\tdisplay: none\n\t\t}\n\n\t\t&--vertical &__text {\n\t\t\tmargin: $u-loading-icon-vertical-margin;\n\t\t\tcolor: $u-content-color;\n\t\t}\n\n\t\t&__dot {\n\t\t\tposition: absolute;\n\t\t\ttop: $u-loading-icon-dot-top;\n\t\t\tleft: $u-loading-icon-dot-left;\n\t\t\twidth: $u-loading-icon-dot-width;\n\t\t\theight: $u-loading-icon-dot-height;\n\n\t\t\t&:before {\n\t\t\t\tdisplay: block;\n\t\t\t\twidth: $u-loading-icon-dot-before-width;\n\t\t\t\theight: $u-loading-icon-dot-before-height;\n\t\t\t\tmargin: $u-loading-icon-dot-before-margin;\n\t\t\t\tbackground-color: $u-loading-icon-dot-before-background-color;\n\t\t\t\tborder-radius: $u-loading-icon-dot-before-border-radius;\n\t\t\t\tcontent: \" \"\n\t\t\t}\n\t\t}\n\t}\n\n\t@for $i from 1 through 12 {\n\t\t.u-loading-icon__dot:nth-of-type(#{$i}) {\n\t\t\ttransform: rotate($i * 30deg);\n\t\t\topacity: 1 - 0.0625 * ($i - 1);\n\t\t}\n\t}\n\n\t@keyframes u-rotate {\n\t\t0% {\n\t\t\ttransform: rotate(0deg)\n\t\t}\n\n\t\tto {\n\t\t\ttransform: rotate(1turn)\n\t\t}\n\t}\n\n\t/* #endif */\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-loading-icon.vue?vue&type=style&index=0&id=0fe228ae&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-loading-icon.vue?vue&type=style&index=0&id=0fe228ae&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751873716896\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}