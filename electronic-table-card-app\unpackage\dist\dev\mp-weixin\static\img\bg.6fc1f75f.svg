<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="1000" height="1000" viewBox="0 0 1000 1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e0f7fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4fc3f7;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#grad1)"/>
  <path d="M0,200 Q500,150 1000,200 L1000,1000 L0,1000 Z" fill="#ffffff" fill-opacity="0.3"/>
  <path d="M0,400 Q500,350 1000,400 L1000,1000 L0,1000 Z" fill="#ffffff" fill-opacity="0.3"/>
  <path d="M0,600 Q500,550 1000,600 L1000,1000 L0,1000 Z" fill="#ffffff" fill-opacity="0.3"/>
  <path d="M0,800 Q500,750 1000,800 L1000,1000 L0,1000 Z" fill="#ffffff" fill-opacity="0.3"/>
</svg> 