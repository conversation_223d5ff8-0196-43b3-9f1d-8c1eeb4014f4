<view class="md-form data-v-13c1ba28"><vol-form vue-id="647488cc-1" form-options="{{formOptions}}" formFields="{{formFields}}" data-ref="form" data-event-opts="{{[['^updateFormFields',[['__set_sync',['$0','formFields','$event'],['']]]],['^updateFormFields',[['__set_sync',['$0','formFields','$event'],['']]]]]}}" bind:updateFormFields="__e" class="data-v-13c1ba28 vue-ref" bind:__l="__l"></vol-form><view style="margin:60rpx 30rpx;" class="data-v-13c1ba28"><u-button vue-id="647488cc-2" icon="lock-open" size="large" shape="circle" type="primary" text="修改密码" data-event-opts="{{[['^click',[['submit']]]]}}" bind:click="__e" class="data-v-13c1ba28" bind:__l="__l"></u-button></view></view>