
.template-editor.data-v-6c6302e2 {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f8f9fa;
}

/* 顶部工具栏 */
.toolbar.data-v-6c6302e2 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 60px;
	padding: 0 16px;
	background-color: #fff;
	border-bottom: 1px solid #e5e5e5;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.toolbar-left.data-v-6c6302e2 {
	display: flex;
	align-items: center;
	gap: 12px;
}
.back-btn.data-v-6c6302e2 {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 40px;
	height: 40px;
	border-radius: 8px;
	background-color: #f5f5f5;
	cursor: pointer;
	transition: background-color 0.2s;
}
.back-btn.data-v-6c6302e2:hover {
	background-color: #e9ecef;
}
.back-btn .icon.data-v-6c6302e2 {
	font-size: 18px;
	color: #333;
}
.template-name-input.data-v-6c6302e2 {
	padding: 8px 12px;
	border: 1px solid #ddd;
	border-radius: 6px;
	font-size: 14px;
	min-width: 150px;
	outline: none;
}
.template-name-input.data-v-6c6302e2:focus {
	border-color: #007AFF;
}
.toolbar-right.data-v-6c6302e2 {
	display: flex;
	gap: 8px;
}
.tool-btn.data-v-6c6302e2 {
	display: flex;
	align-items: center;
	gap: 4px;
	padding: 8px 12px;
	border-radius: 6px;
	background-color: #f5f5f5;
	cursor: pointer;
	transition: all 0.2s;
	font-size: 14px;
}
.tool-btn.data-v-6c6302e2:hover {
	background-color: #e9ecef;
}
.tool-btn.primary.data-v-6c6302e2 {
	background-color: #007AFF;
	color: white;
}
.tool-btn.primary.data-v-6c6302e2:hover {
	background-color: #0056b3;
}
.tool-btn.data-v-6c6302e2:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

/* Canvas画布区域 */
.canvas-container.data-v-6c6302e2 {
	position: relative;
	border-bottom: 1px solid #e5e5e5;
}
.canvas-wrapper.data-v-6c6302e2 {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
}
.skyline-canvas.data-v-6c6302e2 {
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.canvas-hint.data-v-6c6302e2 {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	        transform: translate(-50%, -50%);
	pointer-events: none;
}
.hint-text.data-v-6c6302e2 {
	color: #999;
	font-size: 16px;
	text-align: center;
}

/* 工具栏区域 */
.toolbar-container.data-v-6c6302e2 {
	padding: 16px;
	background-color: #fff;
}
.tool-section.data-v-6c6302e2 {
	margin-bottom: 20px;
}
.section-title.data-v-6c6302e2 {
	margin-bottom: 12px;
}
.section-title .title.data-v-6c6302e2 {
	font-size: 16px;
	font-weight: 600;
	color: #333;
}
.tool-buttons.data-v-6c6302e2 {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
}
.tool-button.data-v-6c6302e2 {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 4px;
	padding: 12px 8px;
	border: 1px solid #ddd;
	border-radius: 8px;
	background-color: #fff;
	cursor: pointer;
	transition: all 0.2s;
	min-width: 60px;
}
.tool-button.data-v-6c6302e2:hover {
	border-color: #007AFF;
	background-color: #f8f9ff;
}
.tool-button.active.data-v-6c6302e2 {
	border-color: #007AFF;
	background-color: #007AFF;
	color: white;
}
.tool-button.small.data-v-6c6302e2 {
	min-width: 50px;
	padding: 8px 6px;
}
.tool-icon.data-v-6c6302e2 {
	font-size: 20px;
}
.tool-label.data-v-6c6302e2 {
	font-size: 12px;
	text-align: center;
}

/* 属性控制 */
.property-controls.data-v-6c6302e2 {
	display: flex;
	flex-direction: column;
	gap: 16px;
}
.property-item.data-v-6c6302e2 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 12px;
}
.property-item .label.data-v-6c6302e2 {
	font-size: 14px;
	color: #333;
	min-width: 40px;
}
.number-control.data-v-6c6302e2 {
	display: flex;
	align-items: center;
	gap: 8px;
}
.control-btn.data-v-6c6302e2 {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 32px;
	height: 32px;
	border: 1px solid #ddd;
	border-radius: 4px;
	background-color: #fff;
	cursor: pointer;
	font-size: 16px;
	transition: all 0.2s;
}
.control-btn.data-v-6c6302e2:hover {
	border-color: #007AFF;
	background-color: #f8f9ff;
}
.number-input.data-v-6c6302e2 {
	width: 60px;
	padding: 6px 8px;
	border: 1px solid #ddd;
	border-radius: 4px;
	text-align: center;
	font-size: 14px;
	outline: none;
}
.number-input.data-v-6c6302e2:focus {
	border-color: #007AFF;
}
.color-options.data-v-6c6302e2 {
	display: flex;
	flex-wrap: wrap;
	gap: 6px;
}
.color-item.data-v-6c6302e2 {
	width: 24px;
	height: 24px;
	border: 2px solid #ddd;
	border-radius: 4px;
	cursor: pointer;
	transition: all 0.2s;
}
.color-item.data-v-6c6302e2:hover {
	-webkit-transform: scale(1.1);
	        transform: scale(1.1);
}
.color-item.active.data-v-6c6302e2 {
	border-color: #007AFF;
	border-width: 3px;
}

/* 元素操作按钮 */
.element-actions.data-v-6c6302e2 {
	display: flex;
	gap: 8px;
	margin-top: 12px;
}
.action-btn.data-v-6c6302e2 {
	display: flex;
	align-items: center;
	gap: 4px;
	padding: 8px 12px;
	border: 1px solid #ddd;
	border-radius: 6px;
	background-color: #fff;
	cursor: pointer;
	transition: all 0.2s;
	font-size: 12px;
}
.action-btn.data-v-6c6302e2:hover {
	border-color: #007AFF;
	background-color: #f8f9ff;
}
.action-btn.danger.data-v-6c6302e2 {
	border-color: #dc3545;
	color: #dc3545;
}
.action-btn.danger.data-v-6c6302e2:hover {
	background-color: #dc3545;
	color: white;
}
.action-btn.data-v-6c6302e2:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

/* 快捷操作 */
.quick-actions.data-v-6c6302e2 {
	display: flex;
	gap: 12px;
}

/* 弹窗样式 */
.popup-content.data-v-6c6302e2 {
	width: 320px;
	max-width: 90vw;
	background-color: #fff;
	border-radius: 12px;
	overflow: hidden;
}
.popup-header.data-v-6c6302e2 {
	padding: 16px;
	border-bottom: 1px solid #e5e5e5;
	text-align: center;
}
.popup-title.data-v-6c6302e2 {
	font-size: 16px;
	font-weight: 600;
	color: #333;
}
.popup-body.data-v-6c6302e2 {
	padding: 16px;
}
.text-input.data-v-6c6302e2 {
	width: 100%;
	min-height: 80px;
	padding: 12px;
	border: 1px solid #ddd;
	border-radius: 6px;
	font-size: 14px;
	resize: vertical;
	outline: none;
}
.text-input.data-v-6c6302e2:focus {
	border-color: #007AFF;
}
.popup-footer.data-v-6c6302e2 {
	display: flex;
	gap: 12px;
	padding: 16px;
	border-top: 1px solid #e5e5e5;
}
.popup-btn.data-v-6c6302e2 {
	flex: 1;
	padding: 12px;
	border: none;
	border-radius: 6px;
	font-size: 14px;
	cursor: pointer;
	transition: all 0.2s;
	text-align: center;
}
.popup-btn.secondary.data-v-6c6302e2 {
	background-color: #f5f5f5;
	color: #333;
}
.popup-btn.secondary.data-v-6c6302e2:hover {
	background-color: #e9ecef;
}
.popup-btn.primary.data-v-6c6302e2 {
	background-color: #007AFF;
	color: white;
}
.popup-btn.primary.data-v-6c6302e2:hover {
	background-color: #0056b3;
}
.popup-btn.data-v-6c6302e2:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
.toolbar.data-v-6c6302e2 {
		padding: 0 12px;
}
.template-name-input.data-v-6c6302e2 {
		min-width: 120px;
		font-size: 13px;
}
.tool-btn.data-v-6c6302e2 {
		padding: 6px 8px;
		font-size: 12px;
}
.toolbar-container.data-v-6c6302e2 {
		padding: 12px;
}
.tool-buttons.data-v-6c6302e2 {
		gap: 6px;
}
.tool-button.data-v-6c6302e2 {
		min-width: 50px;
		padding: 8px 6px;
}
.tool-icon.data-v-6c6302e2 {
		font-size: 16px;
}
.tool-label.data-v-6c6302e2 {
		font-size: 11px;
}
.popup-content.data-v-6c6302e2 {
		width: 280px;
}
}

