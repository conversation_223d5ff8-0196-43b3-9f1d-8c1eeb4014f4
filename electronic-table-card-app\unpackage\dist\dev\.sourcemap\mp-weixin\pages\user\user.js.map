{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/user/user.vue?628c", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/user/user.vue?e2df", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/user/user.vue?2b4a", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/user/user.vue?c0f7", "uni-app:///pages/user/user.vue", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/user/user.vue?a88d", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/user/user.vue?e7ce"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "height", "userInfo", "img", "userName", "menu", "name", "path", "icon", "color", "onLoad", "uni", "success", "_this", "onShow", "methods", "getUserInfo", "getTimeText", "text", "onHeaderError", "test", "console", "toUserInfo", "url", "itemClick"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AACuL;AACvL,gBAAgB,2LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAqsB,CAAgB,orBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC8BztB;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;QAGAH;QACAC;QACAC;QACAC;MAAA,WACA;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;QACAH;QACAC;QACAC;QACAC;MACA;IAEA;EACA;EACAC;IACA;IACA;IACAC;MACAC;QACA;QACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACAC;MACA;IACA;IACAC;MACAX;QACAY;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;QACA;QACAb;UACAY;QACA;QACA;MACA;MACAZ;QACAY;MACA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5IA;AAAA;AAAA;AAAA;AAAwzC,CAAgB,otCAAG,EAAC,C;;;;;;;;;;;ACA50C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/user.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/user.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./user.vue?vue&type=template&id=80842834&scoped=true&\"\nvar renderjs\nimport script from \"./user.vue?vue&type=script&lang=js&\"\nexport * from \"./user.vue?vue&type=script&lang=js&\"\nimport style0 from \"./user.vue?vue&type=style&index=0&id=80842834&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"80842834\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/user.vue\"\nexport default component.exports", "export * from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user.vue?vue&type=template&id=80842834&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getTimeText()\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<view class=\"user-info\">\n\t\t\t<image class=\"u-img\" @error=\"onHeaderError()\" :src=\"userInfo.img\"></image>\n\t\t\t<view class=\"u-text\">\n\t\t\t\t<view class=\"username\">{{userInfo.userName}}</view>\n\t\t\t\t<view class=\"small-text\">\n\t\t\t\t\t欢迎使用,{{getTimeText()}}\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"u-icon-setting\" @click=\"toUserInfo\">\n\t\t\t\t<u-icon name=\"arrow-right\" color=\"rgb(227 227 227)\" size=\"18\"></u-icon>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"u-menu-list\">\n\t\t\t<view class=\"u-menu-item\" @click=\"itemClick(item)\" v-for=\"(item,index) in menu\" :key=\"index\">\n\t\t\t\t<view class=\"u-menu-icon\">\n\t\t\t\t\t<image style=\"width:36rpx;height: 36rpx;\" :src=\"item.icon\"></image>\n\t\t\t\t\t<!-- \t<u-icon :name=\"item.icon\" color=\"#303133\" size=\"15\"></u-icon> -->\n\t\t\t\t</view>\n\t\t\t\t<view class=\"u-menu-text\">{{item.name}}</view>\n\t\t\t\t<view class=\"u-menu-icon-rigth\">\n\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"rgb(231 231 231)\" size=\"15\"></u-icon>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\theight: 50,\n\t\t\t\tuserInfo: {\n\t\t\t\t\timg: \"/static/imgs/head.png\",\n\t\t\t\t\tuserName: \"请登陆\"\n\t\t\t\t},\n\t\t\t\tmenu: [{\n\t\t\t\t\t\tname: \"关于vol\",\n\t\t\t\t\t\tpath: '/pages/user/about/about',\n\t\t\t\t\t\ticon: '/static/user.png',\n\t\t\t\t\t\tcolor: ''\n\t\t\t\t\t},\n\n\t\t\t\t\t{\n\t\t\t\t\t\tname: \"修改密码\",\n\t\t\t\t\t\tpath: '',\n\t\t\t\t\t\ticon: '/static/pwd.png',\n\t\t\t\t\t\tcolor: '',\n\t\t\t\t\t\tpath: \"/pages/user/modifyPwd/modifyPwd\"\n\t\t\t\t\t},\n\t\t\t\t\t// {\n\t\t\t\t\t// \tname: \"系统设置\",\n\t\t\t\t\t// \tpath: '',\n\t\t\t\t\t// \ticon: 'setting',\n\t\t\t\t\t// \tcolor: ''\n\t\t\t\t\t// },\n\t\t\t\t\t{\n\t\t\t\t\t\tname: \"退出登陆\",\n\t\t\t\t\t\tpath: '/pages/login/login',\n\t\t\t\t\t\ticon: '/static/logout.png',\n\t\t\t\t\t\tcolor: ''\n\t\t\t\t\t},\n\t\t\t\t]\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tvar _this = this;\n\t\t\t// 获取手机状态栏高度\n\t\t\tuni.getSystemInfo({\n\t\t\t\tsuccess: function(data) {\n\t\t\t\t\t// 将其赋值给this\n\t\t\t\t\t_this.height = data.statusBarHeight;\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\t\tonShow() {\n\t\t\tthis.getUserInfo();\n\t\t},\n\t\tmethods: {\n\t\t\tgetUserInfo() {\n\t\t\t\tthis.http.post(\"api/user/getCurrentUserInfo\", {}).then(x => {\n\t\t\t\t\t//x.data.gender = x.data.gender;\n\t\t\t\t\t//  this.$refs.form.reset(x.data);\n\t\t\t\t\tthis.userInfo.img = this.http.ipAddress + x.data.headImageUrl\n\t\t\t\t\t//this.userInfo.createDate = x.data.createDate;\n\t\t\t\t\tthis.userInfo.userName = x.data.userTrueName;\n\t\t\t\t\t//this.userInfo.phoneNo = x.data.phoneNo;\n\t\t\t\t\t//this.userInfo.email = x.data.email;\n\t\t\t\t});\n\t\t\t},\n\t\t\tgetTimeText() {\n\t\t\t\tlet timeNow = new Date();\n\t\t\t\t// 获取当前小时\n\t\t\t\tlet hours = timeNow.getHours();\n\t\t\t\t// 设置默认文字\n\t\t\t\tlet text = ``;\n\t\t\t\t// 判断当前时间段\n\t\t\t\tif (hours >= 0 && hours <= 10) {\n\t\t\t\t\ttext = `早上好`;\n\t\t\t\t} else if (hours > 10 && hours <= 14) {\n\t\t\t\t\ttext = `中午好`;\n\t\t\t\t} else if (hours > 14 && hours <= 18) {\n\t\t\t\t\ttext = `下午好`;\n\t\t\t\t} else if (hours > 18 && hours <= 24) {\n\t\t\t\t\ttext = `晚上好`;\n\t\t\t\t}\n\t\t\t\treturn text;\n\t\t\t},\n\t\t\tonHeaderError() {\n\t\t\t\tthis.userInfo.img = \"/static/imgs/head.png\";\n\t\t\t},\n\t\t\ttest() {\n\t\t\t\tthis.http.get(\"api/menu/getTreeMenu\", {}, false).then(result => {\n\t\t\t\t\tconsole.log(result)\n\t\t\t\t})\n\t\t\t},\n\t\t\ttoUserInfo() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/user/modifyPwd/modifyPwd'\n\t\t\t\t})\n\t\t\t},\n\t\t\titemClick(item) {\n\t\t\t\tif (!item.path) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (item.path == \"/pages/login/login\") {\n\t\t\t\t\tthis.$store.commit(\"CLEAR_USER_INFO\");\n\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\turl: item.path\n\t\t\t\t\t})\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: item.path\n\t\t\t\t})\n\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"less\" scoped>\n\t.container {\n\t\tposition: relative;\n\t\tmin-height: 100vh;\n\t\tbackground-image: url('http://*************:8890/i/2025/06/12/login-bg.png');\n\t\tbackground-size: cover;\n\t\tbackground-repeat: no-repeat;\n\t\tbackground-position: center;\n\t\toverflow-x: hidden;\n\t}\n\n\t.user-info {\n\t\t// height: 220rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 150rpx 40rpx 60rpx 60rpx;\n\t\t// background-image: linear-gradient(135deg, #26bcff 10%, #078ef9);\n\t\tposition: relative;\n\n\t\t.u-img {\n\t\t\twidth: 150rpx;\n\t\t\theight: 150rpx;\n\t\t\tborder-radius: 50%;\n\t\t\tborder: 2rpx solid #eb8585;\n\t\t}\n\n\t\t.u-text {\n\t\t\tflex: 1;\n\t\t\tcolor:#eb8585;\n\t\t\tpadding: 26rpx 30rpx;\n\t\t}\n\n\t\t.username {\n\t\t\tfont-weight: bolder;\n\t\t\tfont-family: 黑体;\n\t\t}\n\n\t\t.small-text {\n\t\t\tfont-size: 24rpx;\n\t\t\tpadding-top: 10rpx;\n\t\t}\n\n\t\t.u-icon-setting {\n\t\t\twidth: 30rpx;\n\t\t\tcolor: #FFFFFF;\n\t\t}\n\t}\n\n\t.u-menu-list {\n\t\t// background: #FFFFFF;\n\t\tmargin: 20rpx;\n\t\t// border-radius: 5rpx;\n\t\t// border: 1px solid #f7f7f7;\n\n\t\t.u-menu-item {\n\t\t\tdisplay: flex;\n\t\t\tpadding: 30rpx 20rpx;\n\t\t\tborder-bottom: 1px solid #f7f7f7;\n\t\t\talign-items: center;\n\n\t\t\t.u-menu-icon {\n\t\t\t\tpadding-top: 8rpx;\n\t\t\t\tpadding-right: 20rpx;\n\t\t\t}\n\n\t\t\t.u-menu-text {\n\t\t\t\tflex: 1;\n\t\t\t\tcolor: #5e5e5e;\n\t\t\t}\n\n\t\t\t.u-menu-icon-rigth {\n\t\t\t\twidth: 30rpx;\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user.vue?vue&type=style&index=0&id=80842834&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user.vue?vue&type=style&index=0&id=80842834&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751873713566\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}