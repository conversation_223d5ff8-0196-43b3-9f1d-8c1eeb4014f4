<view class="container"><view class="status-bar" style="{{'height:'+(statusBarHeight+'px')+';'}}"></view><view class="content"><view class="welcome-card"><view class="welcome-content"><view class="welcome-text"><text class="greeting">{{greeting}}</text><text class="welcome-desc">欢迎使用智能芯桌牌系统</text></view><view class="welcome-icon"><text class="icon-bluetooth">📶</text></view></view><view class="card-decoration"></view></view><view class="quick-actions"><text class="section-title">快速功能</text><view class="action-grid"><view data-event-opts="{{[['tap',[['goToSingleScreen',['$event']]]]]}}" class="action-item" bindtap="__e"><view class="action-icon single"><text class="icon">📱</text></view><text class="action-text">单一投屏</text></view><view data-event-opts="{{[['tap',[['goToBatchScreen',['$event']]]]]}}" class="action-item" bindtap="__e"><view class="action-icon batch"><text class="icon">📺</text></view><text class="action-text">批量投屏</text></view><view data-event-opts="{{[['tap',[['goToTemplate',['$event']]]]]}}" class="action-item" bindtap="__e"><view class="action-icon template"><text class="icon">🎨</text></view><text class="action-text">模板管理</text></view><view data-event-opts="{{[['tap',[['goToMeeting',['$event']]]]]}}" class="action-item" bindtap="__e"><view class="action-icon meeting"><text class="icon">📅</text></view><text class="action-text">会议管理</text></view></view></view><view class="stats-card"><text class="card-title">今日统计</text><view class="stats-content"><view class="stat-item"><text class="stat-number">{{todayScreens}}</text><text class="stat-label">投屏次数</text></view><view class="stat-divider"></view><view class="stat-item"><text class="stat-number">{{connectedDevices}}</text><text class="stat-label">连接设备</text></view><view class="stat-divider"></view><view class="stat-item"><text class="stat-number">{{activeMeetings}}</text><text class="stat-label">活跃会议</text></view></view></view><view class="recent-section"><view class="section-header"><text class="section-title">最近使用</text><text data-event-opts="{{[['tap',[['viewMore',['$event']]]]]}}" class="more-text" bindtap="__e">查看更多</text></view><view class="recent-list"><block wx:for="{{recentItems}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['openRecentItem',['$0'],[[['recentItems','',index]]]]]]]}}" class="recent-item" bindtap="__e"><view class="recent-icon"><text class="icon">{{item.icon}}</text></view><view class="recent-info"><text class="recent-title">{{item.title}}</text><text class="recent-time">{{item.time}}</text></view><text class="arrow">›</text></view></block></view></view></view><view class="background-decoration"><view class="bg-circle circle1"></view><view class="bg-circle circle2"></view><view class="bg-circle circle3"></view></view></view>