{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/login/account-login.vue?ba44", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/login/account-login.vue?6c2a", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/login/account-login.vue?103d", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/login/account-login.vue?2042", "uni-app:///pages/login/account-login.vue", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/login/account-login.vue?98c4", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/login/account-login.vue?bf55"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "formData", "phone", "password", "formOptions", "field", "title", "type", "placeholder", "methods", "login", "console", "forgotPassword", "uni", "url", "goRegister"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACqC;;;AAGjG;AACuL;AACvL,gBAAgB,2LAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA8sB,CAAgB,6rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC+BluB;EACAC;IACA;MACAC;QACAC;QACAC;MACA;MACAC,cACA;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA;IAEA;EACA;EACAC;IACAC;MACA;MACAC;IACA;IACAC;MACA;MACAC;QACAC;MACA;IACA;IACAC;MACA;MACAF;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxEA;AAAA;AAAA;AAAA;AAAiiC,CAAgB,s+BAAG,EAAC,C;;;;;;;;;;;ACArjC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/account-login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/account-login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./account-login.vue?vue&type=template&id=e2ef718c&scoped=true&\"\nvar renderjs\nimport script from \"./account-login.vue?vue&type=script&lang=js&\"\nexport * from \"./account-login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./account-login.vue?vue&type=style&index=0&id=e2ef718c&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"e2ef718c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/account-login.vue\"\nexport default component.exports", "export * from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./account-login.vue?vue&type=template&id=e2ef718c&scoped=true&\"", "var components\ntry {\n  components = {\n    volForm: function () {\n      return import(\n        /* webpackChunkName: \"components/vol-form/vol-form\" */ \"@/components/vol-form/vol-form.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./account-login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./account-login.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<view class=\"logo-container\">\n\t\t\t<image src=\"http://*************:8890/i/2025/06/12/logo.png\" class=\"logo\" mode=\"aspectFit\"></image>\n\t\t</view>\n\n\t\t<view class=\"form-container\">\n\t\t\t<view class=\"input-group\">\n\t\t\t\t<vol-form :formFields=\"formData\" :formOptions=\"formOptions\" :labelWidth=\"0\" padding=\"0\">\n\t\t\t\t</vol-form>\n\t\t\t</view>\n\n\t\t\t<view class=\"login-btn-container\">\n\t\t\t\t<view class=\"login-btn\" @click=\"login\">\n\t\t\t\t\t<text class=\"login-text\">登 录</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"forgot-password\" @click=\"forgotPassword\">\n\t\t\t\t<text class=\"forgot-text\">忘记密码?</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"register-container\">\n\t\t\t<text class=\"register-text\">还没有账号，现在去 </text>\n\t\t\t<text class=\"register-link\" @click=\"goRegister\">免费注册</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tformData: {\n\t\t\t\t\tphone: '',\n\t\t\t\t\tpassword: ''\n\t\t\t\t},\n\t\t\t\tformOptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tfield: 'phone',\n\t\t\t\t\t\ttitle: '手机号',\n\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\tplaceholder: '请输入手机号'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tfield: 'password',\n\t\t\t\t\t\ttitle: '密码',\n\t\t\t\t\t\ttype: 'password',\n\t\t\t\t\t\tplaceholder: '请输入密码'\n\t\t\t\t\t}\n\t\t\t\t]\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tlogin() {\n\t\t\t\t// 登录逻辑\n\t\t\t\tconsole.log('登录', this.formData.phone, this.formData.password);\n\t\t\t},\n\t\t\tforgotPassword() {\n\t\t\t\t// 跳转到忘记密码页面\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/login/forget-password'\n\t\t\t\t});\n\t\t\t},\n\t\t\tgoRegister() {\n\t\t\t\t// 跳转到注册页面\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/login/register'\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n.container {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: space-between;\n\theight: 100vh;\n\tbackground-image: url('http://*************:8890/i/2025/06/12/login-bg.png');\n\tbackground-size: cover;\n\tbackground-repeat: no-repeat;\n\tbackground-position: center;\n\tpadding: 0 20px;\n\tbox-sizing: border-box;\n\toverflow: hidden;\n}\n\n.logo-container {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tmargin-top: 60px;\n\tmargin-bottom: 50px;\n}\n\n.logo {\n\twidth: 120px;\n\theight: 120px;\n}\n\n.form-container {\n\tflex: 1;\n\twidth: 100%;\n\tmax-width: 350px;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n}\n\n.input-group {\n\twidth: 100%;\n\tmargin-bottom: 20px;\n}\n\n.login-btn-container {\n\twidth: 100%;\n\tmargin-top: 40px;\n\tmargin-bottom: 30px;\n}\n\n.login-btn {\n\twidth: 100%;\n\theight: 52px;\n\tbackground: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);\n\tborder-radius: 26px;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tposition: relative;\n\toverflow: hidden;\n\tbox-shadow: 0 4px 16px rgba(139, 69, 19, 0.3);\n\tborder: none;\n\ttransition: all 0.3s ease;\n}\n\n.login-btn:active {\n\ttransform: translateY(2px);\n\tbox-shadow: 0 2px 8px rgba(139, 69, 19, 0.4);\n}\n\n.login-text {\n\tcolor: #FFFFFF;\n\tfont-size: 18px;\n\tfont-weight: 600;\n\tletter-spacing: 2px;\n\tfont-family: 'STKaiti', 'KaiTi', '楷体', serif;\n\ttext-shadow: 1px 1px 2px rgba(0,0,0,0.3);\n\tposition: relative;\n\tz-index: 10;\n}\n\n.forgot-password {\n\talign-self: center;\n\tpadding: 10px;\n\tmargin-top: 10px;\n}\n\n.forgot-text {\n\tcolor: #8B4513;\n\tfont-size: 16px;\n\tfont-weight: 500;\n\ttext-decoration: underline;\n\ttext-decoration-color: rgba(139, 69, 19, 0.5);\n}\n\n.register-container {\n\tdisplay: flex;\n\tflex-direction: row;\n\talign-items: center;\n\tmargin-bottom: 14px;\n}\n\n.register-text {\n\tcolor: #333;\n\tfont-size:  48rpx;\n\tfont-weight: 500;\n}\n\n.register-link {\n\tcolor: #8B0000;\n\tfont-size: 48rpx;\n\tfont-weight: bold;\n\ttext-shadow: 1px 1px 2px rgba(0,0,0,0.3);\n}\n</style>\n", "import mod from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./account-login.vue?vue&type=style&index=0&id=e2ef718c&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./account-login.vue?vue&type=style&index=0&id=e2ef718c&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // *************\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}