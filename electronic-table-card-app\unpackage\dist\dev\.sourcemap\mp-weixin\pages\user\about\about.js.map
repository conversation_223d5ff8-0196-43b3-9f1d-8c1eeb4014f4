{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/user/about/about.vue?c8dc", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/user/about/about.vue?fbe3", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/user/about/about.vue?811f", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/user/about/about.vue?3c7a", "uni-app:///pages/user/about/about.vue", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/user/about/about.vue?01c2", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/user/about/about.vue?626e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "methods", "goToLogin", "uni", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAqtB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC+DzuB;EACAC;IACA,QAEA;EACA;EACAC;IACAC;MACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5EA;AAAA;AAAA;AAAA;AAA8iC,CAAgB,89BAAG,EAAC,C;;;;;;;;;;;ACAlkC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/about/about.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/about/about.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./about.vue?vue&type=template&id=56ce4180&scoped=true&\"\nvar renderjs\nimport script from \"./about.vue?vue&type=script&lang=js&\"\nexport * from \"./about.vue?vue&type=script&lang=js&\"\nimport style0 from \"./about.vue?vue&type=style&index=0&id=56ce4180&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"56ce4180\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/about/about.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./about.vue?vue&type=template&id=56ce4180&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./about.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./about.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<view class=\"header\">\n\t\t\t<image src=\"http://*************:8890/i/2025/06/12/visitor_title.png\" class=\"visitor-title\" mode=\"aspectFit\"></image>\n\t\t</view>\n\t\t\n\t\t<view class=\"content\">\n\t\t\t<view class=\"description\">\n\t\t\t\t<text class=\"desc-text\">智能电子桌牌是通过手机APP和电脑管理软件操作，</text>\n\t\t\t\t<text class=\"desc-text\">无线通讯技术传输，将需求显示的内容，</text>\n\t\t\t\t<text class=\"desc-text\">对于参会人员较多的场景，免去了</text>\n\t\t\t\t<text class=\"desc-text\">自动刷新显示的一款新型会议桌牌，</text>\n\t\t\t\t<text class=\"desc-text\">一键批量下发至终端。</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"description\">\n\t\t\t\t<text class=\"desc-text\">该产品摆弃了纸质书写会议内容的传统桌牌，</text>\n\t\t\t\t<text class=\"desc-text\">实现了一次摆放，重复使用，批量导入，快速修改</text>\n\t\t\t\t<text class=\"desc-text\">的愿景。</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"description\">\n\t\t\t\t<text class=\"desc-text\">人工书写，制作桌牌的方式，节省了大量的人力物</text>\n\t\t\t\t<text class=\"desc-text\">力，</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"description\">\n\t\t\t\t<text class=\"desc-text\">同时实现了真正的无纸化办公，</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"description\">\n\t\t\t\t<text class=\"desc-text\">达到了节能，环保，便捷的使用特点。</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"features\">\n\t\t\t\t<view class=\"feature-item\">\n\t\t\t\t\t<view class=\"feature-circle\">\n\t\t\t\t\t\t<text class=\"feature-text\">纸质</text>\n\t\t\t\t\t\t<text class=\"feature-text\">效果</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"feature-item\">\n\t\t\t\t\t<view class=\"feature-circle\">\n\t\t\t\t\t\t<text class=\"feature-text\">便携</text>\n\t\t\t\t\t\t<text class=\"feature-text\">高效</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"feature-item\">\n\t\t\t\t\t<view class=\"feature-circle\">\n\t\t\t\t\t\t<text class=\"feature-text\">提升</text>\n\t\t\t\t\t\t<text class=\"feature-text\">格调</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"footer\">\n\t\t\t<button class=\"login-button\" @click=\"goToLogin\">返回登录</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tgoToLogin() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/user/login'\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n.container {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tmin-height: 100vh;\n\tbackground-image: url('http://*************:8890/i/2025/06/12/login-bg.png');\n\tbackground-size: cover;\n\tbackground-repeat: no-repeat;\n\tbackground-position: center;\n\tpadding: 40px 20px;\n\tpadding-bottom: calc(20px + env(safe-area-inset-bottom));\n\tbox-sizing: border-box;\n}\n\n.header {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\twidth: 100%;\n\tmargin-bottom: 30px;\n}\n\n.visitor-title {\n\twidth: 300px;\n\theight: 80px;\n}\n\n.content {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\twidth: 100%;\n\tmax-width: 400px;\n\tpadding: 0 20px;\n\tmargin-bottom: 20px;\n}\n\n.description {\n\tmargin-bottom: 15px;\n\ttext-align: center;\n\tline-height: 1.6;\n}\n\n.desc-text {\n\tfont-size: 16px;\n\tcolor: #333;\n\tline-height: 1.8;\n}\n\n.features {\n\tdisplay: flex;\n\tjustify-content: space-around;\n\twidth: 100%;\n\tmargin-top: 40px;\n\tgap: 20px;\n}\n\n.feature-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n}\n\n.feature-circle {\n\twidth: 80px;\n\theight: 80px;\n\tborder: 2px solid #8B4513;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\tflex-direction: column;\n\tjustify-content: center;\n\talign-items: center;\n\tbackground-color: rgba(255, 255, 255, 0.8);\n}\n\n.feature-text {\n\tfont-size: 14px;\n\tcolor: #8B4513;\n\tfont-weight: bold;\n\tline-height: 1.2;\n}\n\n.footer {\n\twidth: 100%;\n\tmax-width: 300px;\n\tpadding: 20px 0;\n\tmargin-top: auto;\n}\n\n.login-button {\n\twidth: 100%;\n\theight: 48px;\n\tline-height: 48px;\n\tborder-radius: 8px;\n\tfont-size: 16px;\n\ttext-align: center;\n\tcolor: #fff;\n\tborder: none;\n\tbackground-color: #8B4513;\n}\n</style>\n", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./about.vue?vue&type=style&index=0&id=56ce4180&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./about.vue?vue&type=style&index=0&id=56ce4180&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751873713558\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}