<view class="data-v-6726b7c4"><u-toast vue-id="7db0c1c2-1" data-ref="uToast" class="data-v-6726b7c4 vue-ref" bind:__l="__l"></u-toast><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['f-form-item','data-v-6726b7c4',labelPosition=='left'?'left-form-item':'top-form-item',item.$orig.type=='group'?'f-form-group':'']}}" style="{{'padding:'+(padding+'rpx')+';'+('display:'+(item.$orig.hidden?'none':'')+';')}}"><block wx:if="{{item.$orig.type!='group'}}"><view class="f-form-label data-v-6726b7c4" style="{{'max-width:'+(labelWidth+'px')+';'+('min-width:'+('80px')+';')}}"><block wx:if="{{item.$orig.require||item.$orig.required}}"><text class="f-form-label-required data-v-6726b7c4">*</text></block><text class="data-v-6726b7c4">{{item.$orig.title}}</text></view></block><block wx:if="{{item.$orig.readonly||item.$orig.disabled}}"><view style="flex:1;font-size:15px;text-align:right;" class="data-v-6726b7c4"><block wx:if="{{item.$orig.type=='img'}}"><view class="readonly-imgs data-v-6726b7c4"><block wx:for="{{item.l0}}" wx:for-item="src" wx:for-index="imgIndex" wx:key="imgIndex"><image style="width:70px;height:70px;margin-left:20rpx;border-radius:10rpx;" src="{{src.url}}" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item:item.$orig,imgIndex})}}" bindtap="__e" class="data-v-6726b7c4"></image></block></view></block><block wx:else><text class="data-v-6726b7c4">{{''+item.m0}}</text></block></view></block><block wx:else><block wx:if="{{item.$orig.type=='editor'}}"><view class="data-v-6726b7c4"><u-parse vue-id="{{'7db0c1c2-2-'+index}}" content="{{inFormFields[item.$orig.field]}}" class="data-v-6726b7c4" bind:__l="__l"></u-parse></view></block><block wx:else><block wx:if="{{item.$orig.type=='city'}}"><view data-event-opts="{{[['tap',[['showCitySheet',['$0'],[[['formOptions','',index]]]]]]]}}" class="f-form-content f-form-content-select data-v-6726b7c4" bindtap="__e"><view style="flex:1;" class="data-v-6726b7c4"><view hidden="{{!(item.g0)}}" style="color:rgb(192 196 204);font-size:15px;padding-right:12rpx;" class="data-v-6726b7c4">{{''+('请选择'+item.$orig.title)+''}}</view><view hidden="{{!(!item.g1)}}" style="font-size:15px;" class="data-v-6726b7c4">{{''+item.g2+''}}</view></view><u-icon vue-id="{{'7db0c1c2-3-'+index}}" color="rgb(186 186 186)" size="15" name="arrow-right" class="data-v-6726b7c4" bind:__l="__l"></u-icon></view></block><block wx:else><block wx:if="{{item.$orig.type=='date'||item.$orig.type=='datetime'||item.$orig.type=='month'}}"><block wx:if="{{item.$orig.range}}"><view style="{{'flex:1;'+('max-width:'+(item.$orig.type=='date'?'120rpx':'30rpx')+';')}}" class="data-v-6726b7c4"></view><view data-event-opts="{{[['tap',[['showPicker',['$0',0],[[['formOptions','',index]]]]]]]}}" class="f-form-content f-form-content-select data-v-6726b7c4" style="text-align:left;" bindtap="__e"><view hidden="{{!(!inFormFields[item.$orig.field][0])}}" style="color:rgb(192 196 204);font-size:15px;" class="data-v-6726b7c4">开始时间</view><view style="flex:1;" class="data-v-6726b7c4"><view style="font-size:15px;" class="data-v-6726b7c4">{{''+(item.$orig.type=='date'?item.g3:inFormFields[item.$orig.field][0])+''}}</view></view></view><text style="margin:0 0rpx;" class="data-v-6726b7c4">-</text><view data-event-opts="{{[['tap',[['showPicker',['$0',1],[[['formOptions','',index]]]]]]]}}" class="f-form-content f-form-content-select data-v-6726b7c4" bindtap="__e"><view hidden="{{!(!inFormFields[item.$orig.field][1])}}" style="color:rgb(192 196 204);font-size:15px;text-align:right;width:100%;" class="data-v-6726b7c4">结束时间</view><view style="flex:1;" class="data-v-6726b7c4"><view style="font-size:15px;text-align:right;" class="data-v-6726b7c4">{{''+(item.$orig.type=='date'?item.g4:inFormFields[item.$orig.field][1])+''}}</view></view></view></block><block wx:else><view data-event-opts="{{[['tap',[['showPicker',['$0'],[[['formOptions','',index]]]]]]]}}" class="f-form-content f-form-content-select data-v-6726b7c4" bindtap="__e"><view hidden="{{!(!inFormFields[item.$orig.field])}}" style="color:rgb(192 196 204);font-size:15px;width:100%;padding-right:10rpx;" class="data-v-6726b7c4">{{''+('请选择'+item.$orig.title)+''}}</view><view style="flex:1;" class="data-v-6726b7c4"><view style="font-size:15px;padding-right:12rpx;" class="data-v-6726b7c4">{{''+(item.$orig.type=='date'?item.g5:item.$orig.type=='month'?item.g6:inFormFields[item.$orig.field])+''}}</view></view><u-icon vue-id="{{'7db0c1c2-4-'+index}}" color="rgb(186 186 186)" size="15" name="arrow-right" class="data-v-6726b7c4" bind:__l="__l"></u-icon></view></block></block><block wx:else><block wx:if="{{item.$orig.range}}"><view style="flex:1;max-width:120rpx;" class="data-v-6726b7c4"></view><view class="f-form-content f-form-content-select data-v-6726b7c4" style="text-align:left;"><view style="flex:1;" class="data-v-6726b7c4"><view style="font-size:15px;" class="data-v-6726b7c4"><input placeholder-style="color:rgb(192 196 204);font-size:15px;" type="number" border="none" placeholder="{{item.$orig.placeholder||'请输入'+item.$orig.title}}" data-ref="{{item.$orig.field}}" data-event-opts="{{[['input',[['__set_model',['$0',0,'$event',[]],['inFormFields.'+item.$orig.field+'']]]]]}}" value="{{inFormFields[item.$orig.field][0]}}" bindinput="__e" class="data-v-6726b7c4 vue-ref-in-for"/></view></view></view><text style="margin:0 0rpx;" class="data-v-6726b7c4">-</text><view class="f-form-content f-form-content-select data-v-6726b7c4"><input placeholder-style="color:rgb(192 196 204);font-size:15px;" type="number" border="none" placeholder="{{item.$orig.placeholder||'请输入'+item.$orig.title}}" data-ref="{{item.$orig.field}}" data-event-opts="{{[['input',[['__set_model',['$0',1,'$event',[]],['inFormFields.'+item.$orig.field+'']]]]]}}" value="{{inFormFields[item.$orig.field][1]}}" bindinput="__e" class="data-v-6726b7c4 vue-ref-in-for"/></view></block><block wx:else><block wx:if="{{item.g7!=-1}}"><view data-event-opts="{{[['tap',[['showActionSheet',['$0'],[[['formOptions','',index]]]]]]]}}" class="f-form-content f-form-content-select data-v-6726b7c4" bindtap="__e"><view style="flex:1;" class="data-v-6726b7c4"><view hidden="{{!(item.g8)}}" style="color:rgb(192 196 204);font-size:15px;padding-right:12rpx;" class="data-v-6726b7c4">{{''+('请选择'+item.$orig.title)+''}}</view><view hidden="{{!(!item.g9)}}" style="font-size:15px;" class="data-v-6726b7c4">{{''+item.m1+''}}</view></view><u-icon vue-id="{{'7db0c1c2-5-'+index}}" color="rgb(186 186 186)" size="15" name="arrow-right" class="data-v-6726b7c4" bind:__l="__l"></u-icon></view></block><block wx:else><block wx:if="{{item.$orig.type=='group'}}"><view class="f-form-group-content data-v-6726b7c4" style="{{(item.$orig.style)}}">{{''+(item.$orig.title||'')+''}}</view></block><block wx:else><block wx:if="{{item.$orig.type=='number'}}"><view class="f-form-content data-v-6726b7c4"><input focus="{{item.$orig.focus}}" placeholder-style="color:rgb(192 196 204);font-size:15px;" type="number" border="none" placeholder="{{item.$orig.placeholder||'请输入'+item.$orig.title}}" data-ref="{{item.$orig.field}}" data-event-opts="{{[['input',[['__set_model',['$0','$1','$event',[]],['formFields',[['formOptions','',index,'field']]]]]]]}}" value="{{formFields[item.$orig.field]}}" bindinput="__e" class="data-v-6726b7c4 vue-ref-in-for"/></view></block><block wx:else><block wx:if="{{item.$orig.type=='decimal'}}"><view class="f-form-content data-v-6726b7c4"><input focus="{{item.$orig.focus}}" placeholder-style="color:rgb(192 196 204);font-size:15px;" type="digit" border="none" placeholder="{{item.$orig.placeholder||'请输入'+item.$orig.title}}" data-ref="{{item.$orig.field}}" data-event-opts="{{[['input',[['__set_model',['$0','$1','$event',[]],['formFields',[['formOptions','',index,'field']]]]]]]}}" value="{{formFields[item.$orig.field]}}" bindinput="__e" class="data-v-6726b7c4 vue-ref-in-for"/></view></block><block wx:else><block wx:if="{{item.$orig.type=='radio'}}"><view class="f-form-content f-form-content-group data-v-6726b7c4"><u-radio-group vue-id="{{'7db0c1c2-6-'+index}}" placement="{{item.$orig.placement}}" value="{{formFields[item.$orig.field]}}" data-event-opts="{{[['^change',[['radioOnChange',['$event','$0'],[[['formOptions','',index]]]]]],['^input',[['__set_model',['$0','$1','$event',[]],['formFields',[['formOptions','',index,'field']]]]]]]}}" bind:change="__e" bind:input="__e" class="data-v-6726b7c4" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{item.l1}}" wx:for-item="option" wx:for-index="opIndex"><u-radio vue-id="{{('7db0c1c2-7-'+index+'-'+opIndex)+','+('7db0c1c2-6-'+index)}}" customStyle="{{option.a0}}" label="{{option.$orig.value}}" name="{{option.$orig.key}}" class="data-v-6726b7c4" bind:__l="__l"></u-radio></block></u-radio-group></view></block><block wx:else><block wx:if="{{item.$orig.type=='switch'}}"><view class="f-form-content f-form-content-group data-v-6726b7c4"><u-radio-group vue-id="{{'7db0c1c2-8-'+index}}" placement="row" value="{{formFields[item.$orig.field]}}" data-event-opts="{{[['^change',[['radioOnChange',['$event','$0'],[[['formOptions','',index]]]]]],['^input',[['__set_model',['$0','$1','$event',[]],['formFields',[['formOptions','',index,'field']]]]]]]}}" bind:change="__e" bind:input="__e" class="data-v-6726b7c4" bind:__l="__l" vue-slots="{{['default']}}"><u-radio vue-id="{{('7db0c1c2-9-'+index)+','+('7db0c1c2-8-'+index)}}" customStyle="{{item.a1}}" label="是" name="{{1}}" class="data-v-6726b7c4" bind:__l="__l"></u-radio><u-radio vue-id="{{('7db0c1c2-10-'+index)+','+('7db0c1c2-8-'+index)}}" label="否" name="{{0}}" class="data-v-6726b7c4" bind:__l="__l"></u-radio></u-radio-group></view></block><block wx:else><block wx:if="{{item.$orig.type=='textarea'}}"><view class="f-form-content data-v-6726b7c4"><textarea style="width:100%;padding-right:8rpx;" focus="{{item.$orig.focus}}" auto-height="{{true}}" maxlength="{{item.$orig.maxlength||100000}}" border="none" placeholder="{{item.$orig.placeholder||'请输入'+item.$orig.title}}" data-ref="{{item.$orig.field}}" data-event-opts="{{[['input',[['__set_model',['$0','$1','$event',[]],['inFormFields',[['formOptions','',index,'field']]]]]]]}}" value="{{inFormFields[item.$orig.field]}}" bindinput="__e" class="data-v-6726b7c4 vue-ref-in-for"></textarea></view></block><block wx:else><block wx:if="{{item.$orig.type=='img'}}"><u-upload vue-id="{{'7db0c1c2-11-'+index}}" sizeType="{{['compressed']}}" fileList="{{inFormFields[item.$orig.field]}}" name="3" multiple="{{item.$orig.multiple}}" maxCount="{{item.$orig.maxCount||item.$orig.maxFile||1}}" previewFullImage="{{true}}" data-ref="{{item.$orig.field}}" data-event-opts="{{[['^afterRead',[['afterRead',['$0','$event'],[[['formOptions','',index]]]]]],['^delete',[['deletePic',['$0','$event'],[[['formOptions','',index]]]]]]]}}" bind:afterRead="__e" bind:delete="__e" class="data-v-6726b7c4 vue-ref-in-for" bind:__l="__l"></u-upload></block><block wx:else><block wx:if="{{item.$orig.type=='password'}}"><view class="f-form-content data-v-6726b7c4"><input placeholder-style="color:rgb(192 196 204);font-size:15px;" type="password" border="none" placeholder="{{item.$orig.placeholder||'请输入'+item.$orig.title}}" data-ref="{{item.$orig.field}}" data-event-opts="{{[['input',[['__set_model',['$0','$1','$event',[]],['inFormFields',[['formOptions','',index,'field']]]]]]]}}" value="{{inFormFields[item.$orig.field]}}" bindinput="__e" class="data-v-6726b7c4 vue-ref-in-for"/></view></block><block wx:else><view class="f-form-content data-v-6726b7c4"><input focus="{{item.$orig.focus}}" placeholder-style="color:rgb(192 196 204);font-size:15px;" type="text" border="none" placeholder="{{item.$orig.placeholder||'请输入'+item.$orig.title}}" data-ref="{{item.$orig.field}}" data-event-opts="{{[['confirm',[['inputConfirm',['$0','$event'],[[['formOptions','',index,'field']]]]]],['input',[['__set_model',['$0','$1','$event',[]],['inFormFields',[['formOptions','',index,'field']]]]]]]}}" value="{{inFormFields[item.$orig.field]}}" bindconfirm="__e" bindinput="__e" class="data-v-6726b7c4 vue-ref-in-for"/></view></block></block></block></block></block></block></block></block></block></block></block></block></block></block><block wx:if="{{item.$orig.extra}}"><view data-event-opts="{{[['tap',[['extraClick',['$0','$1'],[[['formOptions','',index]],'inFormFields']]]]]}}" style="{{'display:flex;'+(item.$orig.extra.style)}}" bindtap="__e" class="data-v-6726b7c4"><block wx:if="{{item.$orig.extra.icon}}"><u-icon vue-id="{{'7db0c1c2-12-'+index}}" name="{{item.$orig.extra.icon}}" color="{{item.$orig.extra.color}}" size="{{item.$orig.extra.size}}" class="data-v-6726b7c4" bind:__l="__l"></u-icon></block><text class="data-v-6726b7c4">{{item.$orig.extra.text}}</text></view></block></view></block><slot></slot><u-datetime-picker class="form-popup data-v-6726b7c4" vue-id="7db0c1c2-13" minDate="{{pickerCurrentItem.min}}" maxDate="{{pickerCurrentItem.max}}" zIndex="{{9999999}}" closeOnClickOverlay="{{true}}" show="{{pickerModel}}" value="{{pickerValue}}" mode="{{pickerCurrentItem.type=='month'?'year-month':pickerCurrentItem.type}}" data-event-opts="{{[['^confirm',[['pickerConfirm']]],['^cancel',[['pickerClose']]],['^close',[['pickerClose']]]]}}" bind:confirm="__e" bind:cancel="__e" bind:close="__e" bind:__l="__l"></u-datetime-picker><u-popup class="form-popup data-v-6726b7c4" vue-id="7db0c1c2-14" zIndex="{{999999}}" show="{{actionSheetModel}}" data-event-opts="{{[['^touchmove',[['',['$event']]]],['^close',[['e1']]]]}}" bind:touchmove="__e" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="vol-action-sheet-select-container data-v-6726b7c4" style="{{'height:'+(popupHeight+'px')+';'}}"><view class="vol-action-sheet-select-title data-v-6726b7c4">{{"请选择"+actionSheetCurrentItem.title+''}}<text data-event-opts="{{[['tap',[['actionConfirmClick',['$event']]]]]}}" class="vol-action-sheet-select-confirm data-v-6726b7c4" bindtap="__e">确定</text></view><block wx:if="{{showFilter}}"><view class="vol-action-sheet-select-filter data-v-6726b7c4"><u-search vue-id="{{('7db0c1c2-15')+','+('7db0c1c2-14')}}" placeholder="请输入关键字搜索" showAction="{{true}}" actionText="清除" animation="{{false}}" value="{{searchText}}" data-event-opts="{{[['^custom',[['e2']]],['^input',[['__set_model',['','searchText','$event',[]]]]]]}}" bind:custom="__e" bind:input="__e" class="data-v-6726b7c4" bind:__l="__l"></u-search></view></block><view class="vol-action-sheet-select-content data-v-6726b7c4"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['actionClick',['$0'],[[['actionSheetCurrentItem.data','',index]]]]]]]}}" hidden="{{!(item.g10)}}" class="{{['vol-action-sheet-select-item','data-v-6726b7c4',(item.m2)?'vol-action-sheet-select-actived':'']}}" bindtap="__e">{{''+item.$orig.value+''}}</view></block></view></view></u-popup><vol-tree vue-id="7db0c1c2-16" data="{{actionSascaderCurrentItem.data}}" title="{{'请选择'+actionSascaderCurrentItem.title}}" checkStrictly="{{actionSascaderCurrentItem.checkStrictly}}" data-ref="cascader" data-event-opts="{{[['^cancel',[['e3']]],['^confirm',[['cascaderConfirm']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-6726b7c4 vue-ref" bind:__l="__l"></vol-tree><lotus-address vue-id="7db0c1c2-17" lotusAddressData="{{lotusAddressData}}" data-event-opts="{{[['^choseVal',[['onCitySelect']]]]}}" bind:choseVal="__e" class="data-v-6726b7c4" bind:__l="__l"></lotus-address></view>