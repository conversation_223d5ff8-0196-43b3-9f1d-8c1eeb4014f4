<template>
	<view class="template-editor">
		<!-- 模板预览区域 -->
		<view class="preview-container">
			<canvas class="preview-canvas" canvas-id="templateCanvas" :canvas-width="canvasWidth"
				:canvas-height="canvasHeight" :style="{width: canvasWidth + 'px', height: canvasHeight + 'px'}"
				@touchstart="onCanvasTouchStart" @touchmove="onCanvasTouchMove" @touchend="onCanvasTouchEnd"></canvas>

			<!-- 元素操作按钮 -->
			<view v-if="selectedElement && showElementButtons" class="element-buttons"
				:style="{left: elementButtonsPosition.x + 'px', top: elementButtonsPosition.y + 'px'}">
				<!-- 文字元素按钮 -->
				<template v-if="selectedElement.type === 'text'">
					<view class="element-btn edit-btn" @click="editSelectedElement">
						<text class="btn-icon">✏️</text>
					</view>
					<view class="element-btn delete-btn" @click="deleteSelectedElement">
						<text class="btn-icon">🗑️</text>
					</view>
				</template>
				
				<!-- 图片元素按钮 -->
				<template v-if="selectedElement.type === 'image'">
					<view class="element-btn zoom-in-btn" 
						@click.stop="zoomInImage">
						<text class="btn-icon">+</text>
					</view>
					<view class="element-btn zoom-out-btn" 
						@click.stop="zoomOutImage">
						<text class="btn-icon">-</text>
					</view>
					<view class="element-btn rotate-btn" 
						@click.stop="rotateImage">
						<text class="btn-icon">🔄</text>
					</view>
					<view class="element-btn delete-btn" @click.stop="deleteSelectedElement">
						<text class="btn-icon">🗑️</text>
					</view>
				</template>
			</view>
		</view>

		<!-- 模板基础设置 -->
		<view class="template-settings">
			<view class="setting-row">
				<text class="setting-label">模板名称</text>
				<input class="template-name-input" v-model="templateName" placeholder="新增模板" />
			</view>

			<view class="setting-row">
				<text class="setting-label">对象插入</text>
				<view class="insert-options">
					<view class="insert-btn" :class="{active: insertType === 'template-text'}"
						@click="setInsertType('template-text')">
						模板文字
					</view>
					<view class="insert-btn" :class="{active: insertType === 'fixed-text'}"
						@click="setInsertType('fixed-text')">
						固定文字
					</view>
					<view class="insert-btn" :class="{active: insertType === 'image'}" @click="setInsertType('image')">
						图片
					</view>
				</view>
			</view>
		</view>

		<!-- 桌牌类型选择 -->
		<view class="background-settings">
			<view class="setting-row">
				<text class="setting-label">桌牌类型</text>
				<view class="background-options">
					<view class="bg-btn" :class="{active: cardType === 'three-color'}"
						@click="setCardType('three-color')">
						三色桌牌
					</view>
					<view class="bg-btn" :class="{active: cardType === 'six-color'}"
						@click="setCardType('six-color')">
						六色桌牌
					</view>
				</view>
			</view>
		</view>

		<!-- 背景选择功能 -->
		<view class="background-settings">
			<view class="setting-row">
				<text class="setting-label">背景选择</text>
				<view class="background-options">
					<view class="bg-btn" :class="{active: backgroundType === 'select'}"
						@click="setBackgroundType('select')">
						选择背景
					</view>
					<view class="bg-btn" :class="{active: backgroundType === 'solid'}"
						@click="setBackgroundType('solid')">
						纯色背景
					</view>
					<view class="bg-btn" :class="{active: backgroundType === 'clear'}"
						@click="setBackgroundType('clear')">
						清除背景
					</view>
				</view>
			</view>
		</view>

		<!-- 批量操作 -->
		<view class="background-settings">
			<view class="setting-row">
				<text class="setting-label">批量操作</text>
				<view class="background-options">
					<view class="bg-btn" @click="centerAllElements">
						垂直水平居中
					</view>
					<view class="bg-btn danger-btn" @click="clearAllElements">
						清空画布
					</view>
				</view>
			</view>
		</view>

		<!-- 保存按钮 -->
		<view class="save-container">
			<view class="preview-btn" @click="openPreview">
				<text class="btn-icon">👁</text>
				<text class="btn-text">预览</text>
			</view>
			<view class="save-btn" @click="saveTemplate">
				<text class="btn-icon">💾</text>
				<text class="btn-text">保存</text>
			</view>
		</view>

		<!-- 模板字段选择弹窗 -->
		<!-- 模板文字弹窗 -->
		<u-popup :show="showTemplatePopup" mode="bottom" @close="onTemplatePopupClose" :round="16"
			:safeAreaInsetBottom="true" closeable>
			<view class="template-popup-content">
				<view class="popup-title">添加模版文字</view>

				<view class="template-field">
					<text class="field-label">姓名</text>
					<input v-model="templateFields.name" class="field-input" placeholder="请输入姓名" />
				</view>

				<view class="template-field">
					<text class="field-label">职位</text>
					<input v-model="templateFields.position" class="field-input" placeholder="请输入职位" />
				</view>

				<view class="template-field">
					<text class="field-label">公司</text>
					<input v-model="templateFields.company" class="field-input" placeholder="请输入公司" />
				</view>

				<view class="template-field">
					<text class="field-label">其他信息</text>
					<input v-model="templateFields.other" class="field-input" placeholder="请输入其他信息" />
				</view>

				<view class="popup-btns">
					<view class="popup-btn cancel" @click="onTemplatePopupClose">取消</view>
					<view class="popup-btn confirm" @click="confirmAddTemplateText">确认</view>
				</view>
			</view>
		</u-popup>

		<!-- 预览弹窗 -->
		<u-popup :show="showPreview" mode="center" @close="closePreview" :round="16" :safeAreaInsetBottom="true"
			closeable>
			<view class="preview-popup-content">
				<view class="popup-title">模板预览</view>

				<view class="preview-image-container">
					<image v-if="previewImagePath" :src="previewImagePath" mode="aspectFit" class="preview-image">
					</image>
					<view v-else-if="previewLoading" class="preview-loading">
						<text>生成预览中...</text>
					</view>
					<view v-else class="preview-error">
						<text>预览生成失败</text>
					</view>
				</view>

				<view class="popup-btns">
					<view class="popup-btn cancel" @click="closePreview">关闭</view>
					<view class="popup-btn confirm" @click="savePreviewImage" v-if="previewImagePath">保存图片</view>
				</view>
			</view>
		</u-popup>

		<!-- 元素属性编辑弹窗 -->
		<u-popup :show="showPropertyPopup" mode="bottom" @close="closePropertyPopup" :round="16"
			:safeAreaInsetBottom="true" closeable>
			<view class="property-popup-content">
				<!-- 固定标题 -->
				<view class="property-title">属性设置</view>
				<!-- 可滚动的内容区域 -->
				<scroll-view class="popup-scroll-content" scroll-y="true">
					<view class="edit-form-container" v-if="editingElement">
						<view class="edit-form">
							<!-- 文字内容 -->
							<view class="property-row" v-if="editingElement.type === 'text'">
								<text class="property-label">文字内容</text>
								<input class="text-input" v-model="editingElement.text" @input="onTextChange"
									placeholder="请输入文字内容" />
							</view>

							<!-- 文字字体 -->
							<view class="property-row" v-if="editingElement.type === 'text'">
								<text class="property-label">文字字体</text>
								<view class="property-picker" @click="showFontPicker = true">
									<view class="picker-value">{{editingElement.fontFamily || fontFamilies[0]}}
										<text class="picker-arrow">▼</text>
									</view>
								</view>
							</view>
							
							<!-- 字体选择器 -->
							<u-picker 
								:show="showFontPicker" 
								:columns="[fontFamilies]" 
								:defaultIndex="[getFontFamilyIndex()]"
								title="选择字体"
								@confirm="onFontPickerConfirm"
								@cancel="showFontPicker = false"
								@close="showFontPicker = false"
							></u-picker>
							
							<!-- 文字颜色 -->
							<view class="property-row" v-if="editingElement.type === 'text'">
								<text class="property-label">文字颜色</text>
								<view class="property-picker" @click="showColorPicker = true">
									<view class="picker-value">
										<view class="color-preview-small" :style="{backgroundColor: editingElement.color}"></view>
										<text class="color-name">{{getColorName()}}</text>
										<text class="picker-arrow">▼</text>
									</view>
								</view>
							</view>
							
							<!-- 颜色选择器 -->
							<u-picker 
								:show="showColorPicker" 
								:columns="[colorOptions]" 
								:defaultIndex="[getColorIndex()]"
								title="选择颜色"
								@confirm="onColorPickerConfirm"
								@cancel="showColorPicker = false"
								@close="showColorPicker = false"
							></u-picker>
							
							<!-- 文字字形 -->
							<view class="property-row" v-if="editingElement.type === 'text'">
								<text class="property-label">文字字形</text>
								<view class="style-buttons">
									<view class="style-btn" :class="{active: editingElement.fontWeight === 'bold'}"
										@click="toggleFontWeight()">
										<text class="style-text">B</text>
									</view>
									<view class="style-btn" :class="{active: editingElement.fontStyle === 'italic'}"
										@click="toggleFontStyle()">
										<text class="style-text">/</text>
									</view>
									<view class="style-btn"
										:class="{active: editingElement.textDecoration === 'underline'}"
										@click="toggleTextDecoration()">
										<text class="style-text">U</text>
									</view>
									<view class="style-btn"
										:class="{active: editingElement.textDecoration === 'line-through'}"
										@click="toggleStrikethrough()">
										<text class="style-text">S</text>
									</view>
								</view>
							</view>
							
							<!-- 文字字号 -->
							<view class="property-row" v-if="editingElement.type === 'text'">
								<text class="property-label">文字字号</text>
								<view class="slider-container">
									<u-slider 
										:min="12" 
										:max="72" 
										:step="1" 
										:value="editingElement.fontSize" 
										:showValue="true"
										@change="onFontSizeChange"
									></u-slider>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</u-popup> 
	</view> 
</template>

<script>
	export default {
		data() {
			return {
				// 模板基础信息
				templateName: '新增模板',
				insertType: 'template-text',
				backgroundType: 'select',
				// 背景相关
				backgroundImageUrl: '',
				backgroundColor: '#FFFFFF', 
				// 画布相关
				canvasWidth: 0,
				canvasHeight: 0,
				previewContainerStyle:0,
				canvasContext: null, 
				// 文字属性
				fontFamilies: ['微软雅黑', '宋体', '黑体', '楷体', 'Arial', 'Times New Roman'],
				textColor: '#000000',
				fontWeight: 'normal',
				fontStyle: 'normal',
				textDecoration: 'none',
				fontSize: 30,
				// 电子桌牌类型
				cardType: 'six-color', // 'three-color' | 'six-color'
				
				//六色电子桌牌
				//黑、白、红、黄、蓝、绿
				//三色电子桌牌
				//黑白红 
				// 颜色选择器
				colorOptions: [
					'黑色', '白色', '红色', '黄色', '蓝色', '绿色' 
				],
				colorValues: [
					'#000000', '#FFFFFF', '#FF0000', '#FFFF00', '#0000FF', '#00FF00' 
				],
				
				// 三色桌牌颜色选项
				threeColorOptions: [
					'黑色', '白色', '红色'
				],
				threeColorValues: [
					'#000000', '#FFFFFF', '#FF0000'
				],
				
				// 六色桌牌颜色选项
				sixColorOptions: [
					'黑色', '白色', '红色', '黄色', '蓝色', '绿色'
				],
				sixColorValues: [
					'#000000', '#FFFFFF', '#FF0000', '#FFFF00', '#0000FF', '#00FF00'
				], 
				// 预览相关
				previewImagePath: '',
				previewLoading: false,
				showTemplatePopup: false, 
				// 模板字段输入数据
				templateFields: {
					name: '',
					position: '',
					company: '',
					other: ''
			},
				// 画布元素
				canvasElements: [],
				selectedElement: null,
				dragging: false,
				lastTouchX: 0,
				lastTouchY: 0,
				touchStartTime: 0, 
				// 属性编辑弹窗
				showPropertyPopup: false,
				editingElement: null,
				// picker控制
				showFontPicker: false,
				showColorPicker: false,
				// 元素操作按钮
				showElementButtons: false,
				elementButtonsPosition: { x: 0, y: 0 },
				buttonHideTimer: null,
				
				// 缩放和旋转控制（保留基础变量用于其他功能）
				scaleStartDistance: 0,
				rotateStartAngle: 0
			}
		},
		//
		computed: {
			 
		},
		mounted() {
			this.initCanvas()
			// 初始化防抖函数
			this.debouncedDrawCanvas = this.debounce(this.drawCanvas, 16) // 约60fps
		},
		
		onShow() {
			// 检查是否有选择的背景图片
			this.checkSelectedBackground()
		},
		
		beforeDestroy() {
			// 清理资源
			if (this.canvasContext) {
				this.canvasContext = null
			}
			this.canvasElements = []
			
			// 清理定时器
			if (this.buttonHideTimer) {
				clearTimeout(this.buttonHideTimer)
				this.buttonHideTimer = null
			}
			

		},
		methods: {
			// 初始化画布
			initCanvas() {
				const systemInfo = uni.getSystemInfoSync();
				
				// 获取状态栏高度和安全区域信息
				const statusBarHeight = systemInfo.statusBarHeight || 0;
				const safeAreaTop = systemInfo.safeArea ? systemInfo.safeArea.top : statusBarHeight;
				
				// 计算可用屏幕高度（减去状态栏、导航栏等系统UI占用的空间）
				const availableHeight = systemInfo.windowHeight - safeAreaTop;
				
				// 考虑页面其他元素的空间占用
				// 预估其他UI元素总高度：模板设置(80px) + 背景设置(80px) + 批量操作(60px) + 属性面板(200px) + 保存按钮(75px) + 各种margin/padding(80px)
				const otherElementsHeight = 80 + 80 + 60 + 200 + 75 + 80;
				
				// 计算画布容器可用高度
				const containerAvailableHeight = Math.max(300, availableHeight - otherElementsHeight);
				
				// 考虑preview-container的margin(20px)、padding(16px)和border(4px)
				const containerOverhead = 20 + 16 + 4; // margin: 10px*2 + padding: 8px*2 + border: 2px*2
				const maxCanvasHeight = containerAvailableHeight - containerOverhead;
				console.log("maxCanvasHeight",maxCanvasHeight);
				// 电子桌牌标准比例 800:480 = 5:3
				const aspectRatio = 5 / 3;
				
				// 计算画布可用宽度（考虑容器的margin、padding和border）
				const widthOverhead = 20 + 16 + 4; // margin: 10px*2 + padding: 8px*2 + border: 2px*2
				const maxCanvasWidth = systemInfo.windowWidth - widthOverhead;
					
				// 根据比例和可用空间计算最终尺寸
				let finalWidth, finalHeight;
				
				// 按宽度优先计算
				finalWidth = maxCanvasWidth;
				finalHeight = finalWidth / aspectRatio;
				
				// 如果高度超出限制，则按高度重新计算
				if (finalHeight > maxCanvasHeight) {
					finalHeight = maxCanvasHeight;
					finalWidth = finalHeight * aspectRatio;
				}
					
					// 确保最小尺寸，但要考虑屏幕限制
				const minWidth = Math.min(320, maxCanvasWidth); // 最小宽度不超过可用宽度
				const minHeight = minWidth / aspectRatio;
				
				// 确保尺寸不超出限制
				finalWidth = Math.min(finalWidth, maxCanvasWidth);
				finalHeight = Math.min(finalHeight, maxCanvasHeight);
				
				this.canvasWidth = Math.max(minWidth, Math.floor(finalWidth));
				this.canvasHeight = Math.max(minHeight, Math.floor(finalHeight));
					
					// 输出调试信息
				console.log('画布尺寸计算:', {
					screenSize: `${systemInfo.windowWidth}x${systemInfo.windowHeight}`,
					statusBarHeight,
					safeAreaTop,
					availableHeight,
					otherElementsHeight,
					containerAvailableHeight,
					maxCanvasWidth,
					maxCanvasHeight,
					finalSize: `${this.canvasWidth}x${this.canvasHeight}`,
					aspectRatio: (this.canvasWidth / this.canvasHeight).toFixed(2)
				});
					
				this.$nextTick(() => {
					try {
						this.canvasContext = uni.createCanvasContext('templateCanvas', this);
						if (this.canvasContext) {
							// 设置canvas的实际渲染尺寸
							if (typeof this.canvasContext.setCanvasSize === 'function') {
								this.canvasContext.setCanvasSize(this.canvasWidth, this.canvasHeight);
							}  
							this.drawCanvas();
						} else {
							console.error('Canvas context creation failed');
						}
					} catch (error) {
						console.error('Canvas initialization error:', error);
					}
				})
			},
			
			// 绘制画布 - 优化版本
			drawCanvas() {
				if (!this.canvasContext) {
					console.warn('Canvas context not available');
					return;
				}
				
				if (!this.canvasWidth || !this.canvasHeight) {
					console.warn('Canvas dimensions not set');
					return;
				}
				
				try {
					// 清空画布
					this.canvasContext.clearRect(0, 0, this.canvasWidth, this.canvasHeight)
					
					// 绘制背景
					this.drawBackground()
					
					// 绘制选中元素的高亮边框
					if (this.selectedElement) {
						this.drawElementHighlight(this.selectedElement)
					}
					
					// 绘制元素
					this.canvasElements.forEach(element => {
						this.drawElement(element)
					})
					
					this.canvasContext.draw()
				} catch (error) {
					console.error('Canvas drawing error:', error);
				}
			},
			
			// 绘制背景
			drawBackground() {
				const ctx = this.canvasContext
				
				switch (this.backgroundType) {
					case 'select':
						if (this.backgroundImageUrl) {
							// 绘制背景图片
							ctx.drawImage(this.backgroundImageUrl, 0, 0, this.canvasWidth, this.canvasHeight)
						} else {
							// 没有选择图片时使用默认白色背景
							ctx.setFillStyle('#FFFFFF')
							ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)
						}
						break
					case 'solid':
						// 绘制纯色背景
						ctx.setFillStyle(this.backgroundColor || '#FFFFFF')
						ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)
						break
					case 'clear':
						// 透明背景，不绘制任何背景
						break
					default:
						// 默认白色背景
						ctx.setFillStyle('#FFFFFF')
						ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)
						break
				}
			},
			
			// 绘制元素高亮边框
			drawElementHighlight(element) {
				// 设置高亮边框样式
				this.canvasContext.setStrokeStyle('#007AFF')
				this.canvasContext.setLineWidth(2)
				
				if (element.type === 'text') {
					const width = this.getElementWidth(element)
					const fontSize = element.fontSize || this.fontSize
					
					// 计算文本边界，考虑基线位置
					// element.y 是基线位置，文本顶部在基线上方 fontSize * 0.8
					const textTop = element.y - fontSize * 0.8
					const textBottom = element.y + fontSize * 0.2
					const textHeight = textBottom - textTop
					
					// 绘制高亮边框，添加5px边距
					this.canvasContext.strokeRect(
						element.x - width/2 - 5, 
						textTop - 5, 
						width + 10, 
						textHeight + 10
					)
				} else if (element.type === 'image') {
					// 图片元素的高亮边框
					const padding = 5 // 边框内边距
					this.canvasContext.strokeRect(
						element.x - element.width/2 - padding,
						element.y - element.height/2 - padding,
						element.width + padding * 2,
						element.height + padding * 2
					)
				}
			},
			
			// 绘制元素
			drawElement(element) {
				if (element.type === 'text') {
					this.canvasContext.setFillStyle(element.color || this.textColor)
					this.canvasContext.setFontSize(element.fontSize || this.fontSize)
					
					// 构建字体样式字符串
					const fontWeight = element.fontWeight || 'normal'
					const fontStyle = element.fontStyle || 'normal'
					const fontFamily = element.fontFamily || this.fontFamilies[0]
					const fontSize = element.fontSize || this.fontSize
					
					// 设置字体样式 - 使用uni-app canvas的正确方式
					// 注意：uni-app的canvas可能不完全支持CSS font属性，需要分别设置
					this.canvasContext.setFontSize(fontSize)
					
					// 尝试设置字体样式（某些平台可能不支持）
					try {
						const fontString = `${fontStyle} ${fontWeight} ${fontSize}px ${fontFamily}`
						if (this.canvasContext.font !== undefined) {
							this.canvasContext.font = fontString
						}
					} catch (e) {
						console.warn('设置字体样式失败，使用默认样式:', e)
					}
					
					// 设置文本对齐方式
					this.canvasContext.textAlign = 'center'
					
					// 绘制文本
					this.canvasContext.fillText(element.text, element.x, element.y)
					
					// 处理文本装饰（下划线和删除线）
					if (element.textDecoration && element.textDecoration !== 'none') {
						this.drawTextDecoration(element)
					}
				} else if (element.type === 'image') {
				// 绘制图片（支持旋转）
				if (element.src) {
					const rotation = element.rotation || 0
					
					if (rotation !== 0) {
						// 保存当前状态
						this.canvasContext.save()
						
						// 移动到图片中心点
						this.canvasContext.translate(element.x, element.y)
						
						// 旋转
						this.canvasContext.rotate(rotation * Math.PI / 180)
						
						// 绘制图片（相对于旋转后的坐标系）
						this.canvasContext.drawImage(
							element.src,
							-element.width / 2,
							-element.height / 2,
							element.width,
							element.height
						)
						
						// 恢复状态
						this.canvasContext.restore()
					} else {
						// 无旋转时的正常绘制
						this.canvasContext.drawImage(
							element.src,
							element.x - element.width / 2,
							element.y - element.height / 2,
							element.width,
							element.height
						)
					}
				}
			}
			},
			
			// 绘制文本装饰（下划线和删除线）
			drawTextDecoration(element) {
				if (!element.textDecoration || element.textDecoration === 'none') return
				
				const fontSize = element.fontSize || this.fontSize
				const textWidth = this.getElementWidth(element)
				const lineThickness = Math.max(1, fontSize / 20) // 线条粗细根据字体大小调整
				
				// 设置线条样式
				this.canvasContext.setStrokeStyle(element.color || this.textColor)
				this.canvasContext.setLineWidth(lineThickness)
				
				const startX = element.x - textWidth / 2
				const endX = element.x + textWidth / 2
				
				if (element.textDecoration === 'underline') {
					// 下划线位置：文本基线下方
					const lineY = element.y + fontSize * 0.1
					this.canvasContext.beginPath()
					this.canvasContext.moveTo(startX, lineY)
					this.canvasContext.lineTo(endX, lineY)
					this.canvasContext.stroke()
				} else if (element.textDecoration === 'line-through') {
					// 删除线位置：文本中间
					const lineY = element.y - fontSize * 0.3
					this.canvasContext.beginPath()
					this.canvasContext.moveTo(startX, lineY)
					this.canvasContext.lineTo(endX, lineY)
					this.canvasContext.stroke()
				}
			},
			
			// 画布触摸事件
			onCanvasTouchStart(e) {
				const touch = e.touches[0]
				this.lastTouchX = touch.x
				this.lastTouchY = touch.y
				this.touchStartTime = Date.now()
				
				// 检查是否点击了元素
				const element = this.getElementAtPosition(touch.x, touch.y)
				if (element) {
					this.selectedElement = element
					this.dragging = true
					// 显示操作按钮
					this.showElementOperationButtons(element)
					// 重绘画布以显示高亮边框
					this.drawCanvas()
				} else {
					// 点击空白区域，隐藏按钮
					this.hideElementButtons()
					this.selectedElement = null
					// 重绘画布以移除高亮边框
					this.drawCanvas()
				}  
			},
			
			onCanvasTouchMove(e) {
				if (!this.dragging || !this.selectedElement) return
				
				const touch = e.touches[0]
				const deltaX = touch.x - this.lastTouchX
				const deltaY = touch.y - this.lastTouchY
				
				// 计算新位置
				let newX = this.selectedElement.x + deltaX
				let newY = this.selectedElement.y + deltaY
				
				// 边界检测 - 防止元素超出画布范围
				const elementWidth = this.getElementWidth(this.selectedElement)
				const elementHeight = this.getElementHeight(this.selectedElement)
				
				// 改进边界检测，确保元素完全在画布内
				// 设置合理的边距，防止元素贴边或超出
				const margin = 10
				
				// 对于文本元素，考虑文本对齐方式的影响
				if (this.selectedElement.type === 'text') {
					const fontSize = this.selectedElement.fontSize || this.fontSize
					
					// 文本居中对齐
					const minX = margin + elementWidth / 2
					const maxX = this.canvasWidth - margin - elementWidth / 2
					
					// 考虑文本基线偏移，newY是基线位置
					// 文本顶部需要 fontSize * 0.8 的空间，底部需要 fontSize * 0.2 的空间
					const minY = margin + fontSize * 0.8
					const maxY = this.canvasHeight - margin - fontSize * 0.2
					
					newX = Math.max(minX, Math.min(newX, maxX))
					newY = Math.max(minY, Math.min(newY, maxY))
				} else if (this.selectedElement.type === 'image') {
					// 图片元素的边界检测
					const minX = margin + elementWidth / 2
					const maxX = this.canvasWidth - margin - elementWidth / 2
					const minY = margin + elementHeight / 2
					const maxY = this.canvasHeight - margin - elementHeight / 2
					
					newX = Math.max(minX, Math.min(newX, maxX))
					newY = Math.max(minY, Math.min(newY, maxY))
				} else {
					// 其他类型元素的边界检测
					const minX = margin + elementWidth / 2
					const maxX = this.canvasWidth - margin - elementWidth / 2
					const minY = margin + elementHeight / 2
					const maxY = this.canvasHeight - margin - elementHeight / 2
					
					newX = Math.max(minX, Math.min(newX, maxX))
					newY = Math.max(minY, Math.min(newY, maxY))
				}
				
				
				
				this.selectedElement.x = newX
				this.selectedElement.y = newY
				
				this.lastTouchX = touch.x
				this.lastTouchY = touch.y
				
				// 使用防抖渲染提高性能
				this.debouncedDrawCanvas()
			},
			
			onCanvasTouchEnd(e) {
				const touchEndTime = Date.now()
				const touchDuration = touchEndTime - this.touchStartTime
				
				// 如果是拖拽结束，更新按钮位置
				if (this.dragging && this.selectedElement) {
					this.updateElementButtonsPosition(this.selectedElement)
				}
				
				this.dragging = false
			},
			
			// 打开元素属性编辑弹窗
			openElementPropertyPopup() {
				if (!this.selectedElement) return
				
				// 设置当前编辑的元素
				this.editingElement = this.selectedElement
				
				// 显示弹窗
				this.showPropertyPopup = true
			},
			
			// 关闭属性编辑弹窗
			closePropertyPopup() {
				this.showPropertyPopup = false
				this.editingElement = null
			},
			
			
			
			// 文字内容编辑
			onTextChange(e) {
				const newText = e.detail.value.trim();
				console.log(this.editingElement.text);
				if (newText === '') {
					uni.showToast({
						title: '文字内容不能为空',
						icon: 'none'
					})
					// 恢复原来的文字
					this.$nextTick(() => {
						this.editingElement.text = this.editingElement.text || '文字'
					})
					return
				}
				this.editingElement.text = newText
				this.drawCanvas()
			},
			
			// 获取字体索引
			getFontFamilyIndex() {
				if (!this.editingElement || !this.editingElement.fontFamily) return 0
				return this.fontFamilies.indexOf(this.editingElement.fontFamily) || 0
			},
			

			
			// 属性编辑相关方法（立即生效）
			onFontPickerConfirm(e) {
				const { indexs, value } = e
				if (this.editingElement && this.editingElement.type === 'text') {
					this.editingElement.fontFamily = value[0]
					this.drawCanvas()
				}
				this.showFontPicker = false
			},

			toggleFontWeight() {
				if (!this.editingElement || this.editingElement.type !== 'text') return
				this.editingElement.fontWeight = this.editingElement.fontWeight === 'bold' ? 'normal' : 'bold'
				this.drawCanvas()
			},
			toggleFontStyle() {
				if (!this.editingElement || this.editingElement.type !== 'text') return
				this.editingElement.fontStyle = this.editingElement.fontStyle === 'italic' ? 'normal' : 'italic'
				this.drawCanvas()
			},
			toggleTextDecoration() {
				if (!this.editingElement || this.editingElement.type !== 'text') return
				if (this.editingElement.textDecoration === 'underline') {
					this.editingElement.textDecoration = 'none'
				} else {
					this.editingElement.textDecoration = 'underline'
				}
				this.drawCanvas()
			},
			toggleStrikethrough() {
				if (!this.editingElement || this.editingElement.type !== 'text') return
				if (this.editingElement.textDecoration === 'line-through') {
					this.editingElement.textDecoration = 'none'
				} else {
					this.editingElement.textDecoration = 'line-through'
				}
				this.drawCanvas()
			},
			onFontSizeChange(value) {
				if (this.editingElement && this.editingElement.type === 'text') {
					this.editingElement.fontSize = value
					this.drawCanvas()
				}
			},

			
			// 获取指定位置的元素 - 改进的碰撞检测算法
			getElementAtPosition(x, y) {
				// 从后往前遍历，优先选择最上层的元素
				for (let i = this.canvasElements.length - 1; i >= 0; i--) {
					const element = this.canvasElements[i]
					if (this.isPointInElement(x, y, element)) {
						return element
					}
				}
				return null
			},
			
			// 检查点是否在元素内
			isPointInElement(x, y, element) {
				if (element.type === 'text') {
					const textWidth = this.getElementWidth(element)
					const textHeight = this.getElementHeight(element)
					const fontSize = element.fontSize || this.fontSize
					
					// 考虑文本基线偏移，element.y是基线位置
					// 文本顶部位置 = element.y - fontSize * 0.8
					// 文本底部位置 = element.y + fontSize * 0.2
					const textTop = element.y - fontSize * 0.8
					const textBottom = element.y + fontSize * 0.2
					
					return x >= element.x - textWidth/2 && 
						   x <= element.x + textWidth/2 && 
						   y >= textTop && 
						   y <= textBottom
				} else if (element.type === 'image') {
					// 图片元素的碰撞检测
					return x >= element.x - element.width/2 && 
						   x <= element.x + element.width/2 && 
						   y >= element.y - element.height/2 && 
						   y <= element.y + element.height/2
				}
				return false
			},
			
			// 获取元素宽度
			getElementWidth(element) {
				if (element.type === 'text') {
					const fontSize = element.fontSize || this.fontSize
					const text = element.text || ''
					
					// 对于模板字段，使用字段名称长度来估算宽度
					if (element.isTemplate && element.fieldName) {
						return element.fieldName.length * fontSize * 0.8
					}
					
					// 改进的文本宽度计算
					// 考虑中文字符和英文字符的不同宽度
					let width = 0
					for (let i = 0; i < text.length; i++) {
						const char = text.charAt(i);
						// 中文字符、全角字符宽度约为fontSize
						if (/[\u4e00-\u9fa5\uff00-\uffef]/.test(char)) {
							width += fontSize * 0.9;
						} else {
							// 英文字符、数字等宽度约为fontSize的0.5-0.6倍
							width += fontSize * 0.55
						}
					}
					return Math.max(width, fontSize * 0.5) // 最小宽度
				} else if (element.type === 'image') {
					return element.width || 100
				}
				return 50 // 默认宽度
			},
			
			// 获取元素高度
			getElementHeight(element) {
				if (element.type === 'text') {
					return element.fontSize || this.fontSize
				} else if (element.type === 'image') {
					return element.height || 100
				}
				return 20 // 默认高度
			}, 
			//添加元素 - 增强版本
			addElement(x, y) {
				// 如果是模板文字类型，通过按钮打开弹窗，这里不处理
				if (this.insertType === 'template-text') {
					return
				}
				if (this.insertType === 'fixed-text') {
					// 改进的边界验证
					const estimatedWidth = '固定文字'.length * this.fontSize * 0.6
					const margin = 10
					
					// 文本居中对齐
					const minX = margin + estimatedWidth / 2
					const maxX = this.canvasWidth - margin - estimatedWidth / 2
					
					// 考虑文本基线偏移，Y坐标应该是基线位置
					// 文本顶部需要 fontSize * 0.8 的空间，底部需要 fontSize * 0.2 的空间
					const minY = margin + this.fontSize * 0.8
					const maxY = this.canvasHeight - margin - this.fontSize * 0.2
					
					// 使用验证后的位置
					const snappedX = Math.max(minX, Math.min(x, maxX))
					const snappedY = Math.max(minY, Math.min(y, maxY))
					
					const newElement = {
						id: Date.now(),
						type: 'text',
						text: '固定文字',
						isTemplate: false,
						x: snappedX,
						y: snappedY,
						color: this.textColor,
						fontSize: this.fontSize,
						fontWeight: this.fontWeight,
						fontStyle: this.fontStyle,
						fontFamily: this.fontFamilies[0],
						textDecoration: this.textDecoration
					}

					this.canvasElements.push(newElement)
					this.selectedElement = newElement // 自动选中新添加的元素
					this.drawCanvas()
					
					// 提供用户反馈
					uni.showToast({
						title: '元素已添加',
						icon: 'success',
						duration: 1000
					})
				}
			},

			//设置插入类型
			setInsertType(type) {
				console.log('setInsertType called with:', type)
				this.insertType = type
				// 如果选择模板文字，显示字段选择弹窗
				if (type === 'template-text') {
					// 打开模板文字弹窗
					this.showTemplatePopup = true
				} else if (type === 'fixed-text') {
				// 处理固定文字逻辑
				this.showFixedTextInput()
				} else if (type === 'image') {
					// 处理图片逻辑
					this.addImage()
				}
				// 添加用户反馈
				uni.showToast({
					title: `已选择: ${type === 'template-text' ? '模板文字' : type === 'fixed-text' ? '固定文字' : '图片'}`,
					icon: 'none',
					duration: 1000
				})
				// 强制更新视图
			this.$forceUpdate()
		},
			
		// 显示固定文字输入弹窗
			showFixedTextInput() {
				uni.showModal({
					title: '添加固定文字',
					content: '请输入文字内容',
					editable: true,
					placeholderText: '请输入文字内容',
					success: (res) => {
						if (res.confirm && res.content && res.content.trim()) {
							this.addFixedText(res.content.trim())
						} else if (res.confirm && (!res.content || !res.content.trim())) {
							uni.showToast({
								title: '文字内容不能为空',
								icon: 'none'
							})
						}
					}
				})
			},
			
			// 添加固定文字
			addFixedText(text = '固定文字') {
				// 创建临时元素用于计算宽度
				const tempElement = {
					type: 'text',
					text: text,
					fontSize: this.fontSize
				}
				
				// 计算文字宽度
				const textWidth = this.getElementWidth(tempElement)
				const margin = 10
				
				// 计算安全的初始位置
				const minX = margin + textWidth / 2
				const maxX = this.canvasWidth - margin - textWidth / 2
				const minY = margin + this.fontSize * 0.8
				const maxY = this.canvasHeight - margin - this.fontSize * 0.2
				
				// 使用画布中心位置，但确保在边界内
				const centerX = this.canvasWidth / 2
				const centerY = this.canvasHeight / 2
				
				const safeX = Math.max(minX, Math.min(centerX, maxX))
				const safeY = Math.max(minY, Math.min(centerY, maxY))
				
				const newElement = {
					id: Date.now(),
					type: 'text',
					text: text,
					x: safeX,
					y: safeY,
					fontSize: this.fontSize,
					fontFamily: this.fontFamilies[0],
					color: this.textColor,
					fontWeight: this.fontWeight,
					fontStyle: this.fontStyle,
					textDecoration: this.textDecoration
				}
				this.canvasElements.push(newElement)
				
				// 自动选中新添加的元素
				this.selectedElement = newElement
				this.drawCanvas()
				
				uni.showToast({
					title: '文字已添加',
					icon: 'success',
					duration: 1000
				})
			},
			
			// 添加图片
			addImage() {
				uni.chooseImage({
					count: 1,
					sourceType: ['album', 'camera'],
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0]
						const newElement = {
					id: Date.now(),
					type: 'image',
					src: tempFilePath,
					x: this.canvasWidth / 2,
					y: this.canvasHeight / 2,
					width: 100,
					height: 100,
					rotation: 0
				}
						this.canvasElements.push(newElement)
						this.drawCanvas()
					},
					fail: (err) => {
						uni.showToast({
							title: '选择图片失败',
							icon: 'none'
						})
					}
				})
			},

			//设置背景类型
			setBackgroundType(type) {
				this.backgroundType = type
				
				// 根据背景类型执行相应操作
				switch(type) {
					case 'select':
						this.selectBackgroundImage()
						break
					case 'solid':
						this.setSolidBackground()
						break
					case 'clear':
						this.clearBackground()
						break
				}
				
				this.drawCanvas()
			},
			
			// 选择背景图片
			selectBackgroundImage() {
				uni.navigateTo({
					url: '/pages/subPackage/template/backgroundList?select=true',
					fail: (error) => {
						console.error('跳转背景选择页面失败:', error)
						uni.showToast({
							title: '打开背景选择失败',
							icon: 'none'
						})
					}
				})
			},
			
			// 检查选择的背景图片
			checkSelectedBackground() {
				try {
					const selectedBackground = uni.getStorageSync('selectedBackgroundImage')
					if (selectedBackground) {
						// 设置背景图片
						this.backgroundImageUrl = selectedBackground
						this.backgroundType = 'select'
						this.drawCanvas()
						
						// 清除存储，避免重复使用
						uni.removeStorageSync('selectedBackgroundImage')
						
						uni.showToast({
							title: '背景设置成功',
							icon: 'success'
						})
					}
				} catch (error) {
					console.error('检查背景选择失败:', error)
				}
			},
			
			// 设置桌牌类型
			setCardType(type) {
				this.cardType = type
				
				// 更新颜色选择器选项
				if (type === 'three-color') {
					this.colorOptions = [...this.threeColorOptions]
					this.colorValues = [...this.threeColorValues]
				} else {
					this.colorOptions = [...this.sixColorOptions]
					this.colorValues = [...this.sixColorValues]
				}
				
				uni.showToast({
					title: `已切换到${type === 'three-color' ? '三色' : '六色'}桌牌模式`,
					icon: 'success'
				})
			},
			
			// 设置纯色背景
			setSolidBackground() {
				// 根据桌牌类型提供不同的背景选择
				let itemList = []
				let colorMap = {}
				
				if (this.cardType === 'three-color') {
					// 三色桌牌：黑、白、红
					itemList = ['黑色背景', '白色背景', '红色背景']
					colorMap = {
						0: '#000000',
						1: '#FFFFFF', 
						2: '#FF0000'
					}
				} else {
					// 六色桌牌：黑、白、红、黄、蓝、绿
					itemList = ['黑色背景', '白色背景', '红色背景', '黄色背景', '蓝色背景', '绿色背景']
					colorMap = {
						0: '#000000',
						1: '#FFFFFF',
						2: '#FF0000',
						3: '#FFFF00',
						4: '#0000FF',
						5: '#00FF00'
					}
				}
				
				uni.showActionSheet({
					itemList: itemList,
					success: (res) => {
						const lastIndex = itemList.length - 1
						
						// 使用预设颜色
						this.backgroundColor = colorMap[res.tapIndex]
						this.backgroundImageUrl = ''
						this.drawCanvas()
						
						uni.showToast({
								title: '背景设置成功',
								icon: 'success'
							})
						}
					})
			},
			// 清除背景
			clearBackground() {
				uni.showModal({
					title: '清除背景',
					content: '确定要清除当前背景吗？',
					success: (res) => {
						if (res.confirm) {
							this.backgroundImageUrl = ''
							this.backgroundColor = 'transparent'
							this.drawCanvas()
							
							uni.showToast({
								title: '背景已清除',
								icon: 'success'
							})
						}
					}
				})
			}, 
			// 颜色选择相关方法
			getColorIndex() {
				if (!this.editingElement || !this.editingElement.color) return 0
				const index = this.colorValues.indexOf(this.editingElement.color)
				return index >= 0 ? index : 0
			},
			
			getColorName() {
				if (!this.editingElement || !this.editingElement.color) return this.colorOptions[0]
				const index = this.colorValues.indexOf(this.editingElement.color)
				return index >= 0 ? this.colorOptions[index] : this.colorOptions[0]
			},
			
			onColorPickerConfirm(e) {
				const { indexs, value } = e
				if (this.editingElement && this.editingElement.type === 'text') {
					const colorIndex = indexs[0]
					this.editingElement.color = this.colorValues[colorIndex]
					this.drawCanvas()
				}
				this.showColorPicker = false
			},
			
			// 防抖函数
			debounce(func, wait) {
				let timeout
				return function executedFunction(...args) {
					const later = () => {
						clearTimeout(timeout)
						func.apply(this, args)
					}
					clearTimeout(timeout)
					timeout = setTimeout(later, wait)
				}
			},
			// 保存模板 - 增强版本
			saveTemplate() {
				// 验证模板数据
				if (!this.templateName.trim()) {
					uni.showToast({
						title: '请输入模板名称',
						icon: 'none'
					})
					return
				}
				
				if (this.canvasElements.length === 0) {
					uni.showModal({
						title: '提示',
						content: '模板中没有任何元素，确定要保存吗？',
						success: (res) => {
							if (res.confirm) {
								this.performSave()
							}
						}
					})
					return
				} 
				this.performSave()
			},
			
			// 执行保存操作
			performSave() {
				const templateData = {
					name: this.templateName.trim(),
					canvasSize: {
						width: this.canvasWidth,
						height: this.canvasHeight
					},
					elements: this.canvasElements.map(el => ({
						...el,
						// 确保所有属性都被保存
						fontFamily: this.fontFamilies[this.fontFamilyIndex]
					})),
					settings: {
						backgroundType: this.backgroundType,
						backgroundImageUrl: this.backgroundImageUrl,
						backgroundColor: this.backgroundColor
					},
					createdAt: new Date().toISOString(),
					version: '1.0'
				}
				
				// 这里可以添加实际的保存逻辑，如发送到服务器
				console.log('保存模板数据:', templateData)
				
				uni.showToast({
					title: '模板保存成功',
					icon: 'success'
				})
			},
			
			// 模板文字弹窗相关方法
			confirmAddTemplateText() {
				// 检查是否有输入内容
				if (!this.templateFields.name && !this.templateFields.position && 
					!this.templateFields.company && !this.templateFields.other) {
					uni.showToast({
						title: '请至少输入一项内容',
						icon: 'none'
					})
					return
				}
				
				// 获取画布中心位置
				const centerX = this.canvasWidth / 2;
				const centerY = this.canvasHeight / 2;
				
				// 为每个有内容的字段创建独立的元素对象，设置字体层次和居中布局
				// 根据画布高度动态调整间距，适应不同屏幕尺寸
				const baseSpacing = Math.min(this.canvasHeight * 0.15, 40) // 基础间距不超过40px
				
				// 根据画布大小动态计算字体粗细
				const canvasArea = this.canvasWidth * this.canvasHeight
				const baseFontWeight = Math.min(Math.max(canvasArea / 50000, 0.5), 1.2) // 基础字体粗细系数
				
				const fieldConfigs = [
					{ key: 'name', label: '姓名', offsetY: -baseSpacing * 1.5, fontSizeRatio: 1.3, fontWeight: Math.round(700 * baseFontWeight).toString() },
					{ key: 'position', label: '职位', offsetY: -baseSpacing * 0.5, fontSizeRatio: 1.0, fontWeight: Math.round(600 * baseFontWeight).toString() },
					{ key: 'company', label: '公司', offsetY: baseSpacing * 0.5, fontSizeRatio: 0.9, fontWeight: Math.round(500 * baseFontWeight).toString()},
					{ key: 'other', label: '其他', offsetY: baseSpacing * 1.5, fontSizeRatio: 0.8, fontWeight: Math.round(400 * baseFontWeight).toString()}
				]
				
				let addedCount = 0
				const newElements = []
				
				fieldConfigs.forEach(config => {
					const fieldValue = this.templateFields[config.key]
					if (fieldValue && fieldValue.trim()) {
						const fontSize = Math.round(this.fontSize * config.fontSizeRatio)
						
						// 计算文本尺寸用于边界检测
						const textWidth = fieldValue.length * fontSize * 0.6
						const textHeight = fontSize * this.heightStretch
						
						// 边界检测，确保文本不超出画布
						const margin = 20
						const minX = margin + textWidth / 2
						const maxX = this.canvasWidth - margin - textWidth / 2
						// 考虑文本基线偏移，Y坐标应该是基线位置
						const minY = margin + fontSize * 0.8
						const maxY = this.canvasHeight - margin - fontSize * 0.2
						
						// 应用边界约束
						const safeX = Math.max(minX, Math.min(centerX, maxX))
						const safeY = Math.max(minY, Math.min(centerY + config.offsetY, maxY))
						
						const newElement = {
						id: Date.now() + addedCount,
						type: 'text',
						text: fieldValue,
						isTemplate: true,
						templateKey: config.key,
						templateLabel: config.label,
						templateData: { [config.key]: fieldValue },
						x: safeX,
						y: safeY,
						color: this.textColor,
						fontSize: fontSize,
						fontWeight: config.fontWeight,
						fontStyle: this.fontStyle,
						fontFamily: this.fontFamilies[0],
						textDecoration: this.textDecoration
					}
						newElements.push(newElement)
						this.canvasElements.push(newElement)
						addedCount++
					}
				})
				
				// 选中最后一个添加的元素
				if (newElements.length > 0) {
					this.selectedElement = newElements[newElements.length - 1]
				}
				
				this.drawCanvas()
				
				// 关闭弹窗并清空输入
				this.onTemplatePopupClose()
				
				uni.showToast({
					title: `已添加${addedCount}个模板字段`,
					icon: 'success',
					duration: 1500
				})
			}, 
			onTemplatePopupClose() {
				// 关闭弹窗时重置插入类型
				this.showTemplatePopup = false;
				this.insertType = '';
				// 清空输入字段
				this.templateFields = {
					name: '',
					position: '',
					company: '',
					other: ''
				}
			}, 

			
			// 批量修改模板字段方法
			batchUpdateTemplateFields(updates) {
				// updates 格式: { key: newValue, key2: newValue2 }
				let updatedCount = 0
				
				this.canvasElements.forEach(element => {
					if (element.isTemplate && element.templateKey) {
						const newValue = updates[element.templateKey]
						if (newValue !== undefined && newValue !== null) {
							// 更新元素文本
							element.text = newValue
							// 更新模板数据
							element.templateData[element.templateKey] = newValue
							updatedCount++
						}
					}
				})
				
				if (updatedCount > 0) {
					this.drawCanvas()
					uni.showToast({
						title: `已更新${updatedCount}个字段`,
						icon: 'success',
						duration: 1500
					})
				}
				
				return updatedCount
			},
			
			// 根据模板key获取所有相关元素
			getElementsByTemplateKey(templateKey) {
				return this.canvasElements.filter(element => 
					element.isTemplate && element.templateKey === templateKey
				)
			},
			
			// 删除指定模板key的所有元素
			removeElementsByTemplateKey(templateKey) {
				const initialLength = this.canvasElements.length
				this.canvasElements = this.canvasElements.filter(element => 
					!(element.isTemplate && element.templateKey === templateKey)
				)
				
				const removedCount = initialLength - this.canvasElements.length
				if (removedCount > 0) {
					this.selectedElement = null
					this.drawCanvas()
					uni.showToast({
						title: `已删除${removedCount}个${templateKey}字段`,
						icon: 'success',
						duration: 1500
					})
				}
				
				return removedCount
			},
			
			// 获取所有模板字段的统计信息
			getTemplateFieldsStats() {
				const stats = {}
				
				this.canvasElements.forEach(element => {
					if (element.isTemplate && element.templateKey) {
						if (!stats[element.templateKey]) {
							stats[element.templateKey] = {
								count: 0,
								label: element.templateLabel || element.templateKey,
								elements: []
							}
						}
						stats[element.templateKey].count++
						stats[element.templateKey].elements.push(element)
					}
				})
				
				return stats
			},
			
			// 预览弹窗相关方法
			openPreview() {
				// 生成预览图片
				this.previewLoading = true
				this.showPreview = true
				
				// 模拟生成预览图片的过程
				setTimeout(() => {
					// 这里应该调用画布截图API生成预览图
					this.generatePreviewImage()
				}, 500)
			},
			
			generatePreviewImage() {
				// 使用canvas生成预览图片
				uni.canvasToTempFilePath({
					canvasId: 'templateCanvas',
					success: (res) => {
						this.previewImagePath = res.tempFilePath
						this.previewLoading = false
					},
					fail: (err) => {
						console.error('生成预览图片失败:', err)
						this.previewLoading = false
						uni.showToast({
							title: '预览生成失败',
							icon: 'none'
						})
					}
				}, this)
			},
			
			closePreview() {
				this.showPreview = false
				this.previewImagePath = ''
				this.previewLoading = false
			},
			
			savePreviewImage() {
				// 保存预览图片的逻辑
				uni.showToast({
					title: '图片保存功能待实现',
					icon: 'none'
				})
			},
			
			//批量操作方法
			centerAllElements() {
				if (this.canvasElements.length === 0) {
					uni.showToast({
						title: '没有可操作的元素',
						icon: 'none'
					})
					return
				}
				
				// 按Y坐标排序元素
				const sortedElements = [...this.canvasElements].sort((a, b) => a.y - b.y)
				
				// 计算元素尺寸和边界
				const margin = 20 // 边界留白
				const elementsWithSize = sortedElements.map(element => {
					const textWidth = this.getElementWidth(element)
					const textHeight = element.fontSize || this.fontSize
					return {
						...element,
						textWidth,
						textHeight
					}
				}) 
				// 计算可用空间和分布参数
				const maxTextWidth = Math.max(...elementsWithSize.map(e => e.textWidth))
				const totalTextHeight = elementsWithSize.reduce((sum, e) => sum + e.textHeight, 0)
				const availableWidth = this.canvasWidth - 2 * margin
				const availableHeight = this.canvasHeight - 2 * margin
				
				// 检查是否有足够空间
				if (maxTextWidth > availableWidth) {
					uni.showToast({
						title: '元素宽度超出画布范围',
						icon: 'none'
					})
					return
				}
				
				// 计算垂直分布
				const canvasCenterX = this.canvasWidth / 2
				const remainingHeight = availableHeight - totalTextHeight
				const spacing = elementsWithSize.length > 1 ? remainingHeight / (elementsWithSize.length + 1) : remainingHeight / 2
				
				// 重新分布元素，确保不超出边界
				let currentY = margin + spacing
				elementsWithSize.forEach((element) => {
					// 水平居中，确保不超出边界
					const halfWidth = element.textWidth / 2
					element.x = Math.max(margin + halfWidth, Math.min(canvasCenterX, this.canvasWidth - margin - halfWidth))
					
					// 垂直分布，确保不超出边界
					element.y = Math.max(margin + element.textHeight / 2, Math.min(currentY + element.textHeight / 2, this.canvasHeight - margin - element.textHeight / 2))
					
					currentY += element.textHeight + spacing
				})
				
				this.drawCanvas()
				
				uni.showToast({
					title: '元素已垂直分布',
					icon: 'success',
					duration: 1000
				})
			},
			// 清空画布
			clearAllElements() {
				uni.showModal({
					title: '确认清空',
					content: '确定要清空画布上的所有元素吗？此操作不可撤销。',
					success: (res) => {
						if (res.confirm) {
							const elementCount = this.canvasElements.length
							this.canvasElements = []
							this.selectedElement = null
							this.hideElementButtons()
							this.drawCanvas()

							uni.showToast({
								title: `已清空${elementCount}个元素`,
								icon: 'success',
								duration: 1500
							})
						}
					}
				});
			},
			
			// 显示元素操作按钮
			showElementOperationButtons(element) {
				if (!element) return
				
				// 清除之前的定时器
				if (this.buttonHideTimer) {
					clearTimeout(this.buttonHideTimer)
					this.buttonHideTimer = null
				}
				
				// 计算按钮位置
				this.updateElementButtonsPosition(element)
				
				// 显示按钮
				this.showElementButtons = true
				
				// 设置自动隐藏定时器
				this.buttonHideTimer = setTimeout(() => {
					this.hideElementButtons()
				}, 3000) // 3秒后自动隐藏
			},
			
			// 更新按钮位置
			updateElementButtonsPosition(element) {
				if (!element) return
				
				const elementWidth = this.getElementWidth(element)
				const elementHeight = this.getElementHeight(element)
				
				// 计算元素边界
				let elementLeft, elementTop, elementRight, elementBottom
				
				if (element.type === 'text') {
					const fontSize = element.fontSize || this.fontSize
					elementLeft = element.x - elementWidth / 2
					elementRight = element.x + elementWidth / 2
					elementTop = element.y - fontSize * 0.8
					elementBottom = element.y + fontSize * 0.2
				} else if (element.type === 'image') {
					// 图片元素边界
					elementLeft = element.x - element.width / 2
					elementRight = element.x + element.width / 2
					elementTop = element.y - element.height / 2
					elementBottom = element.y + element.height / 2
				}
				
				// 按钮尺寸
			const buttonSize = 32
			const buttonSpacing = 8
			
			// 根据元素类型计算按钮数量
			const buttonCount = element.type === 'image' ? 3 : 2
			const totalButtonWidth = buttonSize * buttonCount + buttonSpacing * (buttonCount - 1)
			
			// 计算按钮位置（在元素右上角）
			let buttonX = elementRight + 5
			let buttonY = elementTop - buttonSize - 5
				
				// 边界检测，确保按钮不超出画布
				if (buttonX + totalButtonWidth > this.canvasWidth) {
					buttonX = elementLeft - totalButtonWidth - 5
				}
				if (buttonY < 0) {
					buttonY = elementBottom + 5
				}
				
				// 确保按钮不超出左边界
				if (buttonX < 0) {
					buttonX = 5
				}
				
				// 确保按钮不超出下边界
				if (buttonY + buttonSize > this.canvasHeight) {
					buttonY = this.canvasHeight - buttonSize - 5
				}
				
				this.elementButtonsPosition = { x: buttonX, y: buttonY }
			},
			
			// 隐藏元素操作按钮
			hideElementButtons() {
				this.showElementButtons = false
				if (this.buttonHideTimer) {
					clearTimeout(this.buttonHideTimer)
					this.buttonHideTimer = null
				}
			},
			
			// 编辑选中元素
			editSelectedElement() {
				if (!this.selectedElement) return
				
				// 隐藏操作按钮
				this.hideElementButtons()
				
				// 打开属性编辑弹窗
				this.openElementPropertyPopup()
			},
			
			// 删除选中元素
			deleteSelectedElement() {
				if (!this.selectedElement) return
				
				uni.showModal({
					title: '确认删除',
					content: '确定要删除这个元素吗？',
					success: (res) => {
						if (res.confirm) {
							// 找到元素索引并删除
							const index = this.canvasElements.findIndex(el => el.id === this.selectedElement.id)
							if (index !== -1) {
								this.canvasElements.splice(index, 1)
								
								// 清除选中状态和按钮
								this.selectedElement = null
								this.hideElementButtons()
								
								// 重绘画布
								this.drawCanvas()
								
								uni.showToast({
									title: '元素已删除',
									icon: 'success',
									duration: 1000
								})
							}
						}
					}
				})
			}, 

			// 放大图片
			zoomInImage() {
				if (!this.selectedElement || this.selectedElement.type !== 'image') return
				
				console.log('放大图片')
				
				const currentWidth = this.selectedElement.width || 100
				const currentHeight = this.selectedElement.height || 100
				
				// 等比例放大5%
				const newWidth = currentWidth * 1.05
				const newHeight = currentHeight * 1.05
				
				// 边界检测，确保放大后的图片不超出画布
				const margin = 10
				const maxWidth = (this.canvasWidth - margin * 2)
				const maxHeight = (this.canvasHeight - margin * 2)
				
				this.selectedElement.width = Math.min(newWidth, maxWidth)
				this.selectedElement.height = Math.min(newHeight, maxHeight)
				
				// 确保图片中心不超出边界
				const halfWidth = this.selectedElement.width / 2
				const halfHeight = this.selectedElement.height / 2
				
				this.selectedElement.x = Math.max(halfWidth + margin, 
					Math.min(this.selectedElement.x, this.canvasWidth - halfWidth - margin))
				this.selectedElement.y = Math.max(halfHeight + margin, 
					Math.min(this.selectedElement.y, this.canvasHeight - halfHeight - margin))
				
				console.log('图片放大完成:', {
					width: this.selectedElement.width,
					height: this.selectedElement.height
				})
				
				this.drawCanvas()
			},
			
			// 缩小图片
			zoomOutImage() {
				if (!this.selectedElement || this.selectedElement.type !== 'image') return
				
				console.log('缩小图片')
				
				const currentWidth = this.selectedElement.width || 100
				const currentHeight = this.selectedElement.height || 100
				
				// 等比例缩小5%
				const newWidth = currentWidth * 0.95
				const newHeight = currentHeight * 0.95
				
				// 设置最小尺寸限制
				const minSize = 20
				
				this.selectedElement.width = Math.max(newWidth, minSize)
				this.selectedElement.height = Math.max(newHeight, minSize)
				
				console.log('图片缩小完成:', {
					width: this.selectedElement.width,
					height: this.selectedElement.height
				})
				
				this.drawCanvas()
			},
			
			// 旋转图片
			rotateImage() {
				if (!this.selectedElement || this.selectedElement.type !== 'image') return
				
				console.log('旋转图片')
				
				const currentRotation = this.selectedElement.rotation || 0
				
				// 每次点击旋转15度
				this.selectedElement.rotation = (currentRotation + 15) % 360
				
				console.log('图片旋转完成:', {
					rotation: this.selectedElement.rotation
				})
				
				this.drawCanvas()
			}
		}
	}

</script>

<style scoped>
	.template-editor {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
	}
	
	/* 预览区域 */
	.preview-container {
		position: relative;
		margin: 10px;
		padding: 8px;
		background-color: #ffffff;
		border-radius: 8px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
		border: 2px solid #e0e0e0;
		overflow: visible; /* 确保按钮不被裁剪 */
	}
	
	.preview-canvas {
		display: block;
		width: 100%;
		height: 100%;
		z-index: 1000;
	}
	
	/* 元素操作按钮 */
	.element-buttons {
		position: absolute;
		display: flex;
		gap: 8px;
		z-index: 99999999;
		pointer-events: auto;
	}
	
	.element-btn {
		width: 32px;
		height: 32px;
		border-radius: 16px;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
		cursor: pointer;
		transition: all 0.3s ease;
		border: 2px solid #fff;
		pointer-events: auto;
		user-select: none;
		-webkit-user-select: none;
		touch-action: manipulation;
		-webkit-touch-callout: none;
		-webkit-tap-highlight-color: transparent;
		position: relative;
		z-index: 99999999;
	}
	
	.edit-btn {
		background-color: #007AFF;
	}
	
	.zoom-in-btn {
		background-color: #007AFF;
	}
	
	.zoom-out-btn {
		background-color: #FF9500;
	}
	
	.rotate-btn {
		background-color: #FF9500;
	}
	
	.delete-btn {
		background-color: #FF3B30;
	}
	
	.element-btn:hover {
		transform: scale(1.1);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
	}
	
	.element-btn:active {
		transform: scale(0.95);
	}
	
	.btn-icon {
		font-size: 14px;
		color: #fff;
		line-height: 1;
	}
	

	
	/* 设置区域 */
	.template-settings, .background-settings {
		padding: 15px;
		margin: 0 10px 10px;
		background-color: #ffffff;
		border-radius: 8px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
	}
	
	/* 批量操作区域 */
	.control-group {
		padding: 15px;
		margin: 0 10px 10px;
		background-color: #ffffff;
		border-radius: 8px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
	}
	
	.group-title {
		font-size: 16px;
		font-weight: bold;
		color: #333;
		margin-bottom: 12px;
		display: block;
	}
	
	.button-row {
		display: flex;
		gap: 8px;
		margin-bottom: 8px;
	}
	
	.button-row:last-child {
		margin-bottom: 0;
	}
	
	.control-btn {
		flex: 1;
		height: 36px;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1px solid #ddd;
		border-radius: 6px;
		background-color: #fff;
		font-size: 13px;
		color: #666;
		transition: all 0.3s ease;
		cursor: pointer;
	}
	
	.control-btn:hover {
		background-color: #f8f9fa;
		border-color: #007AFF;
		color: #007AFF;
		transform: translateY(-1px);
		box-shadow: 0 2px 8px rgba(0, 122, 255, 0.15);
	}
	
	.control-btn:active {
		transform: translateY(0);
		box-shadow: 0 1px 4px rgba(0, 122, 255, 0.2);
	}
	
	/* 危险操作按钮 */
	.danger-btn {
		border-color: #ff4757;
		color: #ff4757;
	}
	
	.danger-btn:hover {
		background-color: #ff4757;
		color: #fff;
		border-color: #ff4757;
		box-shadow: 0 2px 8px rgba(255, 71, 87, 0.25);
	}
	
	.danger-btn:active {
		background-color: #ff3742;
		border-color: #ff3742;
	}
	
	.setting-row {
		display: flex;
		align-items: center;
		margin-bottom: 15px;
		position: relative;
		z-index: 2; /* 提高层级 */
		pointer-events: auto; /* 确保可点击 */
	}
	
	.setting-row:last-child {
		margin-bottom: 0;
	}
	
	.setting-label {
		font-size: 14px;
		color: #333;
		width: 80px;
		flex-shrink: 0;
	}
	
	.template-name-input {
		flex: 1;
		height: 35px;
		padding: 0 10px;
		border: 1px solid #ddd;
		border-radius: 4px;
		font-size: 14px;
	}
	
	/* 按钮组 */
	.insert-options, .background-options {
		display: flex;
		flex: 1;
		gap: 8px;
		position: relative;
		z-index: 2; /* 提高层级 */
	}
	
	.insert-btn, .bg-btn {
		flex: 1;
		height: 35px;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1px solid #ddd;
		border-radius: 4px;
		background-color: #fff;
		font-size: 12px;
		color: #666;
		transition: all 0.3s;
		position: relative; /* 确保定位正确 */
		z-index: 1; /* 提高层级 */
		pointer-events: auto; /* 确保可点击 */
	}
	
	.insert-btn.active, .bg-btn.active {
		background-color: #d32f2f;
		color: #fff;
		border-color: #d32f2f;
	}
	
	/* 属性面板 */
	.property-panel {
		/* flex: 1; */
		padding: 15px;
		margin: 0 10px;
		background-color: #ffffff;
		border-radius: 8px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
		height: 40vh;
		display: flex;
		flex-direction: column;
		overflow: hidden;
	}
	
	/* 属性内容容器 */
	.property-container {
		/* flex: 1; */
		overflow-y: auto;
		-webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
		scrollbar-width: thin; /* Firefox细滚动条 */
		scrollbar-color: #c1c1c1 transparent; /* Firefox滚动条颜色 */
	}
	
	/* 自定义滚动条样式 - Webkit内核 */
	.property-container::-webkit-scrollbar {
		width: 6px;
	}
	
	.property-container::-webkit-scrollbar-track {
		background: transparent;
		border-radius: 3px;
	}
	
	.property-container::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 3px;
		transition: background 0.3s ease;
	}
	
	.property-container::-webkit-scrollbar-thumb:hover {
		background: #a8a8a8;
	}
	
	/* 响应式设计 - 小屏幕优化 */
	@media (max-height: 600px) {
		.property-panel {
			height: 40vh; /* 小屏幕增加高度比例 */
		}
	}
	
	@media (max-height: 500px) {
		.property-panel {
			height: 45vh; /* 更小屏幕进一步增加高度比例 */
		}
	}
	
	/* 移动端触摸优化 */
	@media (max-width: 768px) {
		.property-panel {
			padding: 12px; /* 移动端减少内边距 */
			margin: 0 5px; /* 减少外边距 */
		}
		
		.property-container::-webkit-scrollbar {
			width: 4px; /* 移动端更细的滚动条 */
		}
	}
	
	/* 面板头部 - 固定定位 */
	.panel-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15px;
		position: sticky;
		top: 0;
		z-index: 10;
		background-color: #ffffff;
		padding: 10px 0;
		border-bottom: 1px solid #f0f0f0;
		margin: -15px -15px 15px -15px;
		padding-left: 15px;
		padding-right: 15px;
	}
	
	.panel-title {
		font-size: 16px;
		font-weight: bold;
		color: #333;
	}
	
	
	.property-group {
		margin-bottom: 20px;
	}
	
	.property-row {
		display: flex;
		align-items: center;
		margin-top:9px ;
		margin-bottom: 9px;
	}
	
	.property-label {
		font-size: 13px;
		color: #333;
		width: 70px;
		flex-shrink: 0;
	}
	
	/* 对齐按钮 */
	.align-options, .style-options {
		display: flex;
		flex: 1;
		gap: 4px;
	}
	
	.align-btn, .style-btn {
		width: 32px;
		height: 32px;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1px solid #ddd;
		border-radius: 4px;
		background-color: #fff;
		font-size: 14px;
		color: #666;
		transition: all 0.3s;
	}
	
	.align-btn.active, .style-btn.active {
		background-color: #d32f2f;
		color: #fff;
		border-color: #d32f2f;
	}
	

	
	/* 保存按钮容器 */
	.save-container {
		padding: 15px;
		display: flex;
		justify-content: center;
		gap: 15px;
		flex-wrap: wrap;
	}
	
	/* 预览按钮 */
	.preview-btn {
		width: 140px;
		height: 45px;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(135deg, #4A90E2 0%, #357ABD 50%, #4A90E2 100%);
		border-radius: 25px;
		position: relative;
		overflow: hidden;
		box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
		transition: all 0.3s ease;
	}
	
	.preview-btn:hover {
		transform: translateY(-2px);
		box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
	}
	
	.preview-btn::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
		transition: left 0.5s;
	}
	
	.preview-btn:active::before {
		left: 100%;
	}
	
	.preview-btn:active {
		transform: translateY(0);
	}
	
	/* 保存按钮 */
	.save-btn {
		width: 140px;
		height: 45px;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(135deg, #8B0000 0%, #DC143C 50%, #8B0000 100%);
		border-radius: 25px;
		position: relative;
		overflow: hidden;
		box-shadow: 0 4px 15px rgba(139, 0, 0, 0.3);
		transition: all 0.3s ease;
	}
	
	.save-btn:hover {
		transform: translateY(-2px);
		box-shadow: 0 6px 20px rgba(139, 0, 0, 0.4);
	}
	
	.save-btn::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
		transition: left 0.5s;
	}
	
	.save-btn:active::before {
		left: 100%;
	}
	
	.save-btn:active {
		transform: translateY(0);
	}
	
	/* 按钮图标样式 */
	.btn-icon {
		color: #fff;
		font-size: 18px;
		margin-right: 6px;
		z-index: 1;
		pointer-events: none;
		filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.3));
	}
	
	/* 按钮文字样式 */
	.btn-text {
		color: #fff;
		font-size: 16px;
		font-weight: bold;
		text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
		z-index: 1;
		pointer-events: none;
	}
	
	/* 响应式设计 - 保持并排显示 */
	@media (max-width: 480px) {
		.save-container {
			padding: 10px;
			gap: 8px;
			justify-content: center;
		}
		
		.preview-btn,
		.save-btn {
			width: 110px;
			height: 40px;
		}
		
		.btn-icon {
			font-size: 16px;
			margin-right: 4px;
		}
		
		.btn-text {
			font-size: 14px;
		}
	}
	
	/* 超小屏幕优化 - 保持并排显示 */
	@media (max-width: 360px) {
		.save-container {
			gap: 6px;
			padding: 8px;
		}
		
		.preview-btn,
		.save-btn {
			width: 90px;
			height: 36px;
		}
		
		.btn-icon {
			font-size: 14px;
			margin-right: 2px;
		}
		
		.btn-text {
			font-size: 12px;
		}
	}
	
	/* 模板字段选择器弹窗 */
	.field-picker-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: flex-end;
		justify-content: center;
		z-index: 1000;
		animation: fadeIn 0.3s ease-out;
	}
	
	.field-picker-content {
		background-color: #fff;
		border-radius: 12px 12px 0 0;
		padding: 0;
		max-width: 90%;
		max-height: 80%;
		width: 400px;
		box-shadow: 0 -5px 30px rgba(0, 0, 0, 0.3);
		overflow: hidden;
		animation: slideUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		transform-origin: bottom;
	}
	
	/* 弹窗动画 */
	@keyframes fadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}
	
	@keyframes slideUp {
		from {
			transform: translateY(100%);
			opacity: 0.8;
		}
		to {
			transform: translateY(0);
			opacity: 1;
		}
	}
	
	/* 退出动画 */
	.field-picker-modal.fade-out {
		animation: fadeOut 0.25s ease-in;
	}
	
	.field-picker-modal.fade-out .field-picker-content {
		animation: slideDown 0.3s cubic-bezier(0.55, 0.06, 0.68, 0.19);
	}
	
	@keyframes fadeOut {
		from {
			opacity: 1;
		}
		to {
			opacity: 0;
		}
	}
	
	@keyframes slideDown {
		from {
			transform: translateY(0);
			opacity: 1;
		}
		to {
			transform: translateY(100%);
			opacity: 0.8;
		}
	}
	
	.field-picker-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20px;
		background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
		color: white;
	}
	
	.field-picker-title {
		font-size: 18px;
		font-weight: bold;
	}
	
	.field-picker-close {
		width: 30px;
		height: 30px;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		background-color: rgba(255, 255, 255, 0.2);
		font-size: 20px;
		cursor: pointer;
		transition: background-color 0.3s;
	}
	
	.field-picker-close:hover {
		background-color: rgba(255, 255, 255, 0.3);
	}
	
	.field-grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 15px;
		padding: 20px;
		max-height: 400px;
		overflow-y: auto;
	}
	
	.field-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 15px;
		border: 2px solid #e0e0e0;
		border-radius: 8px;
		background-color: #fafafa;
		cursor: pointer;
		transition: all 0.3s;
		text-align: center;
	}
	
	.field-item:hover {
		border-color: #d32f2f;
		background-color: #fff;
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(211, 47, 47, 0.15);
	}
	
	.field-icon {
		font-size: 24px;
		margin-bottom: 8px;
	}
	
	.field-name {
		font-size: 14px;
		font-weight: bold;
		color: #333;
		margin-bottom: 4px;
	}
	
	.field-desc {
		font-size: 12px;
		color: #666;
		line-height: 1.3;
	}
	

	
	/* 位置信息显示 */
	.position-info {
		flex: 1;
		font-size: 12px;
		color: #666;
		background-color: #f8f9fa;
		padding: 6px 10px;
		border-radius: 4px;
		border: 1px solid #e9ecef;
		font-family: 'Courier New', monospace;
	}
	
	 
	
	/* 预览容器增强 */
	.preview-container {
		position: relative;
		background-color: #ffffff;
		margin: 10px;
		border-radius: 8px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		overflow: visible; /* 改为visible避免裁剪 */ 
		display: flex;
		align-items: flex-start; /* 改为flex-start避免压缩 */
		justify-content: center;
		padding: 8px;
		box-sizing: border-box;
		touch-action: none; /* 防止默认触摸行为 */
	}
	
	/* 画布样式 */
	.preview-canvas {
		display: block;
		border: 2px solid #e0e0e0;
		border-radius: 4px;
		background-color: #fafafa;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
		/* 移除max-width和max-height限制，让画布按计算尺寸显示 */
		transition: box-shadow 0.3s ease;
	}
	
	.preview-canvas:hover {
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
	}
	
	/* 弹窗样式 */
	.template-popup-content, .preview-popup-content, .edit-text-popup-content, .property-popup-content {
		padding: 20px;
		background-color: #fff;
		border-radius: 16px;
		min-width: 300px;
		max-width: 90vw;
	}
	
	/* 属性编辑弹窗特定样式 */
	.property-popup-content {
		max-width: 100vw;
		max-height: 70vh;
		display: flex;
		flex-direction: column;
		padding: 0;
		border-radius: 16px 16px 0 0;
	}

	.property-title {
		font-size: 18px;
		font-weight: bold;
		color: #333;
		text-align: center;
		padding: 16px 0;
		border-bottom: 1px solid #f0f0f0;
		background-color: #fff;
		flex-shrink: 0;
		border-radius: 16px 16px 0 0;
	}

	.popup-header {
		padding: 20px 20px 0 20px;
		flex-shrink: 0;
	}

	.popup-scroll-content {
		flex: 1;
		padding: 8px 16px;
		overflow-y: auto;
	}
 
	.style-buttons {
		display: flex;
		gap: 8px;
		flex: 1;
		margin-left: 12px;
	}
	
	.style-btn {
		width: 32px;
		height: 32px;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1px solid #e0e0e0;
		border-radius: 4px;
		background-color: #fff;
		cursor: pointer;
		transition: all 0.2s;
		user-select: none;
	}
	
	.style-btn:hover {
		background-color: #f0f0f0;
		border-color: #007aff;
	}
	
	.style-btn.active {
		background-color: #007aff;
		border-color: #007aff;
		color: #fff;
	}  

	.popup-title {
		font-size: 18px;
		font-weight: bold;
		color: #333;
		text-align: center;
		margin-bottom: 20px;
	}
	
	.template-field {
		margin-bottom: 15px;
	}
	
	.field-label {
		font-size: 14px;
		color: #333;
		margin-bottom: 8px;
		display: block;
	}
	
	.field-input {
		width: 100%;
		height: 40px;
		padding: 0 12px;
		border: 1px solid #ddd;
		border-radius: 6px;
		font-size: 14px;
		box-sizing: border-box;
	}
	
	.field-input:focus {
		border-color: #d32f2f;
		outline: none;
	}
	
	
	
	/* 弹窗按钮样式 */
	.popup-btns {
		display: flex;
		gap: 12px;
		margin-top: 20px;
		padding: 0 20px 20px 20px;
	}
	
	.popup-btn {
		flex: 1;
		height: 44px;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 8px;
		font-size: 16px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s;
	}
	
	.popup-btn.cancel {
		background-color: #f8f9fa;
		color: #6c757d;
		border: 1px solid #dee2e6;
	}
	
	.popup-btn.cancel:hover {
		background-color: #e9ecef;
	}
	
	.popup-btn.confirm {
		background-color: #007aff;
		color: #fff;
		border: 1px solid #007aff;
	}
	
	.popup-btn.confirm:hover {
		background-color: #0056b3;
	}
	
	/* 文字编辑弹窗专用样式 */
	.edit-form-container {
		padding: 0 16px 16px 16px;
		max-width: 500px;
		margin: 0 auto;
	}
	
	.element-actions {
		display: flex;
		justify-content: center;
		gap: 16px;
		margin-bottom: 24px;
		padding: 16px;
		background-color: #f8f9fa;
		border-radius: 12px;
		border: 1px solid #e9ecef;
	}
	
	.action-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 12px 20px;
		border-radius: 8px;
		cursor: pointer;
		transition: all 0.3s ease;
		min-width: 80px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}
	
	.edit-btn {
		background: linear-gradient(135deg, #007aff 0%, #0056b3 100%);
		color: white;
	}
	
	.edit-btn:hover {
		background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
		transform: translateY(-2px);
		box-shadow: 0 4px 8px rgba(0, 122, 255, 0.3);
	}
	
	.delete-btn {
		background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
		color: white;
	}
	
	.delete-btn:hover {
		background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
		transform: translateY(-2px);
		box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
	}
	
	.action-icon {
		font-size: 20px;
		margin-bottom: 4px;
	}
	
	.action-text {
		font-size: 12px;
		font-weight: 500;
		text-align: center;
	} 
	
	.edit-form {
		padding: 0 16px;
	}
	
	.property-row {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 12px 0;
		border-bottom: 1px solid #f5f5f5;
	}
	
	.property-row:last-child {
		border-bottom: none;
	}
	
	.property-label {
		font-size: 14px;
		color: #333;
		font-weight: 500;
		min-width: 80px;
	}
	
	.property-picker {
		flex: 1;
		margin-left: 12px;
		cursor: pointer;
	}
	
	.text-input {
		flex: 1;
		margin-left: 12px;
		padding: 8px 12px;
		border: 1px solid #e0e0e0;
		border-radius: 6px;
		background-color: #fff;
		font-size: 14px;
		color: #333;
		outline: none;
	}
	
	.text-input:focus {
		border-color: #007aff;
		box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);
	}
	
	.picker-value {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 8px 12px;
		border: 1px solid #e0e0e0;
		border-radius: 6px;
		background-color: #fafafa;
		font-size: 14px;
		color: #666;
	}
	
	.picker-arrow {
		font-size: 12px;
		color: #999;
	} 
	.color-preview-small {
		width: 20px;
		height: 20px;
		border-radius: 4px;
		border: 1px solid #ddd;
		flex-shrink: 0;
	}
	
	.color-name {
		flex: 1;
		font-size: 14px;
		color: #666;
		margin-left: 8px;
	}
	
	.slider-container {
		flex: 1;
		margin-left: 12px;
		padding: 0 8px;
	}
	
	.style-text {
		font-size: 14px;
		font-weight: bold;
		color: inherit;
	} 

	.preview-image-container {
		width: 300px;
		height: 200px;
		border: 1px solid #ddd;
		border-radius: 6px;
		overflow: hidden;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 auto 20px;
	}
	
	.preview-image {
		width: 100%;
		height: 100%;
	}
	
	.preview-loading, .preview-error {
		color: #666;
		font-size: 14px;
	}
	

</style>
