@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  flex: 1;
  background-color: #f5f5f5;
  background-image: url(/static/img/bg.6fc1f75f.svg);
  background-size: cover;
  background-position: center;
}
.navbar {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 40rpx;
  padding-top: calc(20rpx + 25px);
  background-color: transparent;
}
.left-capsule {
  width: 80rpx;
}
.capsule-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: rgba(255, 255, 255, 0.8);
  justify-content: center;
  align-items: center;
}
.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
}
.right-capsule {
  width: 80rpx;
}
.content {
  padding: 30rpx;
  flex: 1;
}
/* 屏幕模式选择标签 */
.screen-mode-tabs {
  background-color: #8B0000;
  /* 深红色背景 */
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
}
.tab-row {
  display: flex;
  flex-direction: row;
  width: 100%;
}
.tab-item {
  height: 80rpx;
  justify-content: center;
  align-items: center;
  display: flex;
}
.tab-item text {
  color: #ffffff;
  font-size: 28rpx;
}
.tab-item.active {
  background-color: #FF0000;
  /* 鲜红色 */
}
/* 预览区域 */
.preview-area {
  margin-bottom: 30rpx;
}
.preview-card {
  background-color: #ffffff;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.template-preview {
  padding: 20rpx;
  align-items: center;
  justify-content: center;
}
/* 前后屏不同模式的卡片布局 */
.preview-cards {
  display: flex;
  flex-direction: column;
}
/* 中国风模板样式 */
.chinese-style-template {
  width: 100%;
  height: 400rpx;
  position: relative;
  border: 2rpx solid #000000;
  border-radius: 8rpx;
  overflow: hidden;
  background-color: #ffffff;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
/* 前后屏标签 */
.screen-tag {
  position: absolute;
  top: 0;
  left: 0;
  width: 60rpx;
  height: 60rpx;
  background-color: #8B0000;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  font-weight: bold;
  border-bottom-right-radius: 8rpx;
}
.template-content {
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8rpx;
  margin: 20rpx;
}
/* 姓名和职称容器 */
.name-title-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}
.for-front-screen {
  width: 100%;
  /* 或指定具体的宽度 */
  height: auto;
  /* 保持图片的原始比例 */
  object-fit: cover;
  /* 覆盖容器，可能会裁剪图片 */
}
.content-divider {
  font-size: 60rpx;
  font-weight: bold;
  margin: 0 20rpx;
}
.content-title {
  font-size: 60rpx;
  font-weight: bold;
  text-shadow: 1rpx 1rpx 3rpx rgba(0, 0, 0, 0.3);
}
.company-name {
  font-size: 36rpx;
  color: #FF0000;
  background: linear-gradient(to right, #FF0000, #000000);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  padding: 10rpx 0;
  text-shadow: 0 0 2rpx rgba(255, 255, 255, 0.5);
}
.company-name-small {
  font-size: 28rpx;
  color: #333;
  margin-top: 10rpx;
  font-weight: bold;
  text-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.8);
}
.content-subtitle {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  text-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.8);
}
/* 底部操作按钮 */
.operation-buttons {
  margin-bottom: 30rpx;
  background-color: #ffffff;
  padding: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  position: relative;
}
.op-button-row {
  display: flex;
  flex-direction: row;
  width: 100%;
  position: relative;
}
/* 删除button-divider样式 */
.bottom-gradient-line {
  height: 4rpx;
  width: 100%;
  background: linear-gradient(to right, #8B0000, #000000);
  position: absolute;
  bottom: 0;
  left: 0;
  border-bottom-left-radius: 16rpx;
  border-bottom-right-radius: 16rpx;
}
.op-button {
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
}
.op-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}
.op-text {
  font-size: 28rpx;
  color: #333;
}
/* 底部确认按钮 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: transparent;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: center;
}
.confirm-button {
  background-color: #8B0000;
  /* 深红色 */
  height: 90rpx;
  border-radius: 45rpx;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 10rpx rgba(139, 0, 0, 0.3);
  display: flex;
  width: 80%;
}
.confirm-button text {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
  align-items: center;
}
/* 弹窗样式 */
.template-popup, .editor-popup {
  padding: 30rpx;
  background-color: #ffffff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
}
.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  text-align: center;
}
.template-scroll {
  width: 100%;
  height: 300rpx;
}
.template-list {
  flex-direction: row;
  padding: 10rpx 0;
}
.template-item {
  width: 200rpx;
  margin-right: 20rpx;
  align-items: center;
}
.template-image {
  width: 180rpx;
  height: 240rpx;
  border-radius: 8rpx;
  border: 2rpx solid #eee;
}
.template-item.active .template-image {
  border: 2rpx solid #8B0000;
}
.template-name {
  font-size: 24rpx;
  margin-top: 10rpx;
  text-align: center;
}
.edit-form {
  margin-top: 20rpx;
  margin-bottom: 30rpx;
}
.form-item {
  margin-bottom: 20rpx;
}
.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.form-input {
  height: 80rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}
.popup-button {
  background-color: #8B0000;
  /* 深红色 */
  height: 80rpx;
  border-radius: 40rpx;
  justify-content: center;
  align-items: center;
}
.popup-button text {
  color: #ffffff;
  font-size: 30rpx;
  font-weight: bold;
}
