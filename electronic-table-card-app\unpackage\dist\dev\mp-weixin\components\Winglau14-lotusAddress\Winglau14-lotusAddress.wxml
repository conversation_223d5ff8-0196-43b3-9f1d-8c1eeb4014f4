<block wx:if="{{lotusAddressData.visible}}"><view class="lotus-address-mask" status="{{checkStatus}}"><view class="{{[lotusAddressData.visible?'lotus-address-box':'lotus-address-box lotus-address-box-out']}}"><view class="lotus-address-action"><text data-event-opts="{{[['tap',[['cancelPicker',['$event']]]]]}}" class="lotus-address-action-cancel" bindtap="__e">取消</text><text data-event-opts="{{[['tap',[['chosedVal',['$event']]]]]}}" class="lotus-address-action-affirm" bindtap="__e">确认</text></view><view class="lotus-address-picker-box"><scroll-view class="lotus-address-picker-box-item" scroll-y="{{true}}" scroll-into-view="{{'pid'+pChoseIndex}}"><block wx:for="{{province}}" wx:for-item="pItem" wx:for-index="pIndex" wx:key="pIndex"><view class="{{[pIndex===pChoseIndex?'lotus-address-picker lotus-address-picker2':'lotus-address-picker']}}" id="{{'pid'+pIndex}}" data-event-opts="{{[['tap',[['clickPicker',[0,pIndex,'$0'],[[['province','',pIndex]]]]]]]}}" bindtap="__e">{{pItem}}</view></block></scroll-view><scroll-view class="lotus-address-picker-box-item" scroll-y="{{true}}" scroll-into-view="{{'cid'+cChoseIndex}}"><block wx:for="{{city}}" wx:for-item="cItem" wx:for-index="cIndex" wx:key="cIndex"><view class="{{[cIndex===cChoseIndex?'lotus-address-picker lotus-address-picker2':'lotus-address-picker']}}" id="{{'cid'+cIndex}}" data-event-opts="{{[['tap',[['clickPicker',[1,cIndex,'$0'],[[['city','',cIndex]]]]]]]}}" bindtap="__e">{{cItem}}</view></block></scroll-view><scroll-view class="lotus-address-picker-box-item" scroll-y="{{true}}" scroll-into-view="{{'tid'+tChoseIndex}}"><block wx:for="{{town}}" wx:for-item="tItem" wx:for-index="tIndex" wx:key="tIndex"><view class="{{[tIndex===tChoseIndex?'lotus-address-picker lotus-address-picker2':'lotus-address-picker']}}" id="{{'tid'+tIndex}}" data-event-opts="{{[['tap',[['clickPicker',[2,tIndex,'$0'],[[['town','',tIndex]]]]]]]}}" bindtap="__e">{{tItem}}</view></block></scroll-view></view></view></view></block>