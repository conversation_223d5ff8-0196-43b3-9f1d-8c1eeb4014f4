
.background-list-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
}

/* 导航栏样式 */
.navbar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 90rpx;
	padding: 0 30rpx;
	background-color: #FFFFFF;
	border-bottom: 1px solid #f0f0f0;
}
.navbar-left {
	display: flex;
	align-items: center;
}
.navbar-title {
	font-size: 28rpx;
	color: #333;
	margin-left: 8rpx;
}
.navbar-center {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.navbar-right {
	width: 60rpx;
}
.tab-section {
	background-color: #FFFFFF;
}
.content-section {
	flex: 1;
	padding: 20rpx;
	overflow-y: auto;
}
.empty-list {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 300rpx;
}
.empty-text {
	color: #999;
	font-size: 28rpx;
}
.background-grid {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}
.background-item {
	display: flex;
	align-items: center;
	background-color: #FFFFFF;
	border-radius: 10rpx;
	padding: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	position: relative;
}
.background-preview {
	width: 120rpx;
	height: 120rpx;
	border-radius: 8rpx;
	overflow: hidden;
	margin-right: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f8f8f8;
}
.background-image {
	max-width: 100%;
	max-height: 100%;
	width: auto;
	height: auto;
}
.background-info {
	flex: 1;
}
.background-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}
.background-type {
	font-size: 24rpx;
	color: #666;
	background-color: #f0f0f0;
	display: inline-block;
	padding: 4rpx 16rpx;
	border-radius: 20rpx;
}
.delete-icon, .select-icon {
	padding: 16rpx;
}
.selectable {
	cursor: pointer;
	transition: all 0.2s;
}
.selectable:hover, .selectable:active {
	background-color: #f5f5f5;
	-webkit-transform: translateY(-2rpx);
	        transform: translateY(-2rpx);
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.add-button {
	position: fixed;
	right: 40rpx;
	bottom: 40rpx;
	width: 100rpx;
	height: 100rpx;
	background-color: #8B1538;
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	box-shadow: 0 4rpx 16rpx rgba(139, 21, 56, 0.3);
	z-index: 10;
}

/* 弹窗样式 */
.add-popup-content {
	width: 600rpx;
	padding: 30rpx;
}
.popup-title {
	font-size: 32rpx;
	font-weight: bold;
	text-align: center;
	margin-bottom: 30rpx;
	color: #333;
}
.form-item {
	margin-bottom: 30rpx;
}
.form-label {
	display: block;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 16rpx;
}
.form-input {
	width: 100%;
	height: 80rpx;
	border: 1px solid #ddd;
	border-radius: 8rpx;
	padding: 0 20rpx;
	box-sizing: border-box;
	font-size: 28rpx;
}
.form-picker {
	width: 100%;
	height: 80rpx;
	border: 1px solid #ddd;
	border-radius: 8rpx;
	font-size: 28rpx;
}
.picker-content {
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 20rpx;
}
.popup-actions {
	display: flex;
	justify-content: space-between;
	margin-top: 40rpx;
}
.popup-btn {
	width: 45%;
	height: 80rpx;
	border-radius: 8rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 28rpx;
}
.cancel {
	background-color: #f5f5f5;
	color: #666;
}
.confirm {
	background-color: #8B1538;
	color: #fff;
}

