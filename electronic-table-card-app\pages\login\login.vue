<template>
	<view class="container"> 
		<view class="logo-container"> 
			<image src="http://*************:8890/i/2025/06/12/logo.png" class="logo" mode="aspectFit"></image> 
		</view>
		<view class="button-group">
			<button class="login-button wechat-login">微信快捷登录</button>
			<button class="login-button other-login" @click="goToAccountLogin">其他账号登录/注册</button>
			<button class="login-button guest-login" @click="goToAbout">暂不登录</button>
		</view>  
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			goToAccountLogin() {
				uni.navigateTo({
					url: '/pages/user/account-login'
				});
			},
			goToAbout() {
				uni.navigateTo({
					url: '/pages/user/about'
				});
			}
		}
	}
</script>

<style scoped>
.container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: space-between;
	height: 100vh;
	background-image: url('http://*************:8890/i/2025/06/12/login-bg.png');
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;
	padding: 0 20px;
	box-sizing: border-box;
	overflow: hidden;
}

.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	padding-top: var(--status-bar-height);
	height: 44px; /* Standard navbar height */
	margin-bottom: 20px;
}

.home-icon {
	color: #000 !important; /* Ensure icon is visible */
}

.title {
	font-size: 18px;
	font-weight: bold;
	color: #333;
}

.header-right-placeholder {
	width: 24px; /* Match icon size for balance */
}

.logo-container {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 40px;
	margin-top: 20px;
}

.logo {
	width: 150px;
	height: 150px;
	/* The actual logo image needs to be placed in /static/logo.png */
	/* For now, using a placeholder background */
	/* background-color: #f0f0f0; */ 
	/* border-radius: 50%; */
}

.button-group {
	display: flex;
	flex-direction: column;
	width: 100%;
	max-width: 300px;
	gap: 15px;
	margin-bottom: auto; /* Pushes footer down */
}

.login-button {
	width: 100%;
	height: 48px;
	line-height: 48px;
	border-radius: 8px;
	font-size: 16px;
	text-align: center;
	color: #fff;
	border: none;
}

.wechat-login {
	background-color: #5A90F8;
}

.other-login {
	background-color: #FFFFFF;
	color: #5A90F8;
	border: 1px solid #5A90F8;
}

.guest-login {
	background-color: transparent;
	color: #5A90F8;
	font-size: 14px;
	height: auto;
	line-height: normal;
	padding: 10px 0;
	border: none;
}

.footer {
	width: 100%;
	text-align: center;
	padding: 20px 0;
	font-size: 14px;
	color: #666;
}
</style>
