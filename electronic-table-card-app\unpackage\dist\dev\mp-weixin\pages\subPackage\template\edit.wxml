<view class="template-editor data-v-6c6302e2"><view class="toolbar data-v-6c6302e2"><view class="toolbar-left data-v-6c6302e2"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="back-btn data-v-6c6302e2" bindtap="__e"><text class="icon data-v-6c6302e2">←</text></view><input class="template-name-input data-v-6c6302e2" placeholder="输入模板名称" maxlength="20" data-event-opts="{{[['input',[['__set_model',['','templateName','$event',[]]]]]]}}" value="{{templateName}}" bindinput="__e"/></view><view class="toolbar-right data-v-6c6302e2"><view class="tool-btn data-v-6c6302e2" disabled="{{!hasElements}}" data-event-opts="{{[['tap',[['openPreview',['$event']]]]]}}" bindtap="__e"><text class="icon data-v-6c6302e2">👁</text><text class="text data-v-6c6302e2">预览</text></view><view class="tool-btn primary data-v-6c6302e2" disabled="{{!canSave}}" data-event-opts="{{[['tap',[['saveTemplate',['$event']]]]]}}" bindtap="__e"><text class="icon data-v-6c6302e2">💾</text><text class="text data-v-6c6302e2">保存</text></view></view></view><view class="canvas-container data-v-6c6302e2" style="{{(canvasContainerStyle)}}"><view class="canvas-wrapper data-v-6c6302e2"><canvas class="skyline-canvas data-v-6c6302e2" style="{{(canvasStyle)}}" type="2d" canvas-id="skylineCanvas" data-event-opts="{{[['touchstart',[['handleTouchStart',['$event']]]],['touchmove',[['handleTouchMove',['$event']]]],['touchend',[['handleTouchEnd',['$event']]]],['tap',[['handleCanvasTap',['$event']]]]]}}" bindtouchstart="__e" bindtouchmove="__e" bindtouchend="__e" bindtap="__e"></canvas><block wx:if="{{!hasElements&&!insertType}}"><view class="canvas-hint data-v-6c6302e2"><text class="hint-text data-v-6c6302e2">选择下方工具开始创建模板</text></view></block></view></view><view class="toolbar-container data-v-6c6302e2" style="{{(toolbarContainerStyle)}}"><view class="tool-section data-v-6c6302e2"><view class="section-title data-v-6c6302e2"><text class="title data-v-6c6302e2">插入元素</text></view><view class="tool-buttons data-v-6c6302e2"><block wx:for="{{insertTools}}" wx:for-item="tool" wx:for-index="__i0__" wx:key="type"><view data-event-opts="{{[['tap',[['setInsertType',['$0'],[[['insertTools','type',tool.type,'type']]]]]]]}}" class="{{['tool-button','data-v-6c6302e2',(insertType===tool.type)?'active':'']}}" bindtap="__e"><text class="tool-icon data-v-6c6302e2">{{tool.icon}}</text><text class="tool-label data-v-6c6302e2">{{tool.label}}</text></view></block></view></view><view class="tool-section data-v-6c6302e2"><view class="section-title data-v-6c6302e2"><text class="title data-v-6c6302e2">背景设置</text></view><view class="tool-buttons data-v-6c6302e2"><block wx:for="{{backgroundTools}}" wx:for-item="bg" wx:for-index="__i1__" wx:key="type"><view data-event-opts="{{[['tap',[['setBackgroundType',['$0'],[[['backgroundTools','type',bg.type,'type']]]]]]]}}" class="{{['tool-button','small','data-v-6c6302e2',(backgroundType===bg.type)?'active':'']}}" bindtap="__e"><text class="tool-icon data-v-6c6302e2">{{bg.icon}}</text><text class="tool-label data-v-6c6302e2">{{bg.label}}</text></view></block></view></view><block wx:if="{{selectedElement}}"><view class="tool-section data-v-6c6302e2"><view class="section-title data-v-6c6302e2"><text class="title data-v-6c6302e2">属性设置</text></view><view class="property-controls data-v-6c6302e2"><view class="property-item data-v-6c6302e2"><text class="label data-v-6c6302e2">字号</text><view class="number-control data-v-6c6302e2"><view data-event-opts="{{[['tap',[['adjustFontSize',[-2]]]]]}}" class="control-btn data-v-6c6302e2" bindtap="__e">-</view><input class="number-input data-v-6c6302e2" type="number" data-event-opts="{{[['input',[['__set_model',['','currentFontSize','$event',['number']]],['updateFontSize',['$event']]]],['blur',[['$forceUpdate']]]]}}" value="{{currentFontSize}}" bindinput="__e" bindblur="__e"/><view data-event-opts="{{[['tap',[['adjustFontSize',[2]]]]]}}" class="control-btn data-v-6c6302e2" bindtap="__e">+</view></view></view><view class="property-item data-v-6c6302e2"><text class="label data-v-6c6302e2">颜色</text><view class="color-options data-v-6c6302e2"><block wx:for="{{colorOptions}}" wx:for-item="color" wx:for-index="__i2__" wx:key="*this"><view data-event-opts="{{[['tap',[['selectColor',['$0'],[[['colorOptions','',__i2__]]]]]]]}}" class="{{['color-item','data-v-6c6302e2',(currentTextColor===color)?'active':'']}}" style="{{'background-color:'+(color)+';'}}" bindtap="__e"></view></block></view></view><view class="element-actions data-v-6c6302e2"><block wx:if="{{selectedElement.type==='text'}}"><view data-event-opts="{{[['tap',[['editElementText',['$event']]]]]}}" class="action-btn data-v-6c6302e2" bindtap="__e"><text class="icon data-v-6c6302e2">✏️</text><text class="text data-v-6c6302e2">编辑</text></view></block><view data-event-opts="{{[['tap',[['duplicateElement',['$event']]]]]}}" class="action-btn data-v-6c6302e2" bindtap="__e"><text class="icon data-v-6c6302e2">📋</text><text class="text data-v-6c6302e2">复制</text></view><view data-event-opts="{{[['tap',[['deleteElement',['$event']]]]]}}" class="action-btn danger data-v-6c6302e2" bindtap="__e"><text class="icon data-v-6c6302e2">🗑️</text><text class="text data-v-6c6302e2">删除</text></view></view></view></view></block><view class="tool-section data-v-6c6302e2"><view class="section-title data-v-6c6302e2"><text class="title data-v-6c6302e2">快捷操作</text></view><view class="quick-actions data-v-6c6302e2"><view class="action-btn data-v-6c6302e2" disabled="{{!hasElements}}" data-event-opts="{{[['tap',[['centerAllElements',['$event']]]]]}}" bindtap="__e"><text class="icon data-v-6c6302e2">⚡</text><text class="text data-v-6c6302e2">居中对齐</text></view><view class="action-btn danger data-v-6c6302e2" disabled="{{!hasElements}}" data-event-opts="{{[['tap',[['clearAllElements',['$event']]]]]}}" bindtap="__e"><text class="icon data-v-6c6302e2">🗑</text><text class="text data-v-6c6302e2">清空画布</text></view></view></view></view><u-popup vue-id="7001d457-1" show="{{showEditTextPopup}}" mode="center" round="{{16}}" closeable="{{true}}" data-event-opts="{{[['^close',[['closeEditTextPopup']]]]}}" bind:close="__e" class="data-v-6c6302e2" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-content data-v-6c6302e2"><view class="popup-header data-v-6c6302e2"><text class="popup-title data-v-6c6302e2">编辑文字内容</text></view><view class="popup-body data-v-6c6302e2"><textarea class="text-input data-v-6c6302e2" placeholder="请输入文字内容" maxlength="100" auto-height="{{true}}" data-event-opts="{{[['input',[['__set_model',['','editingText','$event',[]]]]]]}}" value="{{editingText}}" bindinput="__e"></textarea></view><view class="popup-footer data-v-6c6302e2"><view data-event-opts="{{[['tap',[['closeEditTextPopup',['$event']]]]]}}" class="popup-btn secondary data-v-6c6302e2" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['confirmEditText',['$event']]]]]}}" class="popup-btn primary data-v-6c6302e2" bindtap="__e">确认</view></view></view></u-popup></view>