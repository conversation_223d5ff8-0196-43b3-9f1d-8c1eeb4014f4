<view class="container"><view class="content"><view class="meeting-list"><block wx:for="{{meetingRooms}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="meeting-item"><view class="meeting-header"><image class="header-icon" src="/static/images/logo_icon_text.png" mode="aspectFit"></image><text class="meeting-name-header">{{item.name}}</text></view><view class="meeting-body"><view class="meeting-icon-container"><view class="{{['status-dot-outer',(item.status==='active')?'active-outer':'']}}"><view class="{{['status-dot-inner',(item.status==='active')?'active':'']}}"></view></view><view class="main-icon-bg"><image class="main-icon" src="/static/images/logo_icon.png" mode="aspectFit"></image></view></view><view class="meeting-details"><view class="spec-row"><text class="spec-label">型号：</text><text class="spec-value">{{item.model||'未绑定'}}</text></view><view class="spec-row"><text class="spec-label">版本：</text><text class="spec-value">{{item.version||'未绑定'}}</text></view><view class="spec-row"><text class="spec-label">类型：</text><text class="spec-value">{{item.type||'未绑定'}}</text></view><view class="spec-row"><text class="spec-label">编号：</text><text class="spec-value">{{item.code||'未绑定'}}</text></view><view class="spec-row"><text class="spec-label">频道：</text><text class="spec-value">{{item.channel||'未绑定'}}</text></view></view></view><view class="meeting-actions"><view data-event-opts="{{[['tap',[['unbindDevice',['$0'],[[['meetingRooms','',index]]]]]]]}}" class="action-button unbind" bindtap="__e"><image class="action-icon" src="/static/images/gateway_unbinding.png" mode="aspectFit"></image><text>未绑定</text></view><view data-event-opts="{{[['tap',[['showBindModal',['$0'],[[['meetingRooms','',index]]]]]]]}}" class="action-button bind" bindtap="__e"><text>绑定</text></view><view data-event-opts="{{[['tap',[['deleteRoom',['$0'],[[['meetingRooms','',index]]]]]]]}}" class="action-button delete" bindtap="__e"><text>删除</text></view><view data-event-opts="{{[['tap',[['editRoom',['$0'],[[['meetingRooms','',index]]]]]]]}}" class="action-button edit" bindtap="__e"><text>编辑</text></view></view></view></block></view></view><view data-event-opts="{{[['tap',[['addRoom',['$event']]]]]}}" class="add-button" bindtap="__e"><text class="add-icon">+</text></view><view class="confirm-button-container"><view class="confirm-button-wrapper"><image class="confirm-button-bg" src="http://14.103.146.84:8890/i/2025/06/12/btn.png" mode="aspectFill"></image><view data-event-opts="{{[['tap',[['confirmSelection',['$event']]]]]}}" class="confirm-button" bindtap="__e"><text>确定</text></view></view></view><block wx:if="{{showBindPopup}}"><view data-event-opts="{{[['tap',[['hideBindModal',['$event']]]]]}}" class="bind-modal-overlay" bindtap="__e"><view data-event-opts="{{[['tap',[['stopPropagation',['$event']]]]]}}" class="{{['bind-modal',(bindModalVisible)?'show':'']}}" catchtap="__e"><view class="bind-modal-header"><text class="bind-modal-title">绑定设备</text><view data-event-opts="{{[['tap',[['hideBindModal',['$event']]]]]}}" class="bind-modal-close" bindtap="__e"><text>×</text></view></view><view class="bind-modal-content"><view class="bind-info"><text class="bind-info-title">{{currentBindItem&&currentBindItem.name}}</text><text class="bind-info-desc">请选择要绑定的网关设备</text></view><view class="gateway-list"><view class="gateway-header"><text class="header-cell">选择</text><text class="header-cell">网关名称</text><text class="header-cell">网关MAC</text></view><block wx:for="{{gatewayList}}" wx:for-item="gateway" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectGateway',['$0'],[[['gatewayList','',index]]]]]]]}}" class="gateway-item" bindtap="__e"><view class="gateway-cell radio-cell"><radio checked="{{selectedGateway&&selectedGateway.id===gateway.id}}" color="#8B0000"></radio></view><view class="gateway-cell name-cell"><text>{{gateway.name}}</text></view><view class="gateway-cell mac-cell"><text>{{gateway.mac}}</text></view></view></block></view><view class="bind-actions"><view data-event-opts="{{[['tap',[['hideBindModal',['$event']]]]]}}" class="bind-action-btn cancel" bindtap="__e"><text>取消</text></view><view data-event-opts="{{[['tap',[['confirmBind',['$event']]]]]}}" class="bind-action-btn confirm" bindtap="__e"><text>确认绑定</text></view></view></view></view></view></block></view>