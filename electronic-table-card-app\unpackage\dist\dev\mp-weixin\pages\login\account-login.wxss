
.container.data-v-e2ef718c {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: space-between;
	height: 100vh;
	background-image: url('http://14.103.146.84:8890/i/2025/06/12/login-bg.png');
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;
	padding: 0 20px;
	box-sizing: border-box;
	overflow: hidden;
}
.logo-container.data-v-e2ef718c {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 60px;
	margin-bottom: 50px;
}
.logo.data-v-e2ef718c {
	width: 120px;
	height: 120px;
}
.form-container.data-v-e2ef718c {
	flex: 1;
	width: 100%;
	max-width: 350px;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.input-group.data-v-e2ef718c {
	width: 100%;
	margin-bottom: 20px;
}
.login-btn-container.data-v-e2ef718c {
	width: 100%;
	margin-top: 40px;
	margin-bottom: 30px;
}
.login-btn.data-v-e2ef718c {
	width: 100%;
	height: 52px;
	background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
	border-radius: 26px;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	overflow: hidden;
	box-shadow: 0 4px 16px rgba(139, 69, 19, 0.3);
	border: none;
	transition: all 0.3s ease;
}
.login-btn.data-v-e2ef718c:active {
	-webkit-transform: translateY(2px);
	        transform: translateY(2px);
	box-shadow: 0 2px 8px rgba(139, 69, 19, 0.4);
}
.login-text.data-v-e2ef718c {
	color: #FFFFFF;
	font-size: 18px;
	font-weight: 600;
	letter-spacing: 2px;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
	position: relative;
	z-index: 10;
}
.forgot-password.data-v-e2ef718c {
	align-self: center;
	padding: 10px;
	margin-top: 10px;
}
.forgot-text.data-v-e2ef718c {
	color: #8B4513;
	font-size: 16px;
	font-weight: 500;
	text-decoration: underline;
	-webkit-text-decoration-color: rgba(139, 69, 19, 0.5);
	        text-decoration-color: rgba(139, 69, 19, 0.5);
}
.register-container.data-v-e2ef718c {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 14px;
}
.register-text.data-v-e2ef718c {
	color: #333;
	font-size:  48rpx;
	font-weight: 500;
}
.register-link.data-v-e2ef718c {
	color: #8B0000;
	font-size: 48rpx;
	font-weight: bold;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

