{"version": 3, "sources": ["webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/uni_modules/uview-ui/components/u-icon/u-icon.vue?3362", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/uni_modules/uview-ui/components/u-icon/u-icon.vue?e441", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/uni_modules/uview-ui/components/u-icon/u-icon.vue?7394", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/uni_modules/uview-ui/components/u-icon/u-icon.vue?c6de", "uni-app:///uni_modules/uview-ui/components/u-icon/u-icon.vue", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/uni_modules/uview-ui/components/u-icon/u-icon.vue?8723", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/uni_modules/uview-ui/components/u-icon/u-icon.vue?1cde"], "names": ["name", "data", "mixins", "computed", "uClasses", "classes", "iconStyle", "style", "fontSize", "lineHeight", "fontWeight", "top", "isImg", "imgStyle", "icon", "methods", "clickHandler"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAquB,CAAgB,srBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACiDzvB;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAHA;;AAGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzBA,eA0BA;EACAA;EACAC;IACA,QAEA;EACA;EACAC;EACAC;IACAC;MACA;MACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAIA;IACA;IACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACA;QACAC;MACA;MACA;MACA;MAEA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAN;MACAA;MACA;IACA;IACA;IACAO;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChJA;AAAA;AAAA;AAAA;AAAg5C,CAAgB,qvCAAG,EAAC,C;;;;;;;;;;;ACAp6C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-icon/u-icon.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-icon.vue?vue&type=template&id=2ee87dc9&scoped=true&\"\nvar renderjs\nimport script from \"./u-icon.vue?vue&type=script&lang=js&\"\nexport * from \"./u-icon.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-icon.vue?vue&type=style&index=0&id=2ee87dc9&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2ee87dc9\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-icon/u-icon.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-icon.vue?vue&type=template&id=2ee87dc9&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.isImg\n    ? _vm.__get_style([_vm.imgStyle, _vm.$u.addStyle(_vm.customStyle)])\n    : null\n  var s1 = !_vm.isImg\n    ? _vm.__get_style([_vm.iconStyle, _vm.$u.addStyle(_vm.customStyle)])\n    : null\n  var g0 = _vm.label !== \"\" ? _vm.$u.addUnit(_vm.labelSize) : null\n  var g1 =\n    _vm.label !== \"\" && _vm.labelPos == \"right\"\n      ? _vm.$u.addUnit(_vm.space)\n      : null\n  var g2 =\n    _vm.label !== \"\" && _vm.labelPos == \"bottom\"\n      ? _vm.$u.addUnit(_vm.space)\n      : null\n  var g3 =\n    _vm.label !== \"\" && _vm.labelPos == \"left\"\n      ? _vm.$u.addUnit(_vm.space)\n      : null\n  var g4 =\n    _vm.label !== \"\" && _vm.labelPos == \"top\" ? _vm.$u.addUnit(_vm.space) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-icon.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-icon.vue?vue&type=script&lang=js&\"", "<template>\n\t<view\n\t    class=\"u-icon\"\n\t    @tap=\"clickHandler\"\n\t    :class=\"['u-icon--' + labelPos]\"\n\t>\n\t\t<image\n\t\t    class=\"u-icon__img\"\n\t\t    v-if=\"isImg\"\n\t\t    :src=\"name\"\n\t\t    :mode=\"imgMode\"\n\t\t    :style=\"[imgStyle, $u.addStyle(customStyle)]\"\n\t\t></image>\n\t\t<text\n\t\t    v-else\n\t\t    class=\"u-icon__icon\"\n\t\t    :class=\"uClasses\"\n\t\t    :style=\"[iconStyle, $u.addStyle(customStyle)]\"\n\t\t    :hover-class=\"hoverClass\"\n\t\t>{{icon}}</text>\n\t\t<!-- 这里进行空字符串判断，如果仅仅是v-if=\"label\"，可能会出现传递0的时候，结果也无法显示 -->\n\t\t<text\n\t\t    v-if=\"label !== ''\" \n\t\t    class=\"u-icon__label\"\n\t\t    :style=\"{\n\t\t\tcolor: labelColor,\n\t\t\tfontSize: $u.addUnit(labelSize),\n\t\t\tmarginLeft: labelPos == 'right' ? $u.addUnit(space) : 0,\n\t\t\tmarginTop: labelPos == 'bottom' ? $u.addUnit(space) : 0,\n\t\t\tmarginRight: labelPos == 'left' ? $u.addUnit(space) : 0,\n\t\t\tmarginBottom: labelPos == 'top' ? $u.addUnit(space) : 0,\n\t\t}\"\n\t\t>{{ label }}</text>\n\t</view>\n</template>\n\n<script>\n\t// #ifdef APP-NVUE\n\t// nvue通过weex的dom模块引入字体，相关文档地址如下：\n\t// https://weex.apache.org/zh/docs/modules/dom.html#addrule\n\t//const fontUrl = 'https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf'\n\tconst domModule = weex.requireModule('dom')\n\tdomModule.addRule('fontFace', {\n\t\t'fontFamily': \"uicon-iconfont\",\n\t\t'src': `url('./font_2225171_8kdcwk4po24.ttf')`\n\t})\n\t// #endif\n\n\t// 引入图标名称，已经对应的unicode\n\timport icons from './icons'\n\t\n\timport props from './props.js';;\n\n\t/**\n\t * icon 图标\n\t * @description 基于字体的图标集，包含了大多数常见场景的图标。\n\t * @tutorial https://www.uviewui.com/components/icon.html\n\t * @property {String}\t\t\tname\t\t\t图标名称，见示例图标集\n\t * @property {String}\t\t\tcolor\t\t\t图标颜色,可接受主题色 （默认 color['u-content-color'] ）\n\t * @property {String | Number}\tsize\t\t\t图标字体大小，单位px （默认 '16px' ）\n\t * @property {Boolean}\t\t\tbold\t\t\t是否显示粗体 （默认 false ）\n\t * @property {String | Number}\tindex\t\t\t点击图标的时候传递事件出去的index（用于区分点击了哪一个）\n\t * @property {String}\t\t\thoverClass\t\t图标按下去的样式类，用法同uni的view组件的hoverClass参数，详情见官网\n\t * @property {String}\t\t\tcustomPrefix\t自定义扩展前缀，方便用户扩展自己的图标库 （默认 'uicon' ）\n\t * @property {String | Number}\tlabel\t\t\t图标右侧的label文字\n\t * @property {String}\t\t\tlabelPos\t\tlabel相对于图标的位置，只能right或bottom （默认 'right' ）\n\t * @property {String | Number}\tlabelSize\t\tlabel字体大小，单位px （默认 '15px' ）\n\t * @property {String}\t\t\tlabelColor\t\t图标右侧的label文字颜色 （ 默认 color['u-content-color'] ）\n\t * @property {String | Number}\tspace\t\t\tlabel与图标的距离，单位px （默认 '3px' ）\n\t * @property {String}\t\t\timgMode\t\t\t图片的mode\n\t * @property {String | Number}\twidth\t\t\t显示图片小图标时的宽度\n\t * @property {String | Number}\theight\t\t\t显示图片小图标时的高度\n\t * @property {String | Number}\ttop\t\t\t\t图标在垂直方向上的定位 用于解决某些情况下，让图标垂直居中的用途  （默认 0 ）\n\t * @property {Boolean}\t\t\tstop\t\t\t是否阻止事件传播 （默认 false ）\n\t * @property {Object}\t\t\tcustomStyle\t\ticon的样式，对象形式\n\t * @event {Function} click 点击图标时触发\n\t * @event {Function} touchstart 事件触摸时触发\n\t * @example <u-icon name=\"photo\" color=\"#2979ff\" size=\"28\"></u-icon>\n\t */\n\texport default {\n\t\tname: 'u-icon',\n\t\tdata() {\n\t\t\treturn {\n\n\t\t\t}\n\t\t},\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\n\t\tcomputed: {\n\t\t\tuClasses() {\n\t\t\t\tlet classes = []\n\t\t\t\tclasses.push(this.customPrefix + '-' + this.name)\n\t\t\t\t// // uView的自定义图标类名为u-iconfont\n\t\t\t\t// if (this.customPrefix == 'uicon') {\n\t\t\t\t// \tclasses.push('u-iconfont')\n\t\t\t\t// } else {\n\t\t\t\t// \tclasses.push(this.customPrefix)\n\t\t\t\t// }\n\t\t\t\t// 主题色，通过类配置\n\t\t\t\tif (this.color && uni.$u.config.type.includes(this.color)) classes.push('u-icon__icon--' + this.color)\n\t\t\t\t// 阿里，头条，百度小程序通过数组绑定类名时，无法直接使用[a, b, c]的形式，否则无法识别\n\t\t\t\t// 故需将其拆成一个字符串的形式，通过空格隔开各个类名\n\t\t\t\t//#ifdef MP-ALIPAY || MP-TOUTIAO || MP-BAIDU\n\t\t\t\tclasses = classes.join(' ')\n\t\t\t\t//#endif\n\t\t\t\treturn classes\n\t\t\t},\n\t\t\ticonStyle() {\n\t\t\t\tlet style = {}\n\t\t\t\tstyle = {\n\t\t\t\t\tfontSize: uni.$u.addUnit(this.size),\n\t\t\t\t\tlineHeight: uni.$u.addUnit(this.size),\n\t\t\t\t\tfontWeight: this.bold ? 'bold' : 'normal',\n\t\t\t\t\t// 某些特殊情况需要设置一个到顶部的距离，才能更好的垂直居中\n\t\t\t\t\ttop: uni.$u.addUnit(this.top)\n\t\t\t\t}\n\t\t\t\t// 非主题色值时，才当作颜色值\n\t\t\t\tif (this.color && !uni.$u.config.type.includes(this.color)) style.color = this.color\n\n\t\t\t\treturn style\n\t\t\t},\n\t\t\t// 判断传入的name属性，是否图片路径，只要带有\"/\"均认为是图片形式\n\t\t\tisImg() {\n\t\t\t\treturn this.name.indexOf('/') !== -1\n\t\t\t},\n\t\t\timgStyle() {\n\t\t\t\tlet style = {}\n\t\t\t\t// 如果设置width和height属性，则优先使用，否则使用size属性\n\t\t\t\tstyle.width = this.width ? uni.$u.addUnit(this.width) : uni.$u.addUnit(this.size)\n\t\t\t\tstyle.height = this.height ? uni.$u.addUnit(this.height) : uni.$u.addUnit(this.size)\n\t\t\t\treturn style\n\t\t\t},\n\t\t\t// 通过图标名，查找对应的图标\n\t\t\ticon() {\n\t\t\t\t// 如果内置的图标中找不到对应的图标，就直接返回name值，因为用户可能传入的是unicode代码\n\t\t\t\treturn icons['uicon-' + this.name] || this.name\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tclickHandler(e) {\n\t\t\t\tthis.$emit('click', this.index)\n\t\t\t\t// 是否阻止事件冒泡\n\t\t\t\tthis.stop && this.preventEvent(e)\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t// 变量定义\n\t$u-icon-primary: $u-primary !default;\n\t$u-icon-success: $u-success !default;\n\t$u-icon-info: $u-info !default;\n\t$u-icon-warning: $u-warning !default;\n\t$u-icon-error: $u-error !default;\n\t$u-icon-label-line-height:1 !default;\n\n\t/* #ifndef APP-NVUE */\n\t//2023.12.15图标转内网\n\t// 非nvue下加载字体\n\t@font-face {\n\t\tfont-family: 'uicon-iconfont';\n\t//\tsrc: url('./font_2225171_8kdcwk4po24.ttf') format('truetype');\n\t    src:url('data:font/ttf;charset=utf-8;base64,AAEAAAANAIAAAwBQRkZUTZZ6onMAAM1MAAAAHEdERUYAKQDcAADNLAAAAB5PUy8yPG5LfwAAAVgAAABWY21hcLSatyUAAANkAAACymdhc3D//wADAADNJAAAAAhnbHlmOIJP+QAAB+AAALeoaGVhZBuSvAAAAADcAAAANmhoZWEHrwOFAAABFAAAACRobXR4Rfk8iAAAAbAAAAG0bG9jYf2v0MAAAAYwAAABrm1heHAB/AGiAAABOAAAACBuYW1lKeYRVQAAv4gAAAKIcG9zdGOnneAAAMIQAAALEgABAAAAAQAAQ1/rel8PPPUACwQAAAAAANw6PAIAAAAA3Do8AgAu/7wD0QNDAAAACAACAAAAAAAAAAEAAAOA/4AAXAQAAAAAAAPRAAEAAAAAAAAAAAAAAAAAAAAEAAEAAADWAZYAHAAAAAAAAgAAAAoACgAAAP8AAAAAAAAAAQQAAZAABQAAAokCzAAAAI8CiQLMAAAB6wAyAQgAAAIABQMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUGZFZABA5gDpjQOA/4AAXAOAAIAAAAABAAAAAAAABAAAAAAAAAAEAAAABAAAhAB4AI0AeAB/ASUAbAB/APYApgDLALgAhABsASUAQwDJAIIAnAA8AKgANQCfAOAAQADbALIAPgCuAIgAgABBAIMAsAC/AJQAPQBAAC4AxQCnALgAtgB4AEEAQABBAM4AQQC3AJYAbwC0AEIAhAA+AD8AtwDpAK0AQgBAAD4AnAA8AIQAQABxAEAAqABAAHgA0wDrAEEArwB5AI0AvwCbALgAQQGxAEAAQABlAD4AlABxALcAtACwAK0AcABBALMA6QDCAOkAQABtAIgAbgC4AKAAwwC0AIcAWACDARgAtwEYAMMAyQBCAK4AgwCFAJEAgACrAEAAQACKAKgAqACpAKsAsQE0AD4AvwCrAKoBsQC0AMAAxwA/AIAAjwB2AD4AtgA/AEAA8gBAAI8AXABBAHQAPwCDAIcAQACJAEMAdgBAAF4AwgCBAbIAeAB6AD8AQwEmAKQAlwCkAJ0AZQCEAEEAjQBsANkAmgB9AJwAbAC4ALgA2QCNAD4AfwCHAKUAdwBlAEEAQQCQAJMBAgCRAJQAewB9AMwAzgC3ALUAvQC7ALIApQAAAAMAAAADAAAAHAABAAAAAAHEAAMAAQAAABwABAGoAAAAZgBAAAUAJuY55lnmX+Zr5n7mguaH5pfmnOah5qjmq+ay5rrmvObT5wTnCucV5yfnL+cz51HniOfS59fn3+fj5/boB+hR6Fbobuhw6HLoe+iB6IXoh+iK6K7oxOj66UjpVeln6XTpeumL6Y3//wAA5gDmO+Zb5mHmbeaA5oTmieaa5qDmpOaq5rDmuOa85tPnA+cJ5xXnJucv5zPnUeeI59Ln1+ff5+Pn9egH6FHoVehu6HDocuh66IHoheiH6InorujE6PrpSOlV6WTpc+l56Yvpjf//GgMaAhoBGgAZ/xn+Gf0Z/Bn6GfcZ9Rn0GfAZ6xnqGdQZpRmhGZcZhxmAGX0ZYBkqGOEY3RjWGNMYwhiyGGkYZhhPGE4YTRhGGEEYPhg9GDwYGRgEF88Xghd2F2gXXRdZF0kXSAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABBgAAAQAAAAAAAAABAgAAAAIAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAJgBcATYBbAGgAcYB6gIgAnAC1AMkA3oEHgREBGoEsgTyBTQFYgW4BeAGDgZ2BqoGwgdwB+AIHgiKCOAJOgo8Cr4LLAu6DCoMig2cDdIOYA64DxAPUg/kEB4QSBCaETARYBIMEnYS5BNWE4wUFhRcFJQVahWOFcoWFBZsFsQW/BdEF3gXyhhWGJwZThmgGg4akhroGyAbchu0G/gcPBx+HL4dEB06HZ4eCB6EHuIfQh/IIG4hGCFyIdgiQiKoIywjpiQuJJwlCCWAJjYmcibIJuYnKCeoKHgo+inIKkwrmiweLEQsaCz8LmIvXDBSMLIy4DNmM/Q0rDVcNig21jcMN244XDh8OLA5CjlQOZY5vjpCOrg7VD14PcY+ID58Pw4/ZEAGQFhAqkEYQXRCLEL0Q+BEkkS2RQZFcEWURiBGmkceR3hH6EhkSJJI0kkkSaRKHEpuSqhLBEtCS6BMAExeTKpNHk1wTcJN7k4qTlBOvk7wTzRPck/+UJBQ0FE0UXpR0FIwUqhS+FR+VN5VElV+VuxXMFeiWIpZnloEWlJawlsiW2hb1AAAAAEAhABDA3gCtwARAAATIR4BFwYHAQ4BJyYnASY2NzbTAlsfKgEBCv7pF04lFQz+6A8OGhICtwEpIBQS/jIjExUMFQHOGzoRCgAAAAABAHj/+AOIAwcAHAAAEwYUFxYXAR4BPgEnASE+ATQmIyEBNi4BByIHAQZ6AgIDBAFTChkTAQj+3wKHDRISDf15ASEJAhQLDQr+rQQBiQYMAwYD/pcJARIZCgEzARIaEwEyChoRAQr+lwMAAAAGAI0AEwNzAvAACwAXAEcAYQB9AJgAACUUBiImNDY3MhYXFgUOASImNDY3MhYXFhMHDgEHBRYXBR4BFAYjJS4BJyYCJy4BBwYuATY3PgEXHgIfARYXJT4BPwE+AR4BBTEVBg8BMzIWFAYHIyYnJj8BIyImNDY7ARYlBzMyFhQGKwEiJjUxND8BIyImNDY7ATIWFTEUBzEjFA8BMx4BFAYrASYnNTQ/ASMiJjQ2OwEWAbwjMyIiGgwWCREBaAEiMyMjGQ0VCRJNJQc0Jf6IBxIBlg0SEg7+ayMxBhcuCAUbEQ0WBQ4NBCAUIR4HBgcTIwF+DxQDJQMVGw7+YQECMSYFCAgFQwwBAQMyIwUICAU/DQE5STsHCgoHYggKBEo2CAoKCFwHCqoBAjEmBQgIBUMNAQMyIwUICAU/DE8aIiI0IgEJCRIZGiIiNCIBCQkSAb3GJS4EHg8BAQESGxIBASoijgEUIgMBAwMPGhYDAQQCBBwcHShq2h8BEw7GDQ8FFWUBAwRDCAsHAQENAwREBwsIAdllCg8KCggGBWQKDwoKCAZvBQNDAQcLCAEMAQMFQwgLBwEAAQB4//gDiQMHABwAAAE2NCcmJwEuAQ4BFwEhDgEUFjMhAQYeATcyNwE2A4cCAgQD/q0KGRMBCAEh/XkNExMNAof+3wkCFAsNCgFTAwF2BgwEBQMBaQkBEhkK/s0BEhoT/s8LGRIBCgFpAwAAAQB///cDjQMIABwAAAUWMjc2NwE2NCYiBwERNCYiBhURASYOARUWFwEWAf0FDQMGAwFoChIYCv7MEhsS/s4KGhEBCgFoBAYDAwMDAVQKGBQI/uAChw0SEg39eQEgCgIUCw0K/qwDAAABASX/7wLZAxUAEQAACQEWFAcBBiMGJjQ3CQEmNDYyAV8BcAoK/pMKEQ4UCgFZ/qcKFRoDC/6PChsL/pAKARUbCgFZAVkLGhUAAAAAAQBsAKgDkgJcABEAACUBJiIHAQYVBhYyNwkBFjI2NAOI/o8KHAr+kAoBFRsKAVkBWQobFeIBcAoK/pMKEQ4UCgFZ/qcKFRoAAQB///gDjgMJABwAAAEmIgcGBwEOAR4BNwERHgEyNjcRARY+ASc0JwEmAg8FDAQGAv6XCQESGQoBMwESGhIBATEKGhIBCv6XBAMGAwMDA/6tChkTAQgBIP16DRMTDQKG/uAJARUKDgoBUwMAAQD2AAkDCQL3ADcAAAEmBg8BJy4BDwEOAR8BIyIGBx4BOwEVIyIGFBY7ARUeATI2NzUzMjY0JisBNTMyNjQmKwE3NiYnAv0IFQfa2QcVCAEKAQfFiQ4WAQEQDLKzCxAQC7MBEBYRAbILERELsrILERELkcUHAQoC8QYBCfv6CgEGAgcWCOEQEQ4ObQ4cDuILEREL4hEWEW0RFhHmCBYHAAQApv/xA1oDGAAdACYALQA+AAABNS4BJy4BIgYHDgEHFQYXFBY7AR4BMjY3MzI2NTYBMhYXJgYHPgETIiYnMw4BJTY3Nj0BPgE3HgEXFRQXFhcDCgFWRwQ8WD0ERlYBUAERDb8LRFpEC8AMEQH+pg0WBhQqFAYWDRUgCHwJIf7PCTgMAnVYWHUCDDgJARykUoAgLDo6LCCAUqRCaQ0RLDY2LBENagIDDQsEAQMLDf1OFRISFWJAKQkOslh1AgJ1WLIOCSlAAAAAAwDLAAEDNQMFABkALAAtAAAlIi8BBwYmJyY1Ez4DNyEeARcTFAcGBwYnFxY2PwEDLgEnIQ4BBwMXHgE3JQLkFxW/sB4+FQ0DARIjLBkBcDNFAgINFCAI9N4JEwYBAgEjGv6QGyMBAwEGEwkB0AULbGoRDBoREQJCGCwjEgECRTP9vhIRGQcBvX0FAwgBAj4bIwEBIxr9wQEHBAUIAAAAAAIAuAA7A0MCxgAgADgAAAEVDgEHIS4BJxE+ATczHgEUBgcjIhURFDMhMjc1NDYyFhMVDgEiJj0BBwYiJjQ/ASMuATQ2NzMeAQNDASUb/fcbJQEBJRvQDRERDdAFBQIJBAERGREBAREZEeIIHBEJ43YNERENvwwRAUzQGyUBASUbAgkbJQEBERkRAQT99wUF0AwSEgFQvw0REQ124ggRGArhAREZEQEBEQAAAAAGAIQATQN7ArMAKgA4AEwAUgBjAG8AAAEnJisBNS4BJyEOAQcRHgEXMx4BFzI3PgE3Mx4BMzI3PgE3Mz4BNzU0JicBMQ4BJicmNT4BMhYXFDcjLgEHDgEHIyImNRE0NjMhMhYVFzIfASM1Ew4BJyYnJjU+ATMeARcxFhQ3BisBLgErASIHNTMDWlQdKSIBOiz+tSw7AQE7LBMKNiMKCxwpCaIKNyMJCR4sCAkdJQEREP4xByAkBwMBGCUZAdSdDUIpHCoIFBIZGRIBTBIZXREMRYNWByAREwcDARkSDhYFAkkBBgkLNSMBFxSfAbpQHSUsOgEBOiz+tCw6ASInAQIGJhwiKgEGKB0BJR2PFikP/vARDw0SBwgSGRkSByUnKAcHJhsZEwFLEhkZEmAMQ0/+vREQBggSBwgSGQEPDQcOLAchKApOAAAAAQBsAKgDkgJdABEAAAkBBiInASY1JjYyFwkBNjIWFAOI/o8KHAr+kAoBFRsKAVkBWQobFQIj/o8KCgFtCxENFQv+qAFYCxUbAAAAAAEBJf/vAtkDFQARAAAJAQYUFwEWMxY2NCcJATY0JiICoP6PCgoBbQsQDhQK/qgBWAoUGwML/o8KGwv+kAoBFhoLAVgBWQsaFQAAAAADAEP/wAPCA0AACwAXACkAAAEOAQceARc+ATcuAQMuASc+ATceARcOARMnNS4BIgYdARQfARYzMjc2JgICvv0EBP2+vv0FBf2+odkEBNmhotgFBdgVmQESGxMTrQcGFAsECQNABf2+vv0FBf2+vv38xwTZoaLYBATYoqHZAUZH3w4SEg7yFApTAhQMGAAAAAACAMkABAM+AvgAEQAjAAAJASYjDgEHERQXHgE3ATY3NiYHFAYHAQYnIi4BNRE0PgEXARYDB/4yERUfKgELETkbAc4VDRUTMQsI/jMFBQMEAwUKBQHNEgHWARcLASof/aUVERsOEAEXDRUkTj8KEgX+6QMCAwUEAlsDCAIC/ugMAAMAggBgA3oCngAPABgAJAAAASEiBgcRHgEXIT4BNxEuAQUhFh0BBSU1NAEhJicRBRY2NyURFAM5/YobJQEBJRsCdhslAQEl/W8Cdgn+vP68An/9igkBATYGEgYBNQKeJRv+QxslAQElGwG9GyU3AQgXzMwXCP4xAQkBZcIFAgPD/poJAAMAnABFA2UC0AAMABAAFQAAJTcBNycHMQEHIxUhNQMXBycJARcBBwFsSQEPfbNT/skeSgLJ114mXv7TAQBd/wBuggwBEn2zU/7Evz09AfVeJl7+0wEEX/78EAAABAA8ADQDwwLLAA8AGwAnADAAACUmAicmNzYSNxYSFxYHBgIBHgEXPgE3LgEnDgEFLgEnPgE3HgEXDgEnDgEUFjI2NCYB/9TjBgYGBuTT1eMFBwcF4/2vGsSensUaGsSfnsQBYztPAQFPOztPAQFPOx8rKz4rKzQSAQ4dDREcAQ4SE/7xHQ0RGv7zATk3ygsLyjc2ygsLysgBTzs7TwEBTzs7T9UBKz4rKz4rAAIAqAArA1MC1gALABQAAAEeARcOAQcuASc+ARMeARcVITU+AQH9SWACAmBJSGACAmBIksAE/VUEwALWAmBJSGECAmFISWD+VwJgSVVVSWAAAwA1ADMDywLOAAgADAAYAAABIgYHFSE1LgElFSE1Fz4BNy4BJw4BBx4BAn1e5QsCnAvl/VoBTvpHXgICXkdHXgICXgEtU1NUVFNTp1NTUwJeRkdeAgJeR0ZeAAAGAJ8ARwNnArgACAARABoAJwA0AEEAABMeATI2NCYiBgMeATI2NCYiBgMeATI2NCYiBgEOAQchLgE0NjMhMhYTDgEjISImNDYzITIWNw4BIyEiJjQ2NyEeAZ8BHy8fHy8fAQEfLx8fLx8BAR8vHx8vHwLHAREM/jMMEREMAc0MEQEBEQz+MwwREQwBzQwRAQERDP4zDBERDAHNDBECgRcfHy8fH/7nGB8fLx8f/ucYHx8vICAB7AwRAQERGRER/fANEREZEhL2DRERGREBAREAAgDgABgDHwLrABYAHAAAAT4BNx4BFzMeARcTFgYHISImNRM+ATc7AS4BIgYBbgJQPTxQAlIOEgEgARIO/gIOEyABEg5+vAI1TzUCWz1SAQFSPQERDf3/DhQBEw4CAw0RASk2NgAAAAABAEABXAPAAZwACwAAASEiJjQ2MyEyFhQGA5r8zA8XFw8DNBAWFgFcExoTExoTAAAJANsAEQMpAvYAAwAaACAAOQBSAFYAWgBjAGkAAAEDIQMlPgE3HgEXMx4BFxMWBgchIiY1Ez4BNzsBLgEiBgEhIiY1Ez4BOwE+ATceARczMhYXExQGDwEBIgYHAxQWFyUyNjUDLgErATUuAScOAQcVASETIQEhAyElIzU+ATceARcnMy4BIgYBMR8B3x7+nQJQPT1QAlMNEwEgARIO/f8OEyABEw1/vAE1UDUBX/4BERYgARYQSARTPj5TBE0QFgEhFRAD/iELDgEgDwsCAQsOIAEPC1kCTTo6TQEBgf4UHwGu/kAB0R7+awEtygE5Kys5AbyvBDBHMQIt/hsB5TE9UgICUj0BEQ39/A4UARMOAgYNEQEpNjb9ixYRAgYQFT5SAgJSPhUQ/f0RGAEBAkYOC/37Cw8BARELAgMLDgc6TwEBTzoH/esB8v4bAdgwBys7AQE7KwYkLy8AAAAEALIAPwNNAscAGwAnADAARgAAATc2NCYiDwEnJiIGFh8BBwYUFjI/ARcWMjYmJwU+ATcuAScOAQceATceARQGIiY0NhMOAQceATI2NT4BNx4BFxQWMjY1LgEDJxoLFh4MGhoMHhcBCxoaCxYeDBoaDB4XAQv+bT1RAgJRPT1RAgJRPR8oKD4oKB9rjgMBFB8UAmZMTWUCFR4VA44CJRoMHRcLGRoLFx4MGhoMHhYLGRoLFx4LYwFRPj1RAgJRPT1S1wEpPSkpPSn+4QOOaw8VFQ9MZgICZkwPFRUPa44AAAMAPv+/A8ADQAALABcAIwAABS4BJz4BNx4BFw4BAw4BBx4BFz4BNy4BEyEiJjQ2MyEyFhQGAf++/gUF/r6//QUF/b+i2QQE2aKi2QUF2Q7+oQ0TEw0BXw0TE0EE/r++/gQE/r6//gM8BdmhotkFBdmiodn+ZRMbExMbEwAEAK4AQANRAsAAGwAoADEARwAAASM1LgEiBh0BIyIGFBY7ARUUFjI2NzUzMjY0JgU+ATcuAScOAQceARc1HgEUBiImNDYTDgEHHgEyNjU+ATceARcUFjI2NS4BAy4jARQeFCQPFBQPJBQeFAEjDxQU/mo9UAEBUD08UAICUDweKSk8KCgeaowDARQeFAJlS0xkAhQfFAOMAlUkDxQUDyQUHhUkDxQUDyQVHhSxAVA9PFACAlA8PVAC1gEoPCkpPCj+5AKNag8UFA9MZAICZEsPFRUPaY0AAAAEAIj/wQNrA0AADgAaACYAMAAAAQ4BBxYAFxYyNzYANy4BAy4BJz4BNx4BFw4BAw4BBx4BFz4BNy4BBy4BNDYyFhQGBwH6ndEEFQEqIQkTBx8BLBUE0ZxB4Q0Eq4B/qwQN4UA+UgICUj49UgICUj0hLS1BLS0hA0AE053V/t0MBwcMASPVndP8zyr1oIGsAwOsgZ73AhUCUj4+UgEBUj4+Ut4BLEItLUIsAQAABACAAB4DgQLiAB4AJwAwADkAAAEuASIOAhUxFhcWFwcGFh8BFjsBNj8BFjc+ATc0JgcyFhQGIiY0NgUuATQ2MhYUBjc+ATIWFAYiJgMQNoudjG05ASEfNRsIAQgCCg8CExFcQ0Wj2QQ6lRAWFiEWFv69EBYWIRUVcgEVIRYWIRUChi0vL1t2Q0lCPCpQDBwMAgoCCjEQAgS/j0N2mhYhFRUhFk0BFSEWFiEVJhAWFiEVFQAABABB/8ADvwM/AE8AkwCfAKgAAAEvATc2Ji8BLgEPAS8BLgEnIw4BDwInJgYPAQ4BHwEPAQ4BBxUUFh8CBwYWHwEeAT8BHwEeARczPgE/AhcWNj8BPgEvAT8BPgE9AS4BDwEGDwEGHwEHJy4BDwEGDwEjJyYvASYPASc3PgEvASYvATU3Nj8BNi8BNxceAT8BNj8BMxcWHwEWPwEXBw4BHwEWHwElDgEHHgEXPgE3LgEHLgE0NjIWFAYDjVYSMgsDDjcPJRFIJw8CHBRMFB0FDyhJESYPNg8DCzIRVxEXARoUVhI0DAQONw8lEUgnDwMeFUwUHQYPJ0cRJg82DwMLMhFXFBkDGydgEAcfBgo3Kk4GEQdKEAMSOhIDEEgRDU8pNwQDAx8HEGBgEAcfBgo3KU8GEQdKEAMSOhIDEEgRDU4qNwUCAx8GEWD+hDJEAQFEMjJEAQFEMhcfHy4fHwHdDydIESUPNw4DCzISVhMYAQEZFFcRNAsDDjcPJhBIJw8EHBRMFB0FECdKECYPNg8DCzIRVxQZAQEZFFcRMgsDDzYPJhBIJw8EHRZMFR10EQMQSBENTyk2BQMDIQcRX18RBx8FCjYpTgcRB0oQAxE7EQQQSBAOTik2BQMEHgcRYGARBx4GCjYpTgcRBkoRAxFbAUQyMkQCAkQyMkStAR4vHh4vHgAABQCDACEDfgLfAB4AOgBDAE0AVgAAAS4BIg4CFTEWFxYXBwYUHwEWOwE2PwEWNz4BNy4BAw4BIzEGJyYPATc2JicxLgEnND4CMx4BFxQGJxQGIiY0NjIWBzEOASImNDYyFgcOASImNDYyFgMONoqcimw5ASEeNRsHCAIKDwIUEFtCRKLYBAE5YC51QUI+DQtHFgMGCDM6ATBadkKLtAMwPBYgFhYgFqgBFSEVFSEVpwEVIRUVIRUChCwvL1p1QklBPCpPDBsMAgoCCjERAgS+jkJ1/ocoKgMSAwYlPwkTBiNrPjVfSicDknA3ZJQRFRUhFRUQERUVIRUVEBEVFSEVFQAAAAADALAAQgNQAr4ADQAZAEkAACUVDgErAS4BPQE0NjIWBRUOASsBLgI+ARYBFA8BDgEjISImJwMuASsBIiY0NjsBMhYXEx4BMyEyNj8BNiYjIS4BNDYzITIWFxYDJAEcFAEUGxsoHf6fARwTARUaARspHQGOAhYHLR3+px0sByoCCwgxDRERDTEeLAYqAgwHAVoHDAIWAw0L/qgNERENAVgTIQwScwIUGwEaFAITHRwUAhQbARopHAEbAXEJCdUeJSUdAWAHCREaEiUd/qAHCQoH1QwRAREaERAPGAAAAgC/AEADbALsACkAWwAAASYnJgYPASYnNz4BJyYnLgEnIwYHDgEXHgEXHgEXHgEXMzI+Ajc1NiYHFRQGBwYnLgEnLgEnLgEnJjY3NjsBMhYXHgEXFgYPAQ4BFx4BFxY/AT4BFxYXHgEVNAMeKikZMxQYXTkYEwsJDwYFMiNeKxoMDQEJMyolZTtAkE0HER8bDwECLAwFBgsPRYU6NlwjJi8HAQQGCw9eDhMCAwwJBAQHKQcCBSRlPxIPKQcTCjAvDBEBVQYPCQsTGDpcGBQzGScsIisBAR0OJBRNkD88ZSUqMwkNGCMTXiMzVl4HDwQKAQcvJiNcNjuERQcPBgsRDBgwFwkUBykHEgg/ZCUIDCkHBAQQCAIUDwMAAAAFAJQAAANrAwAAHwAnAC8APABJAAABIzUuAScjDgEHFSMiBhQWOwERHgEXIT4BNxEzMjY0JiU0NzMWFxUjAQYHISYnESEBPgE9ATQmIgYdARQWFz4BNzUuASIGHQEUFgNOuwEnHZ0dJwG7DBERDDEBJx0BsR0nATEMERH+TQqdCQGxATsBCf5PCQEBxf7PDBERGRERqgwRAQERGBERAnpBHScBAScdQREZEf4GHScBAScdAfoRGRFBCQEBCUH9ywkBAQkB9v52AREMxQwREQzFDBEBAREMxQwREQzFDBEAAAQAPf+9A8MDQwAMABUALQA5AAAlIiYnET4BMhYXEQ4BBxQWMjY0JiIGAS4BJyYiBw4BBwYUFx4BFxYyNz4BNzY0AS4BJz4BNx4BFw4BAf4MEAEBEBcQAQEPKBAYDw8YEAG+I3xRVLhTUX0iJCQifVFTuFRQfSMj/j2l3AQE3KWl3AUF3PUQDAF+DA8PDP6CDBBvDBAQFxAQAZ9RfCMjIyN8UVS4U1F9IiQkIn1RVLf+HwTcpabbBQXbpqXcAAAAAAgAQP/AA8EDQQALABcAMABJAGIAewCrALQAAAUuASc+ATceARcOASc+ATcuAScOAQceARciJy4BJyY0Nz4BNzYyFx4BFxYUBw4BBwYDIgcOAQcGFBceARcWMjc+ATc2NCcuAScmAyInLgEnJjQ3PgE3NjIXHgEXFhQHDgEHBgMiBw4BBwYUFx4BFxYyNz4BNzY0Jy4BJyYDFAYiJj0BNjc2PwI+ATc2NS4BJw4BBxQGIiYnPgE3HgEXFQYHBgcGDwEOAQcGDwEeATI2NCYiBgIAvv0EBP2+vv0FBf2+pdsEBNulpdoEBNqlW1NRfCIjIyJ8UVO2VFB8IiQkInxQVFtbU1B7IiMjIntQU7ZTUHwiIyMifFBTW09IRmseHh4ea0ZInkhGbB0fHx1sRkhPT0dGax0fHx1rRkeeSEVrHR8fHWtFSDERGhEDHgwPDBkQFAcRAUc3N0cBERoQAQJpUFBpAgEgDRMNEwkQEQUMAUYBFiIXFyIWPwT9vr79BQX9vr79OATapaXbBATbpaXaQSMifFFTtlRQfCMjIyN8UFS2U1F8IiMDfyMifFBTtlNQeyIjIyJ7UFO2U1B8IiP8vR4ea0ZInkhGbB0fHx1sRkieSEZrHh4DBx8da0VInkdGax0fHx1rRkeeSEVrHR/+AgwREQwVLRoLCAYLBwwHEic0RAEBRDUMEREMT2UCAmVOAT0jDwoICAQHCQUKFHQRFhYiFxcAAwAuAC4D0QLTAAgAFAAgAAABIgYHFSE1LgElNSMVIxUzFTM1MzUXPgE3LgEnDgEHHgECfl/pCgKlC+j+I1R/f1R//khgAgJgSEhfAgJfASxVVVRUVVWpf39Vfn5VVQJgSEhfAgJfSEhgAAAABADFACUDmQLgACwAOABEAGAAACUOAQcVFgYnIQYmNRE0NhchNhYHFR4BMjY9AS4BJyEOARURFBYXIT4BNzU0JgEjIgYUFjsBMjY0JgcjIgYUFjsBMjY0JiU3NjQmIg8BJyYiBhQfAQcGFBYyPwEXFjI2NCcDIw0RAQERGf5QGBERGQGvGREBAREaEgEiG/3+GiMjGwIBGyIBEv7ouQ0SEg25DRERDbkNEhINuQ0REQErQAkSGQlAQAkZEglAQAkSGQlAQAkZEgnUARENKRkRAQERGQHtGREBAREZZw0REQ2RGiIBASIb/cEaIwEBIxpSDREBFhEbEREbEZARGxERGxEUQAkYEwlAQAkTGAo/QAkZEgk/PwkSGQkAAAAEAKcAJANgAtsACwAXACUAMwAAAS4BJz4BNx4BFw4BAw4BBx4BFz4BNy4BEycuAQ8BDgEdASE1NCYHITU0Nj8BNhYfAR4BFQIDSmMCAWRKS2MCAmNLMUEBAUExMUECAkH9WWTcZFkYHAK5Fyb9wBgUSVO2UkoTEwF8AmNKS2MCAmNLSmMBIQFCMTFBAQFBMTFC/mEtMQMxLQ03HH5+HDSRTREiCBseAh4bCCARAAAAAAQAuAA5A0gCxwASACIAKwA0AAABIwYHAQYWHwEWNxY3ATY9AS4BAQYvASY3ATA7ARUjFQcBIhMyNjQmIgYUFjcyFhQGIiY0NgMZ3xgV/r4TARLPEhsYFQFCEgEa/poCAswCAgFBBNICAf7AAcggKytBKyshCg8PFQ4OAscBEf6+EzURzhMBARMBQRIb3hQa/a4BAs0DAwFBzAQF/sABUStBKytBK2UPFQ8PFQ8AAAAAAgC2ADcDRwLJAB0AKQAAJTEnPgE1NC4CIgcOAhQeAjMyNjcXFjI/ATYmJS4BJz4BNx4BFw4BAz+MHyEsUGlzNjNQLCxQaTkxXCmMCBsHAQYB/o5hgQICgmBhgQMDgWmMJl0zOmhQLBYXUGl0aFAsICCMCAgBCRdlAoFhYYEDA4FhYYEABAB4ADwDigLDACMAQABJAF4AAAEOAQcVDgEjBic1LgE3PgE3LgEnJiMmBwYuATY3MTYzHgEXFgMBJiIGFB8BDgEPAQYXMR4BFxYXFhc2NxcWMjY0ATQ3Fw4BIy4BFyInLgEnPgE3FwYHFB4CMzI3FwYDgQMjHwUNBgsJCQIHFhsFDTUoZ3MZGA0TBg8MHx+3xAYJPf21ChcTCU0vUyEDBgYCNDE5P05UbGRRCRsS/mwPVwgWCxkkPXJoKDUNEko4Vh8BEyItGC8kUU4BYgc5JQEEBQEGAQcaCRspCRdIJWABBQIOGBMEBRDpGg7/AAJLCRIYCU4jYjsEDBAEUzY6ISkBAUVSCBIXAQYXEVYICAEjxF8mSBYgXSlWIjEZLCMSIFE1AAMAQf/BA8ADQAALABQAIQAAAQ4BBx4BFz4BNy4BAzQ2MhYUBiImEzIWFREUBiImJxE+AQIAvv0EBP2+vv0FBf3pFyIWFiIXKA0RERoRAQERA0AF/b6+/QQE/b6+/f1oERcXIhYWAjQSDP6XDRERDQFpDBIAAgBA/7wDwAM8AAsAFwAAASEiJjQ2MyEyFhQGARE0NjIWFREUBiImA5r8zA8XFw8DNBAWFv42ExoTExoTAVwTGhMTGhP+hgM0EBYWEPzMEBYWAAAAAwBB/78DwgNAAAsAFwAzAAABDgEHHgEXPgE3LgEDLgEnPgE3HgEXDgETIzU0JiIGBxUjIgYUFjsBFR4BMjY3NTMyNjQmAgG+/QUF/b6+/gUF/r6h2QQE2aGi2QQE2Q2OExsTAY4NExMNjgETGxIBjg4SEgNABf2/vv0FBf2+v/38xgTZoaLZBATZoqHZAZuODhMTDo4TGxOODhMTDo4TGxMAAAAGAM4AEANIAvsADAAZAEYATABYAGUAAAEUBgcjLgE0NjsBMhY3FAYHIS4BNDYzITIWAycjJgchDgEHER4BFzMWNjc2LgErAS4BNRE0NjchFR4BFzMdARQWFz4BPQInIiYnNRcTDgEHLgEnPgE3HgEHLgErASIGFBY7ATI2Ai8SDZoNERENmg0SeRIN/u0NERENARMNEigLBgck/u4rOAEBOCvSAwwHCQITCtIQFhYQAQoBOSpUDhIQDZEQFQFuYAJXQkJXAgJXQkJXOAEOC48KDw8KjwsOAVMMEgEBERoSEnwNEQEBERoSEgEHCgEBATgq/d0qOAEBAwYIIA0BFRACIxAVAVQqOAGmAQIQAQEQAgHfBRYQSG7+b0JXAQFXQkFXAgJXPAsODhYODgADAEEBLgO/AcEACAARABoAABMeATI2NCYiBgUUFjI2NCYiBgUeATI2LgEiBkEBKj8qKj8qAXQqQCoqQCoBdAEqPysBKj8qAXcfKio/KiogHyoqPyoqIB8qKj8qKgAAAAAEALcAEQNEAu8APwBOAF8AbgAAJS4BJyYiBwYHJzYnJic3FhcWMjc+AiYnLgEiBw4BBwYXByYnJiIGBw4BHgEXFjI3NjcXBhceARcWMjY3PgEnAzY3NjIWFxYGBwYiJicmBw4BIicuATc+ATIXFhcWFRQBIicuATc+ATIXHgEHDgEDPQghGSFLIRQQgRQMAwWBEBQhSyEZIQ8HDhRCSyEXIQgODoUMDSFMQRQOCA8iGSFLIA8MhQ0LCCEZIUtCFA4HB9cJHBMsJQsREhwTLCULEc8LJSwTHREQDCUrEx0JAwEbFhMcEhELJSwTHBIRCyXEGywPEhIMEUouMAoKSxIMEhMOLDg3GSElEw0oGiwsTQsIEyYhGTc3LA8SEggLTSgpHCwOEyYhGTcbAbQgEQsWEx1BEQoVEx3+ExYLEUEdExUKEiAKCxX+4woSQB0TFgsRQR0TFQAAAAEAlgAEA2kC/QBAAAABMQEOASIuAjQ2NwE2FhceARQGBwEGIicjJic0NjcBNh4BBgcBBhQWMjcBPgE1NCYiBwEOARQWFxYyNwE2HgIDYP7HIVZeVUMkJCEBODSFMxgaGhj+xx9OHgEbAQ8OASIKGhEBCf7eChceCwE3Dw89ViD+xxkaGhk2izUBOQoZEQEBgP7IISMjQlVfViEBODEBMBc9QzwY/scdHR8nFSQOASAKARMZCf7gCyAVCwE7DiYVKz0e/sYXP0Y/GTIyATgJAREZAAAABABv/+MDkAMXABIAGAAvAEUAAAEbAQ4BLwEjLgE9ATQ2NzM3NhYPASMVMxcTPgEXHgEVFAYHBi4BNjc+ATU0JicuAQceARUUBgcGLgE2PwE+ATUmJyY0NjICaQMCA0Ig5XkaIiIacuggQTn5iZD32wkYCiouNC8KGBADCSUoJCEJASwUFx0aChgRAgoBDxIBGQkSGALS/q3+qigeGc0BIhnxGSIBzRgdKdvx3AIbCQEIJ2g6PmwnCAMUFwgfVDAuUB4JGFYVNB4hOxUIAhQYCAEMIxQiGgkYEgAAAAQAtAAxA0oCzwAfADkARgBTAAABIzUuASIGHQEjNTQmIgYdASMOAQcRHgEXIT4BNRE0JgciIyE1NDsBFRQWMjY9ATMVHgEyNj0BMzIVAxQGKwEiJjQ2OwEyFgUOASsBIiY0NjsBMhYDDGkBEBgR0xEYEWkaJAEBJBoCGRokJBYCAv3iBWkRGBHSARAYEWoEUA8KlwoPDwqXCg/+9AEOC0MKDg4KRAoOApUdDBERDB0dDBERDB0BJBr+GhsjAQEjGwHmGiTRkwUvDBAQDC8vDBAQDC8F/rYMEREXERELDBERFxERAAIAQv/BA8EDQAALABwAAAEOAQceARc+ATcuARMBBiIvASY0NjIfAQE2HgEUAgK+/QUF/b6+/QQE/Tb+5AobC5gKFBkLgAEECxsWA0AF/b6+/QQE/b6+/f6a/uUKCpgLGRQKgQEGCQEUGQAAAAUAhABJA3sCswApADcAPQBOAFwAAAEnJisBNS4BJyEOAQcRHgEXMxYXFjc+ATczFhcWMzI3PgE3Mz4BNzU0JgExDgEmJyY1PgEyFhUUATMyHwEjFw4BJicmNTQ2MzEeARcxFhQ3BisBJicuASsBIgc1MwNaVB4pIQE7LP61LDoBATosFAsfJCkdKQmiCh8bIAkJHisJCRwmARH+IQcgJAcDARglGQERIREMRYNWByAkBwMaEg4WBQJJAQYJChgOIRIBFxSfAbpQHSUsOgEBOiz+tCs7ASQVFwgGJhwkFRMBBigdASUdjxYp/v8SDg0SBwgTGBkSBwE8DEP1EQ8OEgcIEhkBDw0HDiwHHhQLDApOAAAAAAIAPv+8A8IDQQALACgAAAEOAQceARc2EjcmAgEGJjQ/AScmNDYyHwE3NjIWFA8BFxYUBiIvAQcGAgC//gUF/r+//gUF/v7ADhUKamoKFRkKamkLGRQKaW4KFRsLaWkLA0EF/r+//wUFAP++vwD//acBFBkLaWkLGRQJamoJFBsLaWcLGRQJamoJAAAAAgA//78DwANAAAsAIAAAAR4BFw4BBy4BJz4BFyIGHQEeAR8CFjY/ATYmLwE1LgEB/7/9BQX9v779BQX9vg0TAQoIrwIMFwcCBQsLnAMSA0AF/b6//QUF/b++/aoTDvcIDwNTAwQKDAIMFwdI4w4RAAkAtwAgA4EDGgAcADEATQBZAGUAcQB9AIsAlAAAAREuAScjBwYrASIvASMOAQcRHgEXIRYzPgE3NCYHIicuATU+ATcyFxYXFhcWFAYHBgclIiY1ETQ2NzMXFjsBMj8BMx4BFxEmIw4BBxQXAyEyNjQmIyEiBhQWFyEyNjQmIyEiBhQWEzMyNjQmKwEiBhQWEyMiBhQWOwEyNjQmFz4BNzUuASIGHQEUFjMHFBYyNjQmIgYDSAEyJVQ5BwqlCgg4VSUxAQExJQF8Ji1GXQEehiMcExYBOywbFwsLDQgLGBYbH/4xCw8PDD0oGCGlIRkmPwsPARcYRl0BFdgBCwoNDQr+9QoMDAoBCwoNDQr+9QoMDCvcDBAQDNwMEBBpfgkNDQl+Cg0N5QkNAQENEw0NChgPFg4OFg8BQgFJJTEBMQcHMQExJv4EJTEBFwJdRSVBzRUPKxksOgINBwkMERUzLg8RARcQCwH9Cw8BIxUVIwEPC/7XBwJdRSwmAYANEw0NEw19DRMNDRMNAW0QFxAQFxD+Qw0TDQ0TDXoBDQlCCg0NCkIJDSUMDg4XDg4AAQDpABUDFwLnABMAACUiLgE1ETQ2NyEeARURDgEvAQcGAREKEwsyJgF/JjEBJxPj1AkaCxILAkgnNAICNCf9uBcWC4CABgAAAgCtAD8DUgLBAAwAJAAAJQ4BIyEiJjQ2MyEyFgMHBiIvASY0NjIfARE0NjIWFxE3NjIWFANSAREN/ZkNEhINAmcNEZCrCRwIqQkSGQl2ExoRAXYJGRJdDBISGRISASKrCAirCRkSCXYBYgwSEgz+nnYJExgAAAAAAwBC/8EDwQNAAAsAFwAoAAABDgEHHgEXPgE3LgEDLgEnPgE3HgEXDgETAScmIgYUHwEWMjcBNjQuAQICvv0FBf2+vv0EBP2+otgEBNiiodkEBNkh/vyACxkUCpgLGwoBHAoWGwNABf2+vv0EBP2+vv38xwTZoaLYBATYoqHZAf/++oEKFBkLmAoKARsLGRQBAAAAAAUAQP++A8ADPgALABcAIAApADQAAAEOAQceARc+ATcuAQMuASc+ATceARcOAQMeATI2NCYiBgcUFjI2NCYiBgUeATI2NzEuASIGAgC+/QUF/b6+/QUF/b6h2QQE2aGi2AUF2M4BGSUYGCUZrxklGBglGQFdARglGQEBGCYYAz4F/b6+/QUF/b6+/fzHBNiiotgFBdiiotgBehMYGCYYGBMTGBgmGBgTExgYExMYGAAAAwA+/7wDwgNBAAsAFwA0AAABDgEHHgEXNhI3JgIDLgEnPgE3HgEXDgEDJiIPAScmIgYUHwEHBhQWMj8BFxYyNjQvATc2NAIAv/4FBf6/v/4FBf6/otoEBNqiotoEBNoNChkLaWoKGRUKamoKFRsLaWkLGxUKbmkKA0EF/r+//wUFAP++vwD//MIE2qKj2gQE2qKj2gIZCQlqagkUGQtpaQsZFAlqagkUGQpoaQsbAAACAJz/4ANkAyAABQAgAAAlHgEyNj8BJi8BNS4BJy4BIgYHDgEHFQcGBx4BFyE+ATcBihI9Tj4R7gRJBAJYSQQ/Wj8ESVgBBE0BARENAooNEQEmICYmIFpkQQSUVYQhLTw8LSGEVZQERWgNEQEBEQ0AAwA8ADQDwwLLAAsAGwAnAAABHgE+AiYnJiIGFCUmAicGAgcGFxYSFzYSNzYFDgEHLgEnPgE3HgEByw8mJxwLCw4XPC0CBwXj1dPkBgYGBuPU1eMFB/7IAU87O08BAU87O08BRA4KChwnJw4WLTwxHQEPExL+8hwRDR3+8hITAQ0aEQY7TwEBTzs7TwEBTwAAAgCEAF8DfQKiAAoAGwAAASU3LgEnIQ4BBxcFBwUOAS8BJScRFBYzITI2NwIAAVIrBSIY/YcZIgQoAs8N/p8GDwcC/qANJRwCdxslAQF91RwXHAEBHBUcKQnfBAEDAt8H/nccJSUcAAAEAED/wQO+Az8ACwAXACQAMQAAAQ4BBx4BFz4BNy4BAy4BJz4BNx4BFw4BJSImJzU+ATIWFxUOATMiJj0BNDYyFh0BFAYB/779BAT9vr79BAT9vqHZBATZoaHZBATZ/uQOEgEBEhwSAQES4w4TExwSEgM/Bfy+vv0EBP2+vvz8yATZoaHZBATZoaHZ4xMN4Q4SEg7gDhMTDeEOEhIO4A4TAAAAAAUAcf/mA5IDGgAUAB0AMwBJAFcAAD8BIzUzNxc3Jy4BDwEjDgEdARQWFwEHEycHFxY2NxMmIgYUFxYXFAYHIw4BHgE3PgE1NCY3Jg4BFhceARUUBgcOAR4BNz4BNTQmAS4BNwE+AR4BBwEOASf8M4KJ+QE8AQNBIOhyGiIiGgHAPAOuJ6wgQgNrChgSCRkBERABCgIRGAoaHRdLChgRAQkhJCglCQMQGAovNC79XAsCCQIZChsWAwr96AobDMw88dueRlkoHRnMASIZ8RohAQEKRv6cmy6aGR4oAbsJEhgJGiMTIw0JGBMCCBU7IR40dwgBExgJHlAuMFQfCBcUAwgnbD46aP4PCRwLAncLAhMcC/2JCwIJAAAEAED/vgPAAz4ACwAUACEAKgAAAQ4BBx4BFz4BNy4BAy4BNDYyFhQGNw4BIiYnMT4BMhYXMCEOASImNDYyFgIAvv0FBf2+vv0FBf2+EhkZJRgYyAEYJhgBARgmGAH+ogEYJRkZJRgDPgX9vr79BQX9vr79/hkBGCYYGCYYKxMYGBMTGBgTExgYJhgYAAUAqAADA1gDJgAxAEQAVgBmAHcAACUGBw4BIiYnJic0NzY3MzIeAQYHDgEHFBcVFhczHgEyNj8BNj8BLgEnMSY1NDYXFhcWAy4BIg4CFBYfARYyPwE+ATQmAwcOAS8BJjU0PgIyHgIUBicuASIOAhUeARc+ATc0JgcGIicuATU0Nz4BMx4BFxQGA1gBZy57jnovZwEqJUQCChUNDBQqLwQKGx4BLGxxaywWFQ4JBCk4GhkQSCctiSlrdmtSLisooBM2E6UrLS1VghkSGYJEI0FVXVRCJCNbFTc8NisXAlRAQFUBFz4bTBsMDRsNIRElNAENejkgDw8PDyA5IhoXEAgZGQQLFQUCBQERBwwMDAwJBgkJBRINCR0PEgUQFxsCMigtLlJrd2kpoBERmytreWv+tYIaARuCTFgvU0MjIkJUX1TeFRYWKjUcQFUBAVVAHDWVGxsNIBImGwwNATUkEiAAAAAABABA/8EDvgM/AAsAFwAsAC8AAAEOAQceARc+ATcuAQMuASc+ATceARcOARMnJiMOARURFBYXHgEyPwE2NzYnJgU1FwH/vv0EBP2+vv0EBP2+odkEBNmhodkEBNko8A0NGB8DAwkZGw7wDQYLBgT+7NADPwX8vr79BAT9vr78/MgE2aGh2QQE2aGh2QGqiQcBHhj+7QYPBQ0PBooGDRUVFpnsdgADAHgAPAOJAsMAHAAuAEUAACUBJiIGFB8BDgEPAQYXFR4BFxYXFjMyNxcWMjY0JQYjLgEnNj8BFwYVHgEXMjcXJQ4BBxUOASMGJwEnLgE2NzE2Mx4BFxYDTP21CRgSCU0vVCADBgYBNDI5P05Ua2VRCBwR/v0hLDZJAQEZAR4KATMmFhMdASwCIx8FDQYLCf6sAwsHDwwfHrfEBglvAksJEhgJTiNiOwQMDwEEUzY6ISlFUggSF60ZAUg3LCECHhMWJjMBCh1OBzklAQQFAQYBVwQIGRMDBhDpGg4ABQDT/9wDMgMlAA0ALwA+AEcAVAAAAS4BPQEuASIGHQEUFhcBIgYdAQ4BByInBxYXFSMOARQWMyEyNjQmJyM1PgE3NTQmJxUHMQc1ByYnET4BNx4BExUOAQciJzczAS4BNwE+AR4BBwEOAQFPExQBERoRHx0B0w0SAnxdNSwoMjtxDBISDAEcDRISDXBrjAIQfTKyHw4BAk06Ok4BAU46FhMvAf7XCwIJAhgKHBYCCf3nCRwBAhtAI0gMEhIMSDFXJAEQEgxIXHwCFy0cCFEBERoRERoRAVEOmm5IDBC4LDrOASUbIQEcOk4BAU7+9006TQIHN/72CRwLAncLAhMbDP2KCwMAAAAAAgDr/9sDGAMlAA0ANwAAAREOAQcuAScRPgE3HgEXIgYdAQ4BBy4BJzUuASIGHQEeARcVIw4BFBYXIT4BNCYnIzU+ATc1NCYCiwFNOzpNAQFNOjtNcA0SAnxdXHoCAREZEgKLbHEMEhIMARwNEhINcGuMAhACnP7kOk0CAk06ARw6TgEBTvISDEhcfAICfV1IDBISDEhtmg9RAREaEQEBERoRAVEOmm5IDBAAAAMAQf/AA8EDQAALABcAIAAAAQ4BBx4BFz4BNy4BAzU0NjIWHQEUBiImEyImNDYyFhQGAgG+/gQE/r6+/QUF/d8TGxMTGxMhExkZJRkZA0AF/b6+/gQE/r6+/f2A8A4TEw7wDhISAWwZJRkZJRkAAAADAK8AQANRAsAAGwAnADQAAAEjNS4BIgYdASMiBhQWOwEVFBYyNjc1MzI2NCYFPgE3LgEnDgEHHgEBPgE1LgEnDgEHFBYXAy4jARQeFCQPFBQPJBQeFAEjDxQU/mo9UAEBUD08UAICUAERDxUDjGpqjAIUDwJVJA8UFA8kFB4VIw8VFQ8jFR4UsQFQPTxQAgJQPD1Q/psBFA9pjQMDjWkPFAEAAAMAeQAVA4YC6QARAB0AJgAAJQEuASIGBwEGFB4BMyEyPgE0ATQ2MhYXFQ4CJicXIiY0NjIWFAYDef7aDS00LA7+2gwaLBoCTBwrGv5dEBgPAgEQFxABHBAWFiAWFqsCDxcYGRb98Rc1LhwbLzUBhAwQDwv6DBABDwyqFiAWFiAWAAAAAAQAjf/AA28DPwAOABoAGwAkAAABDgEHFgAXFjI3NgA3LgEDDgEHLgEnPgE3HgEHIx4BMjY0JiIGAf6d0QMUASsgCRMIHgEsFQTRCwFSPj5SAgJSPj5SkE8BLEMsLEMsAz8E05zW/t0MBwcMASPWnNP+fD5RAgJRPj5SAgJSPiEsLEMsLAAAAAABAL8AQANsAuwAKQAAJRUOAysBLgEnLgEnLgEnJjY3NjczHgEXFhcWBg8BFhc3PgEXFhceAQNqAQ8bHxEHTZBAO2UlKjMJAQ0MGiteIzIFBg8JCxMYOV0YFDMZKSoiLPleEyMYDQkzKiVlPD+QTRQkDh0BASsiLCcZMxQYXDoYEwsJDwYGMwAAAwCbAF8DZAKeAAwAGgAnAAAlDgEjISImNDY3IR4BNw4BByEuATQ2NyEWFxY1DgEjISImNDYzITIWA2QBFxH9iBAYGBACeBEXAQEXEf2IEBgYEAJ4FA0IARcR/YgQGBgQAngRF30NEREZEQEBEfUMEQEBERkRAQEMCPgMEhIYEhIAAAAAAwC4ADoDSALHABIAGwAkAAABIwYHAQYWHwEWMzI3ATY9ATQmAyImNDYyFhQGJxY+AS4BDgEWAxnfGBX+vxQBE84SGxgVAUISG5sgKytBKionCxEFCxURBQsCxwER/r4TNBLOEhIBQRIb3hQa/v8rQSsrQSszAgoVEgULFREAAAAEAEH/vwPCA0AACwAXACMALwAAAQ4BBx4BFz4BNy4BAy4BJz4BNx4BFw4BAw4BBx4BFz4BNy4BAyEiJjQ2MyEyFhQGAgG+/QUF/b6+/gUF/r6h2QQE2aGi2QQE2aOw6gQE6rCw6gUF6gH+og0TEw0BXg4TEwNABf2/vv0FBf2+v/38xgTZoaLZBATZoqHZAxgF6bCx6QUF6bGw6f5GExsTExsTAAMBsf/FAk8DOQAMAA0AFgAABSImJxE+ATIWFxEOAQMjFBYyNjQmIgYCABUcAQEcKhwBARwVTy1ELS1ELTscFQHyFRwcFf4OFRwDJSItLUQtLQAAAAUAQP/BA74DPwALABcAIwAvADsAAAEOAQceARc+ATcuAQMuASc+ATceARcOAQMOAQceARc+ATcuAQEOASImJzU+ATIWHwEUBiImPQE0NjIWFQH/vv0EBP2+vv0EBP2+odkEBNmhodkEBNmeq+IEBOKrquMEBOP++QESHBIBARIcEgHwEhwTExwSAz8F/L6+/QQE/b6+/PzIBNmhodkEBNmhodkDBQXiqqviBATiq6ri/gQOExMO4A4SEg7gDhMTDuAOEhIOAAAABQBA/8ADvwM/AAsAFwAcACgAPQAAAQ4BBx4BFz4BNy4BAy4BJz4BNx4BFw4BAzY/AScTDgEHHgEXPgE3LgETBg8BBiImJy4BNRE+ATcyHwEWFxYB/739BQX9vb79BQX9vqHYBQXYoaLYBATY2A1aaM9BtO8FBe+0tO8FBe8eBw3wDRwZCAQDAR4YDQ3wFAQGAz8F/b2+/QUF/b69/fzIBNiiodgEBNihotgBBAc0OnYBLQXvtLXvBATvtbTv/kYNB4kHDw4FDgcBExcfAQeJDRcUAAADAGX/wQOaAysAQABMAFgAAAE1LgEnDgEHFQ4BHQEUFhczPgE3NS4BJyM1PgE3HgEXFSMOAQcVHgE7AQ4BBy4BIw4BFBYzMjY3PgE3PgE3NTQmBRUUKwEiPQE0OwEyBRQrASI9ATQ7ATIVA1sExJOUxAQcIyofVR4qAQEqHiADoXh4ogMgHyoBASofFQtPVwkcERwjIxwRHQh5bQ0eKAEj/ZYKVQoKVQoCTgpVCgpVCgHDDJTEBATElAwEKBypHioBASoeqR8qAQp4oQQEoXgKASofqR8qJjwIDxEBIzcjERAKYj0CKR+oGydEqQoKqQqzCgqpCgoAAAIAPv++A8ADQAALAEAAAAEOAQceARc+ATcuAQMeAQ4BKwEVMzIWDgErARUUBiImPQEjIiY0NjsBNSMiJjQ2OwE1JyY0NjIfATc2MhYUDwEVAf++/gUF/r6//QUF/QsNEQEQDZaWDREBEA2WERkRlg0REQ2VlQ0REQ2VqwgRFwihogkXEgmtA0AF/b+//QUF/b+//f5iARAaEXgRGRFaDRERDVoRGRF4ERoRE6wJFhEIoKIIERgJrRIAAAAABACUAAADawMAAB8AKwA3AD8AAAEjNS4BJyMOAQcVIyIGFBY7AREeARchPgE3ETMyNjQmATU0NjIWHQEUBiImNzU0NjIWFxUOASImAzQ3MxYXFSMDTrsBJx2dHScBuwwREQwxAScdAbEdJwExDBER/jkRGRERGRGeERgRAQERGBGKCp0JAbECekEdJwEBJx1BERkR/gYdJwEBJx0B+hEZEf5VxQwREQzFDBERDMUMEREMxQwREQH4CQEBCUEAAAAABgBx/+YDkgMaAAUABwAdADMAQQBRAAAlDgEvAQEPATcmIgYUFxYXFAYHIw4BHgE3PgE1NCY3Jg4BFhceARUUBgcOAR4BNz4BNTQmAS4BNwE+AR4BBwEOAScBBwEjLgE9ATQ2NzM3NhYXAnADQiCsAQ4xC6oKGBIJGQEREAEKAhEYChodF0sKGBEBCSEkKCUJAxAYCi80Lv1cCwIJAhkKGxYDCv3oChsMAawl/rVPGiIiGnLoIEEDLCgeGZoBPToMVwkSGAkaIxMjDQkYEwIIFTshHjR3CAETGAkeUC4wVB8IFxQDCCdsPjpo/g8JHAsCdwsCExwL/YkLAgkCTCz+fAEhGvEZIgHMGR0oAAgAtwAPA0oC/AAPABcAHwBCAE4AVgBeAGwAAAEhIgYHER4BFyE+ATcRLgEHFQYHIzUzMiEVIyYnNTY3ASM3PgEuAQcGDwEnJicmDgEWHwEjIgYdARQWFyE+ATc1NCYlPgEXFh8BJyYnLgEXIyY9ATQ3OwIWFxUGByM3NjE3Njc2HgEGBwYPAQMH/fIPFQEBFQ8CDhAVAQEVJgEGxMQG/uvEBgEBBgIOZgceBzRMIBYILSwIFiBMNAceB2YQFRUQAkkPFQEV/iYKHQwFAyZTBQQMAoniBgbiS+EGAQEG4UMBAQMFDB0TAwsEBVMBfhYP/twQFQEBFRABJA8WQuoGAff3AQbqBQEBCAYbTD4HGhIbYGAbEhoHPkwbBhUQdQ8VAQEVEHQQFWwLAwoEBlEfAgQJHuQBBTsGAQEGOwUB5gECBgQKAxcdCQQCHwAABgC0AAADSwMDAA8AHwBGAFYAYwBzAAABISIGFREUFhchPgE3ES4BAw4BKwEiJjURNDY7ATIWFwEjNzY3PgEuAQcGDwEnJicmDgEWFxYfASMiBh0BFBYzITI2PQE0JicwPwEVNjc2HgEGBwYHMQcnMyYnLgE+ARcWFzEfAQ4BKwEiJj0BNDY7ATIWFwMP/eENERENAh8NEAEBEf0BCAceBgkJBh4HCAEBD88yHRkcBzJIHhUHNjUIFR5IMQYdGR0yzw0REQ0CWw0REdYBAQQGDyQZAw8FBmnXAQYGDgMYJA8HBC9WAQgHHgYJCQYeBwgBAWoRDf7SDBEBAREMAS4NEf7DBgkJBgEQBgkJBgEADgEUGkc7BxgTGHR0GRIYBzpIGhQBDhENeQwSEgx5DRGBAQMBCAUMAx0kDQQDJycDBA0kHQMMBQdnpgYJCQZbBgkJBgAAAAMAsABCA1ACvgANABkAOgAAJRUOASsBLgE9ATQ2MhYFFQ4BKwEuAj4BFgEUDwEOASMhIiYnAy4BKwEiJjQ2OwEyFh8BITIXFhcWFQMkARwUARQbGygd/p8BHBMBFRoBGykdAY4CFgctHf6nHSwHKgILCDENERENMR4sBgYBqwQFIBUScwIUGwEaFAITHRwUAhQbARopHAEbAXEJCdUeJSUdAWAHCREaEiUdMAEEGhgdAAAAAAEArQAAA1IC/ABAAAAlBgcOASMiJy4BJyY3JwYHBiInLgI2Nz4BMhcWFzcmNz4BNzYyFhceAQ4BBwYiJyYnBxYXFgcXNjc2MhceAgYDPBMiECQSJiEZIggMEZkOESFLIRkiDwcPE0NLIRENmRIPCSAXIktCFA4IDyIZIUwhFxGVBwMOGJURFyFMIRkiDwhIIhMJChMPLBwtK1gOChITDi03OBgiJhMKDVgvMBkpDRMlIhk3OCwPExMNFVYNDjQwVhUNEhMOLTc3AAAAAAIAcP//A5MDAwAsAEEAAAEuAScmLwImJyYHBg8CBgcGFB8BBwYXFhcWMzI/ATYfARY7ATY3Ni8BNzYHBh8BJyYPATc2LwE3Nj8BFx4BHwEDkwEHBg0S2WALDRIUEgph2BIKDg6eJgMJCRUHBw0LshERsgwMCBINDAQlnQ7dFAYjuxERvCQDEZHKGQ1YWgcUC8oBxwsSCRAEH8UOCAgGBhTFIAMKDygPmtgRDxIGAgZeCAheBgMRDhjYmg6AFBjIYQkJYcgbEY0eBBi2tgwPAR4AAAAABABB/78DwgNAAAsAFwAjAD8AAAEOAQceARc+ATcuAQMuASc+ATceARcOAQMOAQceARc+ATcuAQMjFRQGIiYnNSMiJjQ2OwE1PgEyFhcVMzIWFAYCAb79BQX9vr7+BQX+vqHZBATZoaLZBATZorDqBATqsLDqBQXqAY4TGxMBjg0TEw2OARMbEgGODhISA0AF/b++/QUF/b6//fzGBNmhotkEBNmiodkDGQXpsbDpBQXpsLHp/kaODhMTDo4TGxOODhMTDo4TGxMAAAAABQCzADIDSwLKABMAJgAzAEYAWgAAJRQGByMuASc1PgEyFhcVFDczHgElFQ4BByMuATQ2OwEyPQE0NjIWNw4BByEuATQ2MyEyFjcVDgEiJj0BNCsBLgE0NjczHgElFAYHIyIdAQ4BIiYnNT4BNzMeAQGXEQ2GGyQBAREZEQEDhgwSAbQBJBuGDBISDIYEERkRAQERDP2lDRERDQJbDBEBAREZEQSGDBISDIYbJP5NEgyGAwERGREBASQbhgwSUAwRAQEkG4YMEhIMhgQBARGbhhskAQERGREEhgwSEoIMEQEBERkREfeGDBISDIYDAREZEQEBJAYMEQEDhgwSEgyGGyQBAREAAAAABgDpADQDUAL/ABEAFwApADYAQwBQAAABJyYjISIGBxEeARchPgE3ETQHIiYnNRcTFAYHIS4BNRE0NjMhFR4BOwEDDgEjISImNDYzITIWNw4BIyEiJjQ2MyEyFicOASsBIiY0NjsBMhYDR4gJDf54GB8BAR8YAfcYHwGcCQsBbQcLCf47CQsLCQEpAR8XeVsBEQz+0QwREQwBLwwRAQERDP7RDBERDAEvDBGWAREMmAwREQyYDBECbYoIIBf9pBgfAQEfGAHsDRYMCFlt/jYJCwEBCwkCKggMeRcg/vANEREaERGKDRERGhERig0RERoREQAAAAQAwgAuA0AC6gAXACMAUQBZAAAlJzY1LgEnIg4CFR4BFzI3FxYyNzM2NCcOAQcuASc+ATceAQMhIgYHDgEXEQYWFxYXFjchJyYvASMiJy4BNRE2NzYzIRUUFhceAR8BMxUfATUnJicuAT0BFwM1TB4CaVAmQzYcAmpPOC9LDB8LAQttAUw6OUwBAUw5OkyN/sgFHRINCgEBBgkOFhEHAT9HFhIZtwkDAQIDAgkEAQcGDAoYBQdYIxmJCAUBATxvTC44UGkCHDZEJVBpAh5LCwsLIL05TAEBTDk6TAEBTAGPCREPHwf9/QgfDRIGBAEXBwsTAwIJBQIADAIIPgcfDwwNAgNSGxnBAQMFAggEHzUACgDpADQDUAL/AAEAAwAFAAcAGQAlADEAPQBDAEUAAAEVNRU1FTUVNycmIyEiBgcRHgEXIT4BNxE0BTMyFhQGKwEiJjQ2ASEuATQ2MyEyFhQGJyEiJjQ2MyEyFhQGNyImJzUfATUDHSqICQ3+eBgfAQEfGAH3GB8B/imXCg8PCpcLDg4BOf7SCw4OCwEuCw4OC/7SCw4OCwEuCw4OAgkLAW0RAh0BAQEBAQEBUYoIIBf9pBgfAQEfGAHsDSUPFQ4OFQ/+nwEOFQ8PFQ6XDhUPDxUO2AwIWW0zAQAAAAMAQP/AA78DQAALABQARAAAAQ4BBx4BFz4BNy4BAz4BMhYUBiImNxUUBiImJzU2NzY/Aj4BNzY1LgEnDgEHFAYiJic+ATceARcxFAcGBwYPAQ4BBwYB/778BQX8vr79BQX95gEWIhcXIhZFERoQAQMeDBALGRAVBhEBRzc2RwIRGREBAmlQUGkCIA4SDRMKDxEGCwNABf2+vv0FBf2+vv39aBEXFyIWFoUSDRERDRUtGgsIBgoIDAcRKDREAQFENQwREQxPZQICZU89Iw8LCAgDBwkFCgAAAAQAbQBGA4YCpQAUACkAQABNAAAlBiInLgEiBgcGIiY0Nz4BMhYXFhQ3FAYiJyYgBwYiJjQ3Njc2MhcWFxYHBiInLgEiBgcGIiY0NzY3NjIXFhcWFAEWPgIuAg4CHgECjAkZCRU1OzUVCRkSCR5LU0seCfERGgmW/nuWCRkSCU9mY9djZk8JgAkZCS5zgXMuCRgTCTdHRZRERzcJ/t8NGhQGCBQbGhMHCBToCQkVFhYVCRMYCR4fHx4JGP0MEgmOjgkSGQlPKSgoKU8JmgkJLTAwLQkSGQk3HBwcHDcJGf7eAwgVGhsSBwgUGxsSAAAAAAcAiAA+A3oCwgATACAALgBLAFcAaQB1AAAlLgEHMQ4BFxYXMRYyNyM+AScmJxMeAQcBDgEuATcBPgEDJicjBzYWFycWPgE0JwMzMhcnNyYjBgc3BgczDgEXFR4BNzY/AT4BOwEwBSYvAQcWFyMWPgE0BTcnDgEHMQ4BHwEWMjc+ATcxNwcWFzEWPgEmJyYnAh8NIA0WCw0IDA0gDgEWCw0IC/IKAQj+LAkZEwIIAdQJGYksNgE1HzwaAQoWEQmBAj46AylJWHFhA1xEAQoBCQkYCT5NAyldMgEBby41Ayg3LwEKFxH+dTMgRX8zCQEIAQgZCCdfNakpNy0JGBECCSs1rwcBCA8wFwwHCAgOMBcMBwIMCRkK/ecKAREZCgIZCgH+YiEHPwYOEwEHARIYCgFNDwEyGQEqASdACRgJAQkBCTkhARITVCofAjAdKggBEhhSOwIBMC4JGAkBCQcjKAQeMBUnCAITFwkmGAABAG7//AORAwQAIwAAAQcXFgYHIyIvAQcGJicmPwEnLgE3Nj8CPgEXFh8CHgEXBgODniYDFhUHDAzDwxMnCQkDJZ0OAQ8MD9lgCiUTDglh2RIYAwEBrJnZFh8EBmZmCwwSEBDZmQ4oDwwCIMUSDgkJDMUgBCEUDwADALgARQNIAsMAGwAnADQAAAE3NjQmIg8BJyYOARQfAQcGFBYyPwEXFjI2NCcFPgE3LgEnDgEHHgEBMjY1LgEnDgEHFBYzAyMaCxYeCxoaCx4WCxoaCxYeCxoaCx4WC/5zPFACAlA8PFACAlABEQ8UA4xpaowCFA8CJBoLHRcLGRoLARYeCxoaDB0WCxgZCxcdDGIBUDw9UAEBUD08UP6cFQ9pjAMDjGkPFQAAAgCgAD8DYQLGAAMADQAAATcXBxMVITUzPwEXDwECHnCqcJn9PzxI36rtpAJWcKpw/tA9PdTsqt83AAAAAgDDAAkDNwL9ABEAIwAAAS4BBwEGBwYWFwEWMz4BNxE0BxEUDgEnASY1JjY3ATYXHgIDLBA6G/4yFQwVEyMBzhIUICkBPQUKBP4yEgELCAHOBAUDBAMC1BoPEP7pDRUlTRf+6QsBKSACWxQU/aUEBwMDARcMFQsRBQEXAwEBAwUAAAAABQC0ADEDSgLPAB8AKQBDAE8AWwAAASM1LgEiBh0BIzU0JiIGHQEjDgEHER4BFyE+ATURNCYBMjMhERQjISI1ASIjITU0OwEVFBYyNj0BMxUeATI2PQEzMhUBIyIGFBY7ATI2NCYhIyIGFBY7ATI2NCYDDGkBEBgR0xEYEWkaJAEBJBoCGRokJP3IAgMCHQT95wUCIgIC/eIFaREYEdIBEBgRagT+i0QKDg4KRAoODgEClwoPDwqXCg8PApUdDBERDB0dDBERDB0BJBr+GhsjAQEjGwHmGiT+9f7nBQUBU5MFLwwQEAwvLwwQEAwvBf7SERcRERcRERcRERcRAAAAAAgAhwBkA3wCngAQACIANwA/AFAAYgB3AH8AADceATclNjc2JiclJg4BFREUAzQ2NzIXBR4BBwYHBQYmJyY1FyInJicmNRE+ATcyFwUeAQcGBwUGAxElNjc2JicXHgE3JTY3NiYnJSYOARURFAM0NjcyFwUeAQcGBwUGJicmNRciJyYnJjURPgE3MhcFHgEHBgcFBgMRJTY3NiYntgMKBQEVBgQGBQv+6wUKBRoWEQsJARYTCwwHC/7qDh4JBicGBhkNCAEgGREOARYZDg8JD/7qDgsBCwICAwMEQwMKBQEVBgQGBQv+6wUKBRoWEQsJARYTCwwHC/7qDh4JBicGBhkNCAEgGREOARYZDg8JD/7qDgsBCwICAwMEngYDA8sFBw0aCMsDAwkE/koFAbsUGgEHyw8zGA4JygoJEAwNQgEHGQ8SAbYcJQELyhNDHRILywoB7v5dxAEDBw4D8QYDA8sFBw0aCMsDAwkE/koFAbsUGgEHyw8zGA4JygoJEAwNQgEHGQ8SAbYcJQELyhNDHRILywoB7v5dxAEDBw4DAAAABgBY/84DqAMyABIAGAAwAEYASgBPAAABGwEOAS8BIyImNRE0NjczNzYWBwUjFTMFEz4BFx4BFRQGBwYuATY3PgE1NCYnLgE3Bx4BFRQGBwYuATY/AT4BNTQnJjQ2MiUhESETARMHAQJwAwIDRiHzgRskJBt69SJFPf75kpkBBekJGQstMDcyCxkQAgonKyYjCgEINxYYHxwKGhECCgEREhsKFBn9nAEa/uaEAT8nJf6/Aun+mf6WKiAb2SQbAP8bJAHYGh8r6P/pAjsJAQgqbT1CcykIAxUYCSBaMzBVIAkZC2UXNx8kPhcIAhUZCQEOJBUkHAkaExr+ywELARn9oqoBGAAIAIMAZAN3Ap4AEAAiADcAPwBQAGIAdwB/AAABLgEHBQYHBhYXBRY+ATURNBMUBiMiJyUuATc2NyU2FhcWFScyFxYXFhURFAYjIiclLgE3NjclNhMRBQYHBhYXJy4BBwUGBwYWFwUWPgE1ETQTFAYjIiclLgE3NjclNhYXFhUnMhcWFxYVERQGIyInJS4BNzY3JTYTEQUGBwYWFwNIAwoF/usGBAYGCgEVBQoFGhYRCwn+6hMLDAcLARYOHwkFJwYGGQ4HIRkRDv7qGQ0OCQ8BFg4L/vUCAgMDBEMDCgX+6wYEBgYKARUFCgUaFhELCf7qEwsMBwsBFg4fCQUnBgYZDgchGREO/uoZDQ4JDwEWDgv+9QICAwMEAmQFAwPLBAcNGwjLAwMJBQG2BP5GFBsHyg80GA4IywoJEQsNQgIGGRAR/kocJgrLE0IdEgvLC/4RAaPDAgMHDQPxBQMDywQHDRsIywMDCQUBtgT+RhQbB8oPNBgOCMsKCRELDUICBhkQEf5KHCYKyxNCHRILywv+EQGjwwIDBw0DAAAABQEYAGQC4wKlABAAIgA3AD8ATwAAJR4BNyU2NzYmJyUmDgEVERQDPgE3MhcFHgEHBgcFBiYnJjUXIicmJyY1ET4BNzIXBR4BBwYHBQYDESU2NzYmJxMjIiYnET4BOwEyFhcRDgEBRwMKBQEWBgQGBgr+6gUKBRoBFhALCgEVFAoLBwz+6w8eCQYoBwYZDQgBIRgRDwEVGQ4OCRD+6w8LAQsDAQMCBWEDDREBARENAw0RAQERngYDA8sFBw0aCMsDAwkE/koFAbsUGgEHyw8zGA4JygoJEAwNQgEHGQ8SAbYcJQELyhNDHRILywoB7v5dxAEDBw4D/twRDQH+DRERDf4DDhEAAAAAGAC3ADcDSALIAA8AEwAjACcANwA7AEsATwBfAG8AfwCPAJMAlwCbAJ8ArwC/AM8A3wDjAOcA6wDvAAABIyImJzU+ATczHgEXFQ4BJzM1IwUjIiY9ATQ2NzMeARcVDgEnMzUjEyMiJj0BNDY3Mx4BFxUOASczNSMHIyImJzU+ATczHgEXFQ4BJzM1IwUjIiY9ATQ2OwEyFhcVDgEDIgYdARQWFzM+ATc1LgEjASMiJj0BNDY7ATIWHQEUBgMiBh0BFBYXMz4BPQE0JiMFIzUzBzM1IwcjNTMHMzUjJSMiJj0BNDY3Mx4BFxUOAQMOAR0BFBY7ATI2NzUuAScBIyImPQE0NjczHgEdARQGAw4BHQEUFjsBMjY9ATQmJwUjNTMHMzUjByM1MwczNSMBrccSFwEBFxLHERcBARfQtrYCK8cSFxcSxxIXAQEX0La2vscSFxcSxxIXAQEX0La2rscSFwEBFxLHERcBARfQtrYCK8cUGhoUxxQaAQEa2w8VFQ/HEBQBARQQ/pTHFRoaFccUGhvbDxUVD8cQFRUQAWrAwLasrLfAwLasrAImxxQaGhTHFBoBARrbDxUVD8cQFAEBFBD+lMcVGhoVxxQaG9sPFRUPxxAVFRABasDAtqyst8DAtqysAakXEscSFwEBFxLHEhcytugXEscSFwEBFxLHEhcytv2rGBHHEhcBARcSxxEYMrboGBHHEhcBARcSxxEYMrbtGhXHFBoaFMcVGgEaFQ/HEBQBARQQxw8V/uYaFccUGhoUxxUaARoVD8cQFAEBFBDHDxXowLastsC2rIUaFMcUGgEBGhTHFBoBGgEUEMcPFRUPxxAUAf7mGhTHFBoBARoUxxQaARoBFBDHDxUVD8cQFAHowLastsC2rAAFARgAZALjAqUAEAAiADcAPwBPAAABLgEHBQYHBhYXBRY+ATURJhMOASMiJyUuATc2NyU2FhcWFycyFxYXFhURDgEHIiclLgE3NjclNhMRBQYHBhYXAzMeARURFAYrASImJxE+AQK0AwoF/uoGAwcGCgEWBQoFARsBFhALCv7rFAoLBwwBFQ8eCQUBKAcGGQ0IASEYEg7+6xkODgkPARYPCv71AgEEAwRgAw0REQ0DDREBARECawYDBMoFBw0bCMoEAwoEAbYE/kYUGwfLDzMYDgjLCgkRCw1CAQcZEBH+ShwlAQrLE0IeEgvLCv4SAaLDAgMGDgMBJAERDf4DDRERDQH9DREAAAABAMMACQM3Av0AEQAAAREOAQciJwEuATc2NwE2FhcWAzcBKSAUEv4yIxMVDBUBzhs6EAsCrv2lICkBCwEXF00lFQ0BFxAPGhIAAAAAAQDJAAQDPgL4ABEAAAEGBwEGJicmNRE+ATcyFwEeAQMpDRX+Mhs5EQsBKh8VEQHOJBMBTRUN/ukQDhsRFQJbHyoBC/7pF04AAwBC/8ADwQNAABcALwBlAAABLgEnJiIHDgEHBhQXHgEXFjI3PgE3NjQBBiInLgEnJjQ3PgE3NjIXHgEXFhQHDgEDFTMeARQGKwEVFAYiJj0BIyImNDY3MzUjIiY0NjczJyY0NjIfATc2MhceARUGDwEzHgEUBiMDniJ8UFS2U1B8IiMjInxQU7dTUHwiI/7YSZ5IRmseHh4ea0ZInklGax4eHh5rwJMMEREMkxEZEZMMEREMk5MMEREMgpcIERcInaEJGAkEBQEImX8MEREMAi9QfCIjIyJ8UFS2U1B8IiQkInxQU7b+Px8fHWxGSJ5JRWweHh4ebEVJnkhGawFBSQEQGRFRDBEQDVERGRABSREZEAGWCRYRCJ2iCAgECwYMCZkBEBkRAAAcAK4ANQNFAs0ADwATACMAJwA3ADsASwBPAF8AbwB/AI8AkwCXAJsAnwCvAL8AzwDfAOMA6ADsAPEA9QD5AP0BAQAAASMuAT0BNDY7ATIWFxUOASczNSMFIy4BJzU+ATsBMhYdARQGJzM1IxMjIiYnNT4BOwEyFh0BFAYnMzUjByMiJj0BNDY7ATIWFxUOASczNSMFIy4BJzU+ATczHgEdARQGAyIGBxUeATsBMjY9ATQmIwEjLgE9ATQ2NzMeARcVDgEDIgYdARQWOwEyNj0BNCYjBSM1MwczNSMHIzUzBzM1IyUjIiYnNT4BOwEyFh0BFAYDDgEHFR4BFzM+AT0BNCYnASMiJj0BNDY7ATIWFxUOAQMOAR0BFBYXMz4BPQE0JicFIzUzBzM1IxUHIzUzBzM1IxUnMxUjJTMVIxUzFSMlMxUjAabJEhgYEskSFwEBF9O4uAIxyRIXAQEXEskSGBjSuLjAyRIXAQEXEskSGBjSuLiwyRIYGBLJEhcBARfTuLgCMckUGgEBGhTJFBsb3RAUAQEUEMkQFRUQ/pDJFRoaFckUGgEBGt0QFRUQyQ8VFQ8BbcLCuK6uucLCuK6uAizJFBoBARoUyRQbG90QFAEBFBDJEBUVEP6QyRUaGhXJFBoBARrdEBUVEMkPFRUPAW3CwriurrnCwriurhTq6gFn6urq6v6P6uoBqwEXEskSGBgSyRIXMbnrARcSyRIYGBLJEhcxuf2lFxLKERgXEskTFzK46hcSyhEYFxLJExcyuPABGhTKFBoBARoUyRUaARwVEMkPFRUPyRAV/uMBGhTKFBoBARoUyRUaARwVEMkPFRUPyRAV6sK4rrjCuK6GGxTJFBsbFMkUGwEdARQQyRAUAQEUEMkQFAH+4xsUyRQbGxTJFBsBHQEUEMkQFAEBFBDJEBQB68O5r64Lw7mvrsLNzc2qzMzMAAAAAAoAgwBhA3oCmwAQACIANwA/AFAAYgB4AIAAjACYAAABLgEHBQYHBhYXBRY+ATURNBMOAQciJyUuATc2NyU2FhcWFScyFxYXFhURDgEHIiclLgE3NjclNhMRBQYHBhYXJy4BBwUGBwYWFwUWPgE1ETQTDgEHIiclLgE3NjclNhYXFhUnMhcWFxYVEQ4BByInJS4BNzY3JTYzAxEFBgcGFhclPgE3HgEHFgYHLgElPgE3NhYVFgYnLgEDSQMKBf7qBgMHBgoBFgUKBRoBFhALCv7rFAoLBwwBFQ8eCQYnBgYZDQgBIRgRD/7rGQ4OCRABFQ8L/vUDAQMCBUMDCgX+6gYDBwYKARYFCgUaARYQCwr+6xQKCwcMARUPHgkGJwYGGQ0IASEYEQ/+6xkODgkPARYPEAX+9QMBAwIFAUsJvD81JwECEDNB0v59EMUvMSoCETI53gJhBgMEygUHDRsIygQDCgQBtgT+RhQaAQfLDzMYDgnKCgkRCw1CAQcZEBH+ShwlAQrLE0IeEgvLCv4SAaLDAgMGDgPxBgMEygUHDRsIygQDCgQBtgT+RhQaAQfLDzMYDgnKCgkRCw1CAQcZEBH+ShwlAQrLE0IeEgvLCv4SAaLDAgMGDgMHUpoGBppTUYsEBItSRJsTC6lVU5IFDIwAAAAACgCFAGEDfAKbABAAIgA3AD8AUABiAHcAfwCLAJcAADceATclNjc2JiclJg4BFREUAzQ2NzIXBR4BBwYHBQYmJyY1FyInJicmNRE0NjcyFwUeAQcGBwUGAxElNjc2JicXHgE3JTY3NiYnJSYOARURFAM0NjcyFwUeAQcGBwUGJicmNRciJyYnJjURNDY3MhcFHgEHBgcFBgMRJTY3NiYnBQ4BBy4BNyY2Nx4BBQ4BBwYmNyY2Fx4BtwMKBQEVBgQGBgr+6gQKBRoWEQsJARYTCgsHC/7qDh8JBScGBhoNByEZEQ4BFhkNDgkP/uoOCwELAgIDAwRDAwoFARUGBAYGCv7qBAoFGhYRCwkBFhMLDAcL/uoOHwkFJwYGGg0HIRkRDgEWGQ0OCQ/+6g4LAQsCAgMDBP61CrtANCcBAxAzQdIBhBHFLjIqAQMRMzjemwYDBMsEBw0bCMoEAwoE/koEAboUGgEHyw8zGA4IywoJEQsNQgEHGRARAbYcJQEKyxNCHhILywoB7v5ewwIDBg4D8QYDBMsEBw0bCMoEAwoE/koEAboUGgEHyw8zGA4IywoJEQsNQgEHGRARAbYcJQEKyxNCHhILywoB7v5ewwIDBg4DBlOaBgaaU1GLBASLUUWbEwupVVOSBQyMAAAAAwCRAHwDbQJ/ABEAIAA9AAABISIPAQYUHwEWMyE+ATcRLgEDFCMhIi8BJj8BNhchMhUHJiIPAScmIgYUHwEHBhQWMj8BFxYyNjQvATc+AQMr/lsdFLMREbMUHQGlGyYBASYYA/5bAgKyAgKyAgIBpQOfCBQJNDMIFBAIMjIIEBYIMzIJFhAINjIIAQJ/FcATMhTAFQEmGwF/HCX+PwQCwAMCwAMBA2kICDQyCBAUCDMzCBQQBzMzBxAUCDMzCBYAEgCAAGQDgAMPACUAPQBYAGUAcgCAAKUAyQDiAPgBFwExAT4BSwFdAWwBfAGNAAABMjMyHgIfAh4BHwEVFA4CJyEGLgI9ATQ+Aj8BPgMzBQ8BDgEiJi8CFRcWFzMWHwEhMj4BNTcDISIOAQ8CMzIWFR4BMjY1PgE7AS8BLgIjAx4BFxUOASImPQE0NhcyHgEPAQYiJjQ/ATYlMh8BFg4BIyIvASY+AQMjBi4CPQE0PgI/AT4DMyEyHgIfAh4BHwEVFg4CJwEiDgIPAg4CHQEUHgI3IRY+Aic1Jy4BLwIuAyMTIScmLwEmLwIzFx4BMjY/AhUXFA4CJSE+Ajc1IxUOASImJzUjFRYXMRYXJS4BNS4BKwE3Fyc/AT4BMzchMRYXFh8CIw4BFQ4BJTMyFhcUFjI2NT4BOwEvAS4BLwEhByIGDwE3IiY9ATQ2MhYXFQ4BJw4BHQEUFjI2NzUuARcjIiY1ND8BNjMxMh4BDwEGBzciDwEGFBYXMTI/ATYuAQUiLwEmNjc2MzIfARYGBwYnIg4BHwEWMzEyPgEvASYjMALKAQEDGhoRAlIGBAYBAQcdHwb9mgUeHAcBCQkCRAIPGBgFAiPRAQtBVkELAc4BAQIBAwUHAmUECwYBfv5bAwkGBAVF2QoPASg+KQEOC9dKBwQIDATNCw4BAQ4WDg7vCQ8CBjoIFw4GOgf+SAwIOQYCDwkMCDkGAg9DBgUhHQgBCgkCRAIQGRoFAagEGxsSAlEHBQYBAQEJHyAH/fMFFhcPAUQGBAkBBxkdBAJmBh0aCAEBAQYEBlECEBgZA2X9mwgGBAIDAQEB2AIKP1M+CgPZAQMIDP2WAmUECQQByQtEWkMMxQECAgMBOSEsAQsJ4AUBAUMHAwkFCAGqCQkFBAhP4AkMASv+rs8NEQEmOiYBEQ3ORQcEBwUJ/lsHAwUDBfANEREaEQEBEQ0JCwsSCwEBC6YFDREHOgkOCxIDCDoHDDUJBzoECwkKBjoFAQ3+bg8JOQgDCggLDgo5BwIKCEQHDAIFOQYKBwwCBTkGCgIhBQ8RA4IMChcKDYcHHBkHAQEHGR0EhwQZHhMDdwMSDwTuAQQpMjIpBAGHBQQCAgECAwUDBAFDAgQDB3kOCx8nJx8LDnUIBAUDAR0BDguSCg8PCpILDkkMFQhICQ8TB0gJAglICRQMCkcJFAz9ogEIGx4FhwUZHxMDeAMTEAQFEBIDgA8KGAoNiAgdHAcBAbcDDxECeAwJHRgEhgQbFwcBAQYYGQeHDQkXCQyBAhINBf57AgEDAQQEBowJJzAwJwgBB4YBCAcECgECBQOCASs0NCsBgQUDAQFdASohCQsKAQF3BwUFAQEEAwUIfQELCSEqaRENHSUlHQ0RbgcEAwIBAQMEBmYSDJINERENkgwSxQELCZIIDAwIkgkLwxENCwlHCw8YCkgJAnoHSAUQCwEIRwcQCngLSAoYCQYLSAoYCAd6ChAHRwgKEAdIBwAABgCr/+8DVgMPAAwAGQAzAEcAUABZAAABDgEdARQWMjY3NS4BJQ4BHQEUFjI2NzUuARcRHgE7ARUeATI2NzUzFR4BMjY9ATMyNjcRJzc2Jg8BJiIHJyYGHwEOAQchLgEHLgE0NjIWFAYXLgE0NjIWFAYDJRQcHCkbAQEb/aEUHBwpGwEBGzkBFRArARsqGwFLARspHCwQFQFzKAMKBCorXykqBAsEKDA9BQGxBT3yCg4OFA4OsAsNDRUNDQITARwVwRUbGxXBFRwBARwVwRUbGxXBFRwB/qAQFmsVHBwVa2sVHBwVaxYQAWC3OgYHBT0RETwGBwY6F08xMU9FAQ0VDQ0VDQEBDRUNDRUNAAUAQP/AA78DQAALABsAOgBNAF4AAAEOAQceARc+ATcuAQEOAQcjLgEnNT4BOwEyFhcBFAYrARUOASsBIiYnNSMVFAYrASImJzUjIiY1ESERAyM+ATcnNxc2NzMWFzcXBx4BFxMOAQcjLgEnNT4BOwEyFh0BAf69/AUF/b6/+wUF/f5gARIMFA0RAQERDRQNEQEBpgsHRgERDRQNEQFMEgwUDREBMgkJAYq+zgw5JysFNR8lDyMeNgUtKTsMbwESDBQNEQEBEQ0UDBIDQAX9vr79BQX9vr79/fANEQEBEgzMDRISDf7TBwpyDBISDHJxDRISDXEKBwFM/rQBZSxFE0MFQQ4DAwxDBUUVRC3+/A0RAQESDMwNEhINzAAACQBA/78DwQNAAA0AGgAlADEAPABFAFEAagB2AAAlIycmPQEHFQYWFzM1KwImBgcGHgIXMzUnFSMGJicmPgI3MxMOAQceARc+ATcuAQc+ARceAQ4BJy4BJx4BFAYiJjQ2ByY2NzIWFxYGBwYmAQYmJy4BBiYjBiYnJjY/ATY3PgEXHgMnIiY3JjY3HgEHFgYCSCgDAi0BDhhLFogqAicMAwcDFRhCFyEBFAYCAwILCyNBv/0FBf2/vv0FBf2jBC0ZGCEIKCEhFVgZIiIzISGgARUjHiUDAwsqLCQByx9zBgE3TjcBAzoQBz8KHBYVFVwrIEgIIh4vFwIBFicmHQEDD8sDAgdUAV0CFQJxAg0gFhQGFQGqAZQBCBEIDggNAQIxBf2+v/0FBf2/vv3pGzMDBjI4MwMFMXkBMEgwMEgwyAQ+CCsUD0cNBkL+8DULBQEJEAECFjY5PgUZFCAkETwsQAI7djIaHDUCAzIPEUUAAAAIAIr/8QN0AwkACwAXACEALABGAFQAYgBuAAABPgEnLgEHDgEXBhYXDgEXHgE3MzUjDgETPgE0JicOARQWFxY+ASYnJgYHBhYTLgEnJgYHDgEHDgEXHgE3Mj4BFhceATc2JgUjLgEnLgE3PgEXMzUzEyMuATc1MxUeATsBNTM3NiYnDgEXBhYXPgEA/z4RBgQ3KzMfAgg1nQIFBAkiATExEBdDJTExJSUxMfUwOwwwJCRCBgUfzQVpMD+GIB5BCA5dCxhaBQFKc1ACCacuHTL+03skHAEDCwUSNQNJMcV7IxACMQQTAjExxAEqODghAQIhRUQVAWwTZhYcPgEKWgYoXqkGFw0ZBgFjBg8BVgFFZkUBAUVmRQkDSFFGCgRKJixH/s4CW0BVGTIuOAgIWFFOKAMHFgwCBxBMUlWhAyQGBCchMRsBSf7zBCcDlnsKDpOzFkcEA0soJkcBB2MAAAAABQCoAC4DVwLiAC0AMwA5AFgAkAAAASImNDY7ATUuAScjJyYGDwEnIyYjIgYPASMOAQcVMx4BFAYrARUeARchPgE3NQE+AR8BJwc2Nx8BIwEUBiMhIiY9AT4BNzYmJzU0NjMhMhYdAQYHBhQXFhcnDgEHIxUUBiImPQEjLgE0NjsBNSMiJjQ2NzMnJjQ2Mh8BNz4BHwEWFA8BMx4BFRQGBxUjFTMyFgNHFh0eFRABKx8W8B04ERIfBAcIGicICRcgKgEPFh4eFg8BKiACGR8rAf59AwsFZoN8AwsEaoEB0QgG/ecGCBQfCBAiKQgGAhgGCR0TExQSHacBEg07ExsSOw0TEw07Ow0TEw0UKgoTGQpFRAkaCQEJCSsUDhINCkM6DhIBDx4rHocgKgGLDw8cIAkBHhohASoghwEdLB2WICoBASoglgGKBQMDOyQtCgEBHP4xBggIBmIKIBYrUBRTBggIBlMNGx1GHRoNJg4SARgNExMNGAESGxMGExsSASwJGhQJRkUJAQkBChoKKwETDQsRAwEGEwAABwCoAD0DYALEAA4AGgAwAD0ASgBXAHgAAAEiMw4BBx4BFzE+ATcuAQMuASc+ATceARcOATcUBisBIiYnNDc1NDYzMR4BFxUzHgElFAYrASImNDY3MzIWNw4BKwEiJjQ2NzMeAQcOASsBIiY0NjczHgElFRQGIzEiJi8BNSERMzIWFAYHIyImJzURNDY/ASEeARcClwEBVnECAnFWVXEDA3FWPFABAVA8PVABAVAqEQxiDRABARAMDRABRQwR/qkQDWIMERANYgwRlAERDPUMEREM9Q0QkgEQDWIMERANYg0QAVcRDAsQAgH+IdcMEQ0L4RUeAxoVBwHpFR4DAc8CclVVcgICclVVcv6rAVE8PFACAlA8PFF0DBERDAQDXAwRARAMRQEQVQwRERkQARK4DBERGRABARBvDBERGRABARDQkwwSDgoGjv3vEhcQAhsVBgIbFR0DAQEaFQAAAQCpACwDVgLXABsAAAkBNjQmIgcJASYiBhQXCQEGFBYyNwkBFjI2NCcCLgEcChQaCv7k/uMKGhQKARz+4goUHAoBHQEcChwUCgGAARwKGhQK/uQBHwoVGQv+4v7jChoTCQEc/uQJExoKAAAABACr//UDUwMPABMAIQA0AD0AAAEiJi8BBw4BKwERFBYfATc+ATcRAw4BDwEnLgEnNTY3FhcHJgYHBhYXFRQWMjY9AT4BNzYmBzQ2MhYUBiImA1BwpzgCAjincANQR729R08BPgFBOpqaO0ABqmxrq/kwTw4LLy0SGhIeKwgLL4EeLR4eLR4CnTg4AgI4OP7wVpQxfX0xlFYBEP7wRnkoZmYoeUbUDFBQDDcLLy8vUA2ODRISDY4ILB4wT2IWHh4tHh4AAAkAsQAfA0sC4AAUACEAKwBGAGIAhACQAJwAqAAAASM1LgEnIQ4BFREUFhchPgE3ES4BAyEiJicRPgEzITIWFRMjETMyFhURFAYBIi8BFRQGIiY9ATY/ATIfATU0NjIWHQEUByIzIiYnNTQ2OwEyFgYrARUzHgEGByMVMzIWFAYjMyIxJi8BJjY3MxYfATc2Mh8BNzY3Mx4BDwEGByMiLwEHBgciJjQ2MyEyFhQGIwUiJjQ2NyEeARQGIwUiJjQ2MyEyFhQGIwL0GwExJP6EJDIyJAHtJTEBATF8/moLDwEBDwsBfAsPVxsbDA8P/mIIBSEJDQoBCgUIBSEJDQkLAjQGCQEKBj4JCQkJLi4JCQkJLi4HCQkHNAEMAg4BCAYDDAMFCwQWBAwEAw0CBggBDgIMAQsEEhIE8QgLCwgBNggLCwj+yggLCwgBNggLCwj+yggLCwgBNggLCwgCImclMQEBMSX97SUxAQExJQFVJTH+OhALAhMLEBAL/dIBiw8M/qsLEAGPBzIqBgkJBl0LBAEHMikHCQkHXQsECQZdBwkQDw8BDw8BDwkNCQILXQcKAQEMHyIJCSIfDAEBCgddCwIKMzMKmg4VDg4VDmEOFQ4BAQ4VDlkOFQ4OFQ4AAQE0//QC0QMOABQAAAEVIyIGHQEzByMRIxEjNTM1NDYzMgLRSyocjBN5knp6Y1NGAwl+IyJbjf6WAWqOaFhiAAIAPv+/A8ADQQALAB8AAAEOAQceARc+ATcuAQMjESMRIzUzNSY2NzMVIyIGFxUzAf++/gUF/r6//QUF/UtJZUNDAi5BSy8ZDAFTA0EF/b+//QUF/b+//f5E/vMBDV03OUABXRMSLwAABgC/ACQDRALfABIAFgAaACYALgA2AAABESM1IQ4BBxEzFSMiJjURPgE3ByEVIRUzFSMlDgEHHgEXPgE3LgEXDgEHJic3Fgc+ATcWFwcmAv4+/rQzRAHD2REWAmZNJAEc/uSOjgE/TWYCAmZNTWYCAmYrAUQzLCCvFfABRDMlHasOAt/+3OcCQzP+ST0WEQHNTGcCtT0/PBwCZk1NZgICZk1NZrMzRAEBGqEdJzNEAQESnhoAAgCrAEYDUwK9ABEAIwAAJSInASY0NwE2HgEGBwkBHgEGBSInASY0NwE2HgEGBwkBHgEGAzUMCP7FCQkBOwkYEQIJ/t0BIwkCEv7CCwj+xQoKATsJGBECCf7dASMJAhJGCAEeCRkJAR0JAhIYCP74/vgIFxMBCAEeCRkJAR0JAhIYCP74/vgJFxIAAAACAKoARQNSAr0AEQAjAAATNhcBFhQHAQYuATY3CQEuATYhNhcBFhQHAQYuATY3CQEuATbJCwgBOwkJ/sUJGBECCQEj/t0JAhIBPgsIATsKCv7FCRgRAgkBI/7dCQISArwBCf7jCRkJ/uIJAhMYCAEHAQgIGBMBCf7jCRkJ/uIJAhMYCAEHAQgJFxMAAAEBsf/BAk4DPwAXAAABFAczESMWFQ4BIiYnNDcjETMmNT4BMhYCTgEBAQEBLEMsAQEBAQEBLEMsAvEFBv00BgUhLSwiBQYCzAYFIiwsAAAABQC0AA0DUwLMACUAMgA/AEwAWQAAAREOAQchLgEnET4BNzMVIw4BBxEeATMhMjY1ETQmJyM1MzIWFxYDFAYjISImNDYzITIWAxQGKwEiJjQ2OwEyFhMUBiMhIiY0NjMhMhY1FAYHIS4BNDY3IR4BA1MCPC3+Ny09AQE9LV1dExoBARoTAckTGhoTWFgWKA8ddRIN/poMEhENAWYNEnwSDXUNERENdg0RfBIN/poNERENAWYNEhIN/poNERENAWYNEgJm/g0rOQICOSwB8is6AT0BFxH+DhIXFxIB8hEXAT0QDx7+WQ4REhoREgG6DhERGxER/qcNEREaEhJuDREBAREaEQEBEQAAAwDAABEDOwLnAAsARABOAAAlIS4BJxEFMyURDgEnMzI2NCYnIzU3Njc2JyYiBwYPAScuAQYWHwEHIw4BHgE7ARUjIg4BFjsBFR4BMjY9ATMyNjQmJyMDIyU+ATchHgEXAtr+SCk4AQElMQElATfsRw0MDQxHHRkOFQwHFAkLGh05CBcQBgZCAUkJDAELCUlNCAsBCwlNAQ8WDksJDA0NRgEx/tsBNyoBuCk3AREBNioB42Fh/hwpNvIMEBMBER0aDBIPBwcKFxs6BwcSEwZEEwEUEAo0DRAQGQwMDgkaDxIMAQFUYSk2AgI2KQAEAMcAEwM6Au0AEgAdAFIAbgAAASEOAQcVFBYXBRc3JT4BPQEuAQ8BJzU0NjMhMhYXAxUzMhYUBisBFRQGIiY9ASMiJjQ2OwE1IyImNDY7AScmNDYzMTIfATc2MhYUDwEzMhYUBiM3EQ4BByEuAScRNDYyFhURHgEzITI2NxE0NjIWAuj+MSIvAQ4MAR0GBgEXCw4BLg75/wsJAc8JCwHeLw0REQ0vERoSLw0REQ0vLw0REQ0dMwkSDQ0JNzoKGBIJNR4NEREN7AEuI/4xIy4BEhoRAQsJAc8JCwERGhIC7QEvIk4LEQIzAQEzAhELTiIvhS4uNAgMDAj+eRASGhEvDRISDS8RGhIQERoSMwkYEwk3NwkSGgkyEhoR0v5/Iy4BAS4jAYENEhIN/n8JDAwJAYENEhIAAAAMAD//wQO+A0AACwAZAFEAdACAAIwAowC6APwBPAFrAZUAAAUuASc+ATceARcOAQMOAQceARcyPgI3LgEDIicmPQEmNzY3MzIeAR0BNz4BFzMyFhcWFxYdARYHBisBIicmPQE2Jy4BKwEiBgcGFxUWBwYjJyUmJyY3NTQ2NzY7ATIWFAYHIxUzHgEOASsBFTMyFhQGKwEmEy4BJz4BNx4BFw4BAw4BBx4BFz4BNy4BAyInLgEnJjQ3PgE3NjIXHgEXFhQOAgMiBw4BBwYUFx4BFxYyPgMnLgEnJhMjIiYnJj0BNicmKwEiBzEGBwYXFRYHIwYrASInMSY9ASY2PwE2OwEyFhcxFh0BNz4BOwEyFhcWFzEWHQEWBzEGIycVFBcxFjsBFjc2JzU0JyYnMSYnJisBIgcxBg8BNTQuASsBIgcGHQEWFxY7ATI3Nic1JjczNjc2OwEyFh8BFg8BIyciLwEmNzU0NzE2NzY7ATIXMRYUBzEGKwEVMzIXMxYGBzEGByMVMx4BFAcVBiczNjc2NCYrATUzMjY9ATQnJisBNTMyNzY0JisBIgcxBgcGHQEGFxYzMgH/vPoFBfq8u/sEBPq8ptwFBdymTo5uOgEE3FoJCAcBBwYIAgcKBwkNJhQDFSQHBQICAQgFDAIKBwcBCQYSCgQTGwYGAQEIBgsB/vsNCgkCCAUJCbIJDQ0Kmo8JDgINCY6fCwwNCrIDuL79BQX9vr39BQX9vbr4BQX4urr3BQX3ulFJR20eHx8ebUdJoUlHbR4eO3CPT09IRWoeHh4eakZHnIxtOgEfHWpFSJgDBQsECAEJCxMFEQsOBQYCAQkBCAwBCwkIAQQDAQcKAggMBAQCDyQXAxUmCQYBAwEJBw4VBgQJAggGBwECAgUIDxITAxQQEQ4SBQkFAgYEBgEGBQgBCAUIAQIGAQUQDhQFCxQGAQoB07QDDgsBCgIEBQcKCbIKCQgICQuVigoJAQcBCAYMipoNDgcJu7AHBwUKCKSTBgsEBgeTnwcGBQsIsQgIBAMDAggJCgM6Bfq8vPoFBfq8vPoDPATcpqbcBDpvjU+m3f3dCAkNrgwLBQEHDAkZDRMTARMRCgsPEHANCwcICQ1lGBgJChYSGhlLDQsHAQQBCAsP8gsRAgMLEgsBWQELEQtmCxILA/7bBf2+vvwFBfy+vv0DcQX3urr4BQX4urr3/MQfHm1HSqBJR20eHx8ebUdKoI9wOgMMHx1rRUicSEZqHh46boudR0ZrHR792QQFCg9lFRkQCQsQHRVKDA8JCQoQrQcMBgEHCAYJCQsCFRIUEwsMDxBvDQ4Jh2UMCAUBBgwJcA8PCgoRCAkICBQYJwcLBQQJCq8MBwYFDAlKFx4TDAwLCgEbF4MDCQEMEfIMCgkDBAcIFAgHUQcIEwcGAV0BDhQHAQYIAQQFDQlvCQcBBgMEYwUFDAoDAgYICvIOCQcAAQCAAEcDfgK5ADUAABMhMhcVFAcOARUUFxYXFjI3PgE1NCYnIic1NjsBMh0BFCMOAQcOAQcxBiInJicCIyYnNzQ7AYcBSAQBBBwWJyZFAQUCRzccHgMBAQT9BAQkMiEtbjkQOw8eNsA8BAEBBAECuQQUBAEEEhQcW1iDAwOfkBwXFgQEFAUFFAQEKUFX74gZGT9vAY4BBBQEAAAABACPACoDbgLUABEAIQAqADcAACUBLgEiBgcBBhQeARchPgI0ByEmJyY0NwE2MhcBFgcGIyUUFjI2NCYiBjcyNj0BNCYiBh0BFBYDYf7vDCszLA3+7w0aKxkCIxorGV793RIKBQUBEQsjCwERCgoKEv7LFR4UFB4VJAsPDxYQELwB6hYYGBb+FhYzLhoBARouMz8BEQkTCQHqDw/+FhEUEWYPFBQeFBRTDwugCw8PC6ALDwAAAQB2AAgDigL2ADgAAAEuASMuAScOAQciBgceARcOARcyNjceATc2JicmBgcGLwE3JyYnLgE3PgEXHgEXBxcWFxY2Nyc+AQOKFuoQBl0XF10GEOkXB7ENBDEHGM0ODswaBh0OA2xMRTg74ionKztQBA1yRkFhA+MeHCE+bAQGDbAB2RAXENcPD9cQFxAanAoQ5hh3CQp5Aw+aPgEKAQEDA6gHBgUFAgIDBwIDDQGhBAQBAwIBHAqcAAIAPv+/A88DQAATAF8AAAEOAwceARceATM+ATc+AScuARMGDwEXIicmNzY3Njc2JicmByYGBxYXFhUUDwEGFRY/AQcfARYdARQGJi8CBwYnJjY3NTcvAS4BJz4BMz8CNjc2HwEeAR8EAf5cpHlGAQJ3ZTF0PV+gPlNBICrpeAIVdARYMiEDAQtJMhwJFAUCV6EJjykUESdueosqJxQCAQQRCQiElwsGAgICHHQDBA8BAhUKDJNBDgYDBAQDAwIBQqQaA0ABRXqlW37JPB4gAUU+VOZ6krT+hgkRahUCAQsEBzYdEAsBAgEHDQQHBAEEAQocTQkSFgYUcAcGBwgFBAcGBE5YBgEBFggDnmgCBg4GAwUBE4EgAQEGBgUKAwGEFAUAAAACALYASQNKAs4AKgA2AAABIjEvAg4BFBY7AQ8BLgEjDgEHHgEXPgE3NCYnNj8BFRQWMjY9AS8BJicDDgEHLgEnPgE3HgEDPgEDBrsMDxALcYQEIlcxbZIDA5JtbpEDGhkDA4UQGBACBAICvgJyVlVzAgJyVlZyAskCAgEBDxgQhQQeIgORbm2SAwOSbStOIQEDhXEMEBAMtwcIAgL+hFZyAgJyVlZyAgJyAAAACQA//74DwQNAAAsAFgAhACwANwBCAE0AWABkAAABDgEHHgEXPgE3LgEHFhcWFxYfAQc2LwE2MzIfARUmLwE2BzY3Nj8BFyYjBzYHJjU0PwEzBg8BJhcmJyYvATcGFxUmFwYjIi8BNRYfAQY3BgcGDwEnFjM3BjcGByM2PwEWFxYVBgIAv/0FBf2/v/0FBf1SIB0LCxkRCIUBAYUPDyQgEQ9PWSCNCQsZHQ+FFW9+DSACCQW8EE5ZDV4MCxkQCIQBASClDw8jIRAOT1ohjAkKGh0PhRZufw4aAgK8D09YDgQCAQNABf2/v/0FBf2/v/3DDhcKChoeD4cVcZQBCQW/EFBaDWAMCxkRCYgBASGoDw8kIhAPUFshjwkLGR4QhxZxgA4jAQkFvxBPWw1fDAsZEQmIAQEhRQgIDlBcISUPECQAAAAACABA/74DxQNCAAQACQAOABMAGAAdACIAJwAACQEmNjcXAy4BJzcBBiYnNwUOAQcnARYGBycTHgEXBwE2FhcHJT4BNwGW/r4UBiHTD1JXHu8BLV+CTH8BujZhTDsBQxQHIdMPUlce7/7TX4JMf/5HNWFMAi/+02CBTH/+RzVhTDv+vRMGIdQQUVge7wEtX4JMgAG5NmBNOwFDEwYh0w9SVx4AAAACAPL/3AMOAyoAKQA1AAAlIzU+AScuAQcOARceARcVIyIGBxUeATsBFQYWFzMyNj0BMxY2NzU0JiMBPgE3HgEXDgEHLgECuJ5vhQkOom9vhQkLgV+eCw8BAQ4LngENCgUKDp4LEAEOC/50A3ZaWXYDA3ZZWnbITw6icG+ECA6icF+BC08NCwILD50LDwEOC50BDgsECw8BVll3AgJ3WVl3AgJ3AAIAQP/AA78DQAALAEYAAAEOAQceARc+ATcuARMGJicGBx4BFw4BIyImJyMOASMiJic0NjcmJw4BJyY2Nz4BNzU0NzUmNz4BNx4BFxYXFRYdAR4BFx4BAgC//AUF/L++/QQE/S4IFwwJIRETAQEzJiIwBgsGMSImMgEUESEKDBcHDAQOCxwMCgEFBVRISVQDBAEKDBsLDgMDQAX9vr79BQX9vr79/boEERElGwYUCxQaFhAQFhoUDBMGGyUREAMHNiEbIwQHFBACCgdGXAEBW0YICQIQFAcEIxsiNgABAI///ANyAwMAOgAAEw4BFxY2NxYXDgEVHgEXPgE3Mx4BFz4BNzQmJzY3HgE3NiYnLgEnNTQnNTQnLgEnDgEHBh0BBgcXDgGrFgYRDCMSDzIaHwFNOjRKCRAJSjQ6TQEfGjMPESMMEgYWEikSDwYGf29vfwUGDwEBEioBRDVSCwUZGjoqCh0SHigBASEZGSEBASgeEh0KKjoaGQYKUjUpNgcKHxgEDgxrjAMDjGsMDgQYHwoHNgAABwBcAC8DqALcABwAKAA+AFEAXQBmAG8AAAEuATc2JyYGBw4BNzYmJyYGBw4BBx4BFz4BNy4BAQYmJyY2NzYWFxYGAS4BBw4BHgE3Nh4CBwYeATY3MTYmBy4BBw4BHgE2HgIGHgE2NzYmBSYGBwYWFxY2NzYmBw4BLgE+AR4BNw4BLgE+AR4BAtEJCQQVFRVkQQIVAwoEDyabUjtBAQbRiK3MAgI9/sVrlwkFhWprlwkFhQFCKnA5DQ4GFQ4oTzwQDQQMGhcEEhd9FTYcDAsEExkbEwYIChcTBAkL/qI0XhcWJTI2ZRgUK2cKJCIIFSMhCSIDDg0DBw0NBAGPAggLMyAdARsBBA8gMhAhPFE7eTdodQEFlFQwOP7dCU9GRmsMCU9GRmsCHy4iDAMWGg4CCRlBUicNFwgLDThyHRYRBgMSFwwFCRYbGBQHCgsbOMUMKS0uUxEQKzEwUYgRCw8fIAwOICgGBQUMDQUFDAAAAAAIAEH/wQPAA0AACAASAB4AOwBOAGMAcAB7AAABJg4BHgE+ASY3JgYHBhcWPgEmEw4BBx4BFz4BNy4BAy4BJz4BNz4BFx4BBwY2Nz4BFxYHBhYXHgEXDgETMTYmBwYuATY3Nh4CBw4BLgEXMQ4BLgE3Ni4CBwYuATY3Nh4CBQ4BFx4BNz4BJy4BBxcOAScuAT4BFx4BAaEOGxEHGhwQBhwFCgMFCgUKBgMxvv0EBP2+vv0FBf31a6YFATMvQXseCwQIAxECM08SEBEDBwceMQEBonAHIRYJDwQKCRYrIAkHAxASB4IEEhUJAwoNLz4gCxEFCwstWEIS/mFUaQMId1VUaQQHeFRgElEqKB0jSykpIgEaBQkZGQwJGxgYAQMFCwYCBAoKAhYF/b6+/AUF/L6+/f02AV1TK18wPzAbDCgZCwIBFgEXGigJBwEJLCZCdgFZFiQEAgkTDgIFDSQsFggIBRAgCgoHEgogQDQUBwILFRECChtKWjMKVTc4PgcJVTg3PgepJyINDkFIIQoMQAAABAB0AFYDlAKyABoAiACPAKAAABMeAxcWBgcGDwIGByc2PwE+ATc2LgEvASUWFxYGBw4DBwYnNxcWPgI3NjcRNC4BBgcXDgEHMxUjFTMVIxcHJw4DJwYuAicmPwEzDgIeAhczNSM1MzUjBg8CJz4BPwE2NwYPAQ4BBxQvASYnPgE3NjcXBg8BBgc+AR4BFxYXAT4BNyc3IyUiLgI0PgIyHgIUDgLDGBwWFRAYAQ8EBAcqDRJuIh4xCxMECgYSD1QC8QcDAwEIBBwqMRo8SQ87FSAZEQYOBCtPb0QqAxMN+I2MHTJODw0rPU4wGiQYDgMHBwtSAQQCAQcYDjOgoDgMCxMSKgsWChQJBwwLGAwbDwsYEBMbKg8RDmQDBAgFBkt0WD8VMg7+8Q8dDAwmUv6JDxsTDAwTGx0ZFAsLFBkCDBEVEhMQGEMsDgkRWBooQh4eMwwYCRMeFwgyVSUxKXRJJTEfDgMFEjcNAQYMDwkUGgESJywLExcMCREQLVoweCAzChwZEgIBDhcfECUwAwgTFBIMBQFcMFoNDRcXJAwdDx4ODQUFCg8cDwEFDQgLF0IfJCcbCAgSCQsVEAIQCxww/tIFDgkrDOQKEBYbFhEKChEWGxYRCQAAAAQAP/+/A8ADQAALABQAJgB2AAABDgEHHgEXPgE3LgEFMhYUBiImNDYTDgEHJz4BNzYmIyc3HgEXFhQFBiYnNxc+ASc1NCYHFw4BBzMVIxUzFSMVPgE3JzcXBycOAScuAT8BMwcGFx4BMxYVBzUjNTM1IwYPASc+ATcGBwYHJz4BNRcHBgc2FgceAQH/vv0FBf2+v/0FBf3+cxgfHzAfH0cJFClVBVkUDB8EQSQiHRYTAcEYtgwLJz0NA2tqJQIMCrBgYGACIAwJMyo+CxRbSkwJBQNLAwEGBhIJAQF3dxMTDg4hEiALERMSGDYrLU8FBQricgkCDQNABf2/vv0FBf2+v/3THSocHCod/tMcJ1k1BFgiHyApNhkZFxM2XmwCCSwKAUAF3D4RJgQJGwEwMBhfBAsHIw9mGikRIQEGcQgIIxALCQgBAQJrGDATDg8dEzIVBwkXGB8paQUWCwoSN2EMAYcAAQCDAHgDegKHABEAAAEmIgcBJyYiBhQfARYyNwE2NANtDSIM/mfODiIYDewNIQ0Btg0CewwM/mPKDRoiDOgNDQG7DSEAAAAAAQCHAEsDeQKwADEAAAEGBz4BNwYHLgEnDgEHFBcuAScGFRQWFyYnFR4BFwYjIiceARcOASMiJx4BFzYSNzU2A3kqLxgjCS01FjohQlcCBGGkORUlHyYgAkQ2ExUPDg9NNChhNxMSNHdC1uEBLQJnEgYOLBsaCxcZAQJXQhIRBVZGIispQxUCEQI5UgwGAy86AiAiAiEkAQkBDqEUIQAAAAIAQP/AA8ADQAALAEIAAAU+ATcuAScOAQceARMeATcmNjc2Fhc/AQc3MAYHBjMWBgcOAQcGJicyPgI3LgEnLgEzNy4BJy4BNxcuAScmNjMGFgIAvv0FBf2+vv0FBf07QlsBBAM7QU4BGhsaKAkMGAECDistfFVXZQECNjQyAgRAFRENAUAEQRIOCAExAiYHAwkCAi1ABf2+vv0FBf2+vv0CLh8MAgRiJRoyBgkLLwQLCxYFgUdDTgkFJQMGFCABARgSERMBASgXFiEBFAI/IyAiATAAAQCJAEkDfAK+ABEAACUhLgEnNDcBPgEXFhcBFgYHBgMu/aUgKQELARcXTSUVDQEXDw4aEkkBKh8VEQHOJBMVDRX+Mhs5EQsABwBD/8ADwgNAAAgAEQAdADQARABOAFgAAAEiBhQWMjY0JhcyNjQmIgYUFhMOAQceARc+ATcuAQEiJicHNy4BJz4BNx4BFyYjDgEHFBciBRcnBgcuASc+ATceARcUBiciBhQWFz4BNCYjIgYUFhc+ATQmAVsNFRUaDw+SDQ8PGxQUFr79BAT9vr/8BQX8/uQVJhVPFisvAQOAYVV/EQsLU2wCBwsBRRE+IyJTbAICbFNPcAIsVgkODgkNDw+KCQ4OCQ0PDwIyDxsPDxsPOQ8bDw8bDwFHBf2+vv0FBf2+vv390gcEKEUeTi9VbQIBWUcBAmdNGRhROiMKAgJfRkdfAgJfRydDtQ4SDQEBDRIODhINAQENEg4AAAYAdgBAA4sCxAAWAB8AKAA5AEMATQAAATIXLgEnDgEHFBYXBzceARcyNyY1PgEnHgEUBiImPgEHIiY0NjIWFAYFLgEnDgEHHgEXMjY3Fyc+ASUiJjQ2Nx4BFAYzIiY0NjceARQGAowODRScanaeAzs1HGIZLxkNDQgChTAQExMhGQEZtBAZGSATEwJJA4lhZoUDA4VmFSoVTBUrNv7HCxERCxATE4oLERELEBITAgECVm0CA4VmOl4lVDEFCAEBHR5efU4BEiATEyASRRMgExMgE+BWcwICc1ZWcgIIBSpGIFJSERYQAQEQFhERFhABARAWEQACAED/wAPAA0AAEgBXAAABDgEHBhYXHgEXFjY3PgE3LgEGBTY3LgEnDgEHHgEXPgE3JQ4BBw4BLgInFyYnJic0NTQ3PgE3PgEXHgEXPgE3ITUzNSM1MzU0NjsBFTMVIxUzFAYHFhcBCggPAgQIEBMvDiJDHAsgEidfaAJ3IwEF/b6+/QUF/b5xvD7+5h1PLx03NycZCwEBBgIBAgIODyFJGCJlMg0SBv7ziqenBQpBtbWRGyFQVQFIBxIMESUQEw4BAhsUBx4YFSEEhlFevv0FBf2+vv0FAWNWiyE4DgkGDBcaFAMBEAgIBgYKCw0dDx4PAQEYER04FR06HToGCEgdOgNgPx0gAAACAF4AWgORArAALQA5AAABMD4CIyc1NzUjNSMVIxU3FSMVIQcOAQ8BLgEnJgYHBh4BNjc+ATcFNz4BPwEFLgE3JjY3MhYXDgECSxggCgGozMxkvr6YATkDAxIIBweZQT5tCgZDcmYtL0cBATgMCxYHCP14UTYCAjFFS4wFAnQBOy1YLgI5AihdXSgBPiARETIREgMtAwE4QkRNGhQXGUQDlxIRKxITKQM7Hx8zBT0FB2MAAAADAMIARgM9ArsAOQBAAEcAAAEzLgErATczNiYnIzQ2NyYGBwYPAQY+AjczFxYVIw4BFzMOAQcOAQciFjc+ATc+ATcXPgEvAQc2NxMRMxc3MxEDIwcnIxEzAY+IAQ0BdgZwAQUJxhAMAzoSERAVARQQKAs9AgF+DgsBkAUQCw8sPQIBHiA6FQsWCHcBCgdYHg0EpTEVVm00OEcQEqEBWxgboAMqAwM/GAMWOTsjKQYDBR8pVzcTDCUCH0IVIEUyCAIBHSgXNRyJAS0fYxYsJgEP/ic5OQHZ/lovLwFxAAAAAAIAgf/hA3QDIwAmAE8AAAE1NiYnJicuASIGBwYHDgEXFQ4BFx4BFxYzHgEXPgE3MzI3Njc2JgcUBw4BByYnDgEUFjI2NzY3Njc2Nw4BByMuAS8BNz4BNx4BFx4CHwEDVAEvLy1ABlF3UQY/Li8vAQ0NBwUaEhIXGJVsbJQZAh0VHAkHEWIEI29IDBoTGBgjFwMuKjssBwYbgVsBdY8BAQIxThIQSjkqVUsbAgFSGFKHMS4ZLTs7LRkuMYZSHQ0mGhQcBgxleAEBeGURECEdKDwaGDlFCA8BARAZEA0LBREYLQcHUFwBApN5ECoVYiMYRBcPEBwiAgAAAwGy/9kCTQMmAAwADQAZAAAlHgEyNjcTLgEnDgEVEyMUHgEyPgE1LgEiBgHRBhcjFwYfAi0fIypNTRQlKSQVASxCK+oUGBgUAeQoLwECNin9YhUkFRUkFSErKwACAHgAIwOGAtoACwAnAAABIyImNDY7ATIWFAY3JyYnNSMuASMhIgYHIxUGDwEGFhcBFjI3AT4BAnPoDhISDugOExP8OgQFAgwjE/4SEiMMAgYDOwkIDgFDEjgTAUIPBwHSExwTExwTSI0ICAIPEhIPAgcJjRYyE/56FhYBhxIyAAMAegAnA4YC3QAQACQAMAAACQIuAT8BPgEzITIWHwEWBjcnLgEjISIGDwEGFhcBFjI3AT4BJSMOARQWOwEyNjQmAz3+w/7DAwICOgIKBAHtBAkCOgICPjsKKhj+ExkqCjoJCA4BQhI4EwFBDwf+7ucPEhIP5w4TEwHt/n8BgAQPBI4DBwcDjgQOLI0WHR0WjhUyE/57FhYBhhIyEAEUHhUVHhQABAA//78DvwNAAAYAEgBMAFMAACU3MxEjETMDDgEHHgEXPgE3LgEBBjEGBzcXFgYHJw4BBw4CJjM+ATc+ATcjJjY3MzQvASMOAz8BNjc+ARcOARUzHgEVIwczMhYfASMHJyMRMwJsNyx+DmC+/QUF/b6//AUE/f7qAQIKF0UFBwJcBxEIESwxAQIvIwsJDARwAQkKYwEBMAggDBABEA0NDi0DCgyaBwRYBVwBCgHlVUMQJ8/HJQEg/uACVAX9v779BQX9vr/9/igBHiMRTBkjAWsWKREgFgMGJzYZEDQYAh0JDytDIBgEAgUgGy4sEQISMgICIQJ9FROfLCwBcQAAAAUAQ//BA8ADPgALABcAQABKAFQAAAUuASc+ATceARcOASc+ATcuAScOAQceARM0NjIWHQEzMh4CHQEUDgIrARUUBiImPQEjIi4CPQE0PgE3NjsBByIGHQEUFjsBNTMVMzI2PQE0JiMCAb77BQX7vr78BQX8vqPYBATYo6LYBQXYgQ0cDm4QFAsEAwsUEW4MIQprEBMNAwMNCQ0Na14HAwQHXTdfCQMFCz8F/L69/AUF/L2+/DsE2KOi2AQE2KKj2AIwDwsLDzgDCxMQiA4TCwVcCw4MDlsEDRIOiA8TDAECNQQJYwsEf4AGCmMIBQAAAAIBJgAVAtoC7AAIADQAACUiJjQ2MhYUBhMUBgcOAQcOASsBLgE3PgE3PgEnNicuASMxDgEHFAYiJic0PgIzMhYXHgEB9xYeHiweHssrPyEmAwIYEQQTFgEGOCU4GgECIhMzHDdKARklGAEhPU8sLlQgHRwVHi0dHS0eAf4oUz8gNBURFgIbEi1NJTg3Ei8kFBYBSjcSGRkSLE8+ICMiHkwAAAAAAgCkAEQDWQK5AAkAIwAAASMRMzIWFREUBg8BBi8BLgE/ASMiJic1ND8BPgEzITIWFREUAzpfXw0SErTKCQsbDAkEJMoaJAEFYgMQCgFVDRIBPwF6Eg3+xQ4SKckJBxUJGg6PJBtCDAzsCQsSDv6UDQAAAAADAJcAIANjAtkAHgAzAD0AAAEmIyEiBgcDBhcxFjsBFRQXMRYzMjY/AjMyNj0BNAMHBicuASc1LgEnIyInJjcTPgEzITsBMhYXFQ4BKwEDRx0n/igjNgYtCB0dK55NExARHgoDbk8nOeNxBQUPHwEBEQy6EAwLAi4CEw8BVTxHDxUBARUPRwK+Gy4i/tsoJiBkOy0KEQ8G9Tgm4Sj+ufwGAwgcEoIMEQEMDg4BJg0QFA/fDxQAAAAAAgCkAEgDWQK9AAkAJAAAEzMRIy4BJxE+AT8BNh8BHgEPATMeARcVFA8BDgEHIS4BJxE0N8ReXg0SAQEStMkKCxsLCQMkyRsjAQVhBA8K/qsOEQEJAcL+hgERDgE7DRIpyQkHFAkbDo4BJBpDDAzsCQoBARINAW0NCQAAAAMAnQAgA2kC2QAeACgAPQAAATEmKwE1NCcjJiMGBwYVDwEjDgEHFR4BMyEyNjcTNgEjIiY9ATQ2OwEBIRE3NhceARcVFBY7ARYXFgcDDgEDTR4qnk0BEw8kFQICbU8nOQEBOCgB2CI3Bi0H/dxHEBUVEEcBkf6qcQYFDiABEgy6EAwLAi8BFAHjIGU6LgkBHwIBA/QBOCbhKDcuIgElKP6fFA/fDxT+2wFD/AYDCB0Rgg0RAQwODv7bDREAAQBl/8EDmgMrAEAAAAEVDgEHDgEHDgEjIiY0NjcyFhc+ATcjIiYnNT4BNzM1LgEnDgEHFTMeARcVDgEHIy4BPQE0Njc1PgE3HgEXFR4BA5oBKB4NbXkIHREcIyMcERwJV08LFR8qAQEqHyADonh4oQMgHioBASoeVR8qIxwExJSTxAQcIwF9qB8pAj1iChARIzcjAREPCDwmKh+pHyoBCnihBASheAoBKh+pHioBASoeqRwoBAyUxAQExJQMBCcAAQCEABsDewLmADwAAAExBwYPAQYjIi8BLgE+AR8BLgEnIg4CFB4CMz4BNz4BHgEHDgEHIicmJy4BNDY3Njc2Nx4BFzc+AR4BA3sqBAoEBQcCA6ENDAYVDGYklFs9bVUuLVZtPWGaIgUWGAsEKbl1SURBMjQ2NjQyQURJZKczEgMVGQwCY58NBQICASoEFRkNAx1QXwEuU2t3a1UsAWlZDQoJFg1rfwIcGzIzgZCCMzEcGwEBYVVCDA0HFAAEAEH/wAPBA0AACwAXACAALQAAAQ4BBx4BFz4BNy4BAy4BJz4BNx4BFw4BAxQWMjY0JiIGFyIGHQEUFjI2PQE0JgIBvv4EBP6+vv0FBf2+otkEBNmiodkEBNnNGSUZGSUZLA4TExsTEwNABf2+vv4EBP6+vv38xgTZoqHZBATZoaLZAj8SGRklGRmLEw7wDhISDvAOEwAAAAQAjQBIA3ICuQAXAC8AOwBHAAABIycmKwEiDwEjDgEHER4BFyE+ATcRLgEDDgEjISImJxE+ATsBNj8BMxcWOwEeARclDgEHHgEXPgE3LgEDLgEnPgE3HgEXDgEDF3YyCRCvEQgydiUzAQEyJgIzJTMBAjMJARAM/c8MEAEBEAyGEQgyjzIIEYYMEAH+yktkAgJkS0tlAgJlSy07AgI7LS08AQE8AmBLDg5LATIm/polMwEBMiYBZiYy/kIMEBAMAWYMEAEOSUsOARAMEAJkS0tkAgJkS0tk/ucBPC0tPAEBPC0tPAAAAAACAGwAGQOPAuoAGAAwAAABLgEiBg8BJy4BIgYHBhQXARY2NwE+AiYHCQEmNDc+ATIWHwEWMj8BPgEyHgIUBgNPIVFcUiEOESBSW1MhQkIBOQoZCgE5ICEBIEv+3P7cMDAXO0E8FyUKGQglGDtBOy8YFAKmICQkIBERICQkIEa4R/7BCQIHAT8hU11T9/7WASo0hzQXGhkYKAkJKBcaGTA9RT0AAAIA2QAYA6gC6gAkADEAAAEnLgEPAiIGDwEGFh8BBwYUFjMWNj8BFxYzMj8BPgE/AjY0AQYVDwEBPwEyPwIXA5vEDiUO7FUFCQIzDwEOhbUJEwoFDAS0hQ4VEhE0AgUBDMkN/vkGDSf+2yVTBgQD7rQCGMQNAQzLDQYCMxAnDYa1CRcTAQQFs4UODjQBCQZU7BIm/v8HB1InASUlDQQBy7UAAAAAAgCaAEADZgK+AA4AGAAAATUuASchNS4BJyMOAQcdAh4BFyE+ATc1A2YBLyT+7AEwI7wkLwEBLyQCJCQvAQHCLiQvASYkLwEBLySoOPUkMAEBMCT1AAAAAAIAfQCQA4cCcQAPACIAAAEhJgYHER4BNyEWNjcRLgEFJg8BBh0BFBYfARYzFjc2NREmAmH+YQQ9BAQ9BAGfBD0EBD0BGAoHiQYDA4kEBwIGCAECbgMWMP6rMRUCAhUxAVMyFiUDBWsGBqoDBwJqBAEDBAoBfgsAAAEAnABXA2ACpQASAAATFwE+ARceAQcBFAYvAS4BNz4B5qQBmAEgEwYEDf5VKR64AgsTBx0BnYIBeAIQDQQZE/4MBBke+AIjEQYEAAADAGwAGQOPAuoAGAAwAEAAAAEuASIGDwEnLgEiBgcGFBcBFjY3AT4CJgcJASY0Nz4BMhYfARYyPwE+ATIeAhQGJScHBgcGDwEXCQE3LwIHA08hUVxSIQ4RIFJbUyFCQgE5ChkKATkgIQEgS/7c/twwMBc7QTwXJQoZCCUYO0E7LxgU/r2OVksLAxgbMQFFAV0MNSZXHgKmICQkIBERICQkIEa4R/7BCQIHAT8hU11T9/7WASo0hzQXGhkYKAkJKBcaGTA9RT3OWBoYBg5FUGj+wQFdnEwmGA0AAAAAAgC4ADcDSQLIAA8AGwAAASEOARURFBYXIT4BNxEuAQMhLgE0NjchHgEUBgMO/eUZIiIZAhsYIgEBIn3+rwsODgsBUQoPDwLIASEZ/eUZIQEBIRkCGxkh/p8BDhUOAQEOFQ4AAAIAuAA3A0kCyAAPACsAAAEhDgEVERQWFyE+ATcRLgEDIxUOASImPQEjLgE0NjczNTQ2MhYXFTMeARQGAw795RkiIhkCGxgiAQEifZABDhUPjgsODguODxUOAZAKDw8CyAEhGf3lGSEBASEZAhsZIf6fjgsODguOAQ4VDgGOCw4OC44BDhUOAAABANkAGAOoAuoAJQAAATEPAQ4BDwEGIyIvAQcOASciJjQ/AScuAT8BPgEzPwE2Fh8BFhQDm8kMAQUCNBESFQ6FtAQMBQoTCbWFDgEPMwIJBVXsDiUOxA0B0+xVBQkCMw4OhbMFBAETFwm1hg0nDzQCBg3LDAENxA0mAAAABgCNAEcDcwK4ABcALwAwADwATABYAAABIycmKwEiDwEjDgEHER4BFyE+ATcRLgEDFAYjISImNRE0NjsBNj8BMxcWFzMyFhUFIx4BFz4BNy4BJw4BJScjLwEHJw8CER8BIT8BBS4BJz4BNx4BFw4BAxh2MgkQrxEIMnYmMwEBMicCMyUzAQIzChAM/c8NEBANhhAJMo8yCBGGDBD+y2oBPC0tPAEBPC0tPAG7LHVSrUcWVEMVEjgCQhIV/qRLZAICZEtLZAICZAJfSw4OSwEyJv6aJTMBATImAWYmMv5CDBAQDAFmDBABDUpLDgEQDKEtPAEBPC0tPAEBPIQcZQRZCAgKQv6+PRIU05gCZEtLZAICZEtLZAAAAAIAPv+/A8EDQABRAF0AAAEeAR8CNzYWHwEeAQ8BHwEeARcVDgEPAhcWBg8BDgEvAQ8BDgEHIyImLwIHBiYvAS4BPwEvAS4BJzU0Nj8CJyY2PwE+AR8BPwE+ATczFQMOAQceARc+ATcuAQImFB0GDydIDyUPOQ4DCzIRVxMZAgEZFFYSMgsCDTkPJRFIJw8EHBJRFB0FDydKDyUPOQ8DDDISVxIZAhkUVxIwCwEMOBAlEUgnDwQcElMoMkQBAUQyM0MCAkMDPgEZFFcRNAsCDTkPJRFIJw8EHBJRFB0FDydIDyUPOQ4DCzIRVxMZAhoUVxEwCwINOQ8mEEgnDwQcElEUHQUPJ0gPJQ05DgQMMhJXEhkCAv66AUQyM0QBAUQzMkQAAAABAH//9wOBAwoAJwAAASEVMw4BBwYHLgEnJjQ3PgE3Mhc3LgEnDgEHBhQXHgEXMjY3PgE1NAN6/o3UByggNVBOdRkMDBl1Tlg/cTSEUHS5MikpMrl0UIQxOD0ByJgmPBUlAQJZSCNOJEdaATtxMDYBAnViUcBRYnUBMi00k1oqAAQAhwAHA3kDBwAiACgALwA7AAABPgE1LgEHJgYHPgE3DgEHDgEHHgE3HgE3MjY3IwYmJyE2JgUeARcGJhM+ATceARcDPgEzMhYXFAYHLgEDSg0SAaGQesIfI14xO2UmGCgBBZ9kI0snbaomsUTRCwHhCBP9gBNMM2hz8gRYPTxYBCMYOBsjKwENCB1UAg4cRh9RJ0AFl38oVhY1gkYrdTRpCTQQBwFwZV4zcTBr3zldHjlJAUM+TAEBTD4BEwwUKyMXNxYwTQAAAgCl/+QDWAMcAB0AKAAAASY3JicmBgcuAScOAQcUFx4BFz4BNx4BFz4BNy4BAz4BJw4DFxY2AucBXTRoMlsVFlAnUH8EGhRtRCI+LCo9JURmElUgZCQLAyJELxkDJEABaWQ3SwcBJAMDHwMCeXpITTulBwMcAgIcAwmZOi9yATUuSBADJDVDJgIhAAAEAHf/9wOfAwoACwAWACEALwAAAR4BFz4BNy4BJw4BJQceAQcDFjY3NhIFAw4BFR4BFzcGJhMFLgEnLgEjDgEHFz4BAXsBSzg4SwEBSzg4SwHx3SwEJrE3bzSAXP2rlB4hA7iQZUZ2rQFFGU4zL2QzV585eRhzAYE4SgICSjg5SgICSlQLNoc8/vADGR5NAQpPASIubTyW1BnFC0ABSxEyUx4bGgFLRrpCRwAAAQBl/+8DmgMQADwAAAEOAQceARcWNic1BiYnLgE3NhceATc2Ny4BNzQ3JjY3NhYXNjIXPgEXHgEHFhUWBgceARcVFBYzPgE3LgEB/67oBAKXeg8EAVkuAw8kBykxFDgUCBdgVwEsDA0DKkMELnQuCUAmBA0MLQFYYBASAQQJfJkDBecDEAXnronPLAMPCUcGSA8VFAkWSxoEBiEXEmpHRTQrPQYBJQYNDQgiAgY8KjRFSGkSECsZZwYKK9CKrucABQBB/8EDwANAAAsAFwA6AEEARwAAAQ4BBx4BFz4BNS4BJw4BBx4BFz4BNy4BEyEeATczDgEHIiYnBiYnPgE3PgE3DgEHPgEXNhYXFAYHHgEnDgEHMy4BBQYWNy4BArYTJhAjORQGCQEdzr79BAT9vr79BQX9QP63CI4veRp1Sho0GERtAwEbEBtEKSJAGBaEU2JuAQ0IEw3mKjwC0AM8/vsyT0cjNAJkAQ0IDjYgDyUQGB3dBf2+vv0EBfy+vv3+GE0jQUVMAQULJAZII1AeMFglEDobVmcDKxo4FTATIkl8ATQqKjTLcDEmFUAAAAAAAgBB/8EDwANAAAsAMQAAAQ4BBx4BFz4BNy4BAw4BIy4BJyY0Nz4BNzIWFwcmIw4BBwYUFx4BMjc2NyM1MxYVFAYCAL79BAT9vr79BQX9Fx9RMklzHxkZIHJJMlIgRic3MEoPBwcPSWMhJwqE5wQmA0AF/b6+/QQE/b6+/f2MHR8BST0yeDI9SQEiHkYlATgsFjEWLTgXGy9fGRo4WwAAAAwAkAA+A28CwQALABcAPwBcAIQArADMAOgA9AEAAQwBGAAAAR4BFAYrASImNDY3FzIWFAYrASImNDYzJyImPQE+ATchHgEXFRQGByIGFBYzHgEdAQ4BByEuASc1NDY3MjY0JhcOAQcVHgEzITI2PQEuATQ2NzU0JichDgEHFR4BASEuASc1PgEzPgE0JiciJic1PgE3IR4BFxUOASMOARQWFzIWFxUOAQEOAQcVFBYzHgEUBgciBh0BHgEXIT4BNzUuASMuATQ2NzI2NzUuAScRISImPQE3PgE0Ji8BNTQ2MyEyFhcVBw4BFBYXMxUOASUVFBYzITI2NzUuATQ2NzUuASMhIgYdAR4BFAYlIyImNDY7ATIWFAYnIgYUFjsBMjY0JiM1IyImNDY7ATIWFAYnIgYUFjsBMjY0JiMCbQsQEAvbDA8PDNsLEBAL2wwPDwzkDA8BOCsCESo5ARALGyQkGwsQATkq/e8rOAEPDBskJFwBMygBGhMCERMaKDIyKBoT/e8TGgEoMwHk/e8sOgEBEA0aIyMaDRABATosAhErOgIBEQwaIyMaDBEBAjr9xCk4AQ4LHCYmHAsOATgpAhEpNwIBDgocJiYcCg4BAjcp/e8UHAImMzMmAhwUAhEUGwECJzIyJwIBG/2wGRICERIYASc0NCcBGBL97xIZJzQ0AXrbDRERDdsMERHnCw4OC9sKDg4K2w0REQ3bDBER5wsODgvbCg4OCgHkAQ8XEBAXDwGSEBcQEBcQbRAMfys4AgI4K38MDwEkNyQBDwx/KzgCAjgrfwwPASQ3JEAqPwpnFBoaFGcKP1U/CmcUGQEBGRRnCj/+lAE6LH8NEQEiNCMBEQ1/LDoBATosfw0RASI1IgERDX8sOgJ9ATcqfwsOASY4JQEOC38qNwEBNyp/Cw4BJTklAQ4Lfyo3Af25GxVpAQk/UT8JAWkVGxsVaQEJP1E/CmoUG5VlExgYE2UKQVVAC2UTGBgTZQpBVUEFERkRERkRNg4VDg4VDlwRGRERGRE2DhUODhUOAAAAAAMAkwBBA2wCvwAnADMAPwAAAR4BFxUUBgciBhQWMzIWHQEOAQchLgEnNTQ2NzI2NCYjLgE9AT4BNwEjIgYUFhczPgE0JicjIgYUFhczPgE0JgMIKjkBEAsbJCQbCxABOSr97ys4AQ8MGyQkGwwPATgrAXbbDA8PDNsLEBAL2wwPDwzbCxAQAr8BOSt/DA8BJDYlEAuAKzgBATgrfwwPASQ3JAEPDH8rOQH+kxAXDwEBDxcQkhAXDwEBDxcQAAAAAAIBAv/vAvoDEQAPAB8AAAEjIgYHER4BOwEyNjcRLgEhIyIGBxEeATsBMjY3ES4BAVEEICoBASogBCAqAQEqAT4EICoBASogBCAqAQEqAxErH/1zICsrIAKNHysrH/1zICsrIAKNHysAAwCRACEDZgLGACkANgBDAAABLgEHJwE2NCYiBwkBJiIGFBcBByYGBwYWFxY+Aic3FwYWFx4BPwE2NAUOAScxJjQ2MhcxFhQFDgEnIyY0NjIXMxYUAz8iWihwAQQJEhcK/vz++wkYEgkBBXAxaCAdGC8pW0QJGHBwGAkhKWopASb97hY7FxYtOhcWAawXOxYBFSw6FwEVAQkhCxlxAQYJGBIJ/voBBgkSGAn++nEdGC8xaCAYCkRbKHBwKFsjJwEnASlrahUBFhc7LRYXOxcVARYXOy0WFzsACQCUAAUDbAL9ABEANQBNAF8AeQCeAMQA4wD7AAAlFAcGBwYHIicuATc2Nz4BHgETFAYHDgEHJy4BNz4BNTYmJyYjIgcGJicxJjY3NjMyFx4DJRQPAQ4BFzEOAQcnLgE3NDY3Nj8BNjIWAw4CIyInMS4BNzE2Nz4BHgEFDgEjIicuATcxPgEnJicmNDYzMTIXFhcWBgMOAScmIyIGBw4BDwEGBzEGIyInLgE3Nj8BPgE3Njc2MzIXHgETDgEHMQYHIicuATc+ATc2JicxJisBJgcOAS4BNzY3MxYXFhcWBicGDwEGBw4BBwYHIicuATcxPgE3Nj8BNjc+ARczHgEXMRQOAgcGByIuATc+ATc+ATUxNDYeAQNIAQ0PCRIIBwsHBgsLBBYXDCQJCwIRCwYNDgIKCQIdO02FRDcMFwYFCAxDUqBfJCoUBf3iCQY9KAECEQwEDQ8BCQ0hPgcKGBNtEwgMBgwJCgEJAhEIGRUDAdgEDwoGBgwJBR0YAQE2CRINDQlIAQEYcAQXDCAoLUYZFxQHBgotChAJCAsFBicIBgcYHB8tM0EzKgwLCQwwGAoQCQkKBAcQLA4JBxAQIQEZEAgZFAQIIjgBPiMeBAEFwgcEAwIDFD0EChEJBwsGBwU4EQMDAQMIBBUMAQwNYwkZOTIJDwsTAggsMwsMCRMaEYIEBCwaDwEFBhgMEyUMDQcQAQQiZzsLDgEBAxQNOGEgFIVEURoFCAwMFwYgZChTTDfuDQkGSI4HDA8BAQEUDQIyKGNCBwkT/e4WCQUICRkKAhUKAxAaKAkKAwUXDEqaL1Q2CRoSCUhuLKgBlwwLBAwmJSRSLSI6Rg8FBxgMPC0iMF8sLxoeDwUX/sdBjyUMAQYIGAsZf0Q0ThMTARUKAxAZCiwBASkkQRw0TyEeGwsJRG8HDQEFBxkLBmY7DhgCICQNDQIEFR4IQGF+PgsBEBkKN3ArLzwEDREBEwAAAQB7AAEDhQL/ACwAAAEnJi8BJiIPAgYdAR4BFzMRFBY7AT4BNzU0NzMWFxUeATsBPgE1ETMyNjcmA3AdabMLFDAUhrwXAyMZKhsUkhMYARM4EgEBGhWSExgqGiQBAQHbGFmaCRAQc6ASHQQaIAH+whQbARsTpRIBARKlFBsCGhMBPyQaHQAAAAIAfQABA4gDAQApAE4AAAEmJyYvASYiDwEGBxUOARY7AREUFhczPgE9ATMVHgEXMz4BNREzMjY3NAcRIzU0JicjDgEdASMRNCYnIwY/ATY/ATYfARYXMxUWFQcjIgYDgQYLO1KzFDQTs1I7FwEnGiccFo0WHFgBHBaNFR0mGyYBpnUdFXEWHHUcFjMDAQE8UbMEBbNSOwEBATMWHAHJDQoxRpoQEJpGMQEXNCf+xhUcAQEcFbS0FRwBARwVATonGg5D/seyFRwBARwVtAE7FhwBAQMEMUaaBASaRjICAgEBHAAAAAYAzAAmAzEC2wAtADYAZACTAJwApQAAAR4BFAYrARUUBg8BFx4BHQEzMhYUBgchIiY0NjsBNT4BPwEnLgE9ASMuATQ2NwUhFRYfATc2NQEiJjQ2NzM1NDY/AScuAT0BIyImNDY3ITIWFAYHIxUOAQ8BFx4BFxUzHgEUBiMlMQ4BFBYzITI2NCYrATUuAS8BNz4BPQEzMjY0JiMhIgYUFjsBFRQWHwEHDgEdARMnJj0BIRUGByUVFB8BNzY3NQMUCg0NClcQDnp8DxBUCg0NCv3WCg0NCVYBEA59eQ8RWAoNDQkBpf7jAQ2BgA7+XA0REQxQERB3dBARUg0REQwCKwwREA1QAREPdHYQEQFNDBEQDf3WCAkKBwIqBwoKB1oBDw2Cfw4PXQcKCgf91ggJCgdfDw5/gw0PuoUQASoBD/7zC318CwEC1QENFA19EyIMZmcNIhN+DRQNAQ4UDX4TIgxoZQ0iE3wBDRQNATB7EgxrawwS/fwRGREBdxQlDWNgDiQVdhEZEQERGREBdhQlDWFiDiQVdwERGREvAQkPCQoOCoQSHwxsawsgEYMKDgoKDgqDEh8Mam0LIBGEAURuDhWCghUOmHUPCmhoCg91AAAACQDOABwDMQLhAC0AOABDAHEAlwCiAK0AuADDAAABMhYUBgcjFRQGDwEXHgEdATMyFhQGIyEiLgE2OwE1PgE/AScuASc1Iy4CNjMBBw4BHQEhNS4BJxMhFRQWHwE3PgE3EyEiJjQ2NzM1NDY/AScuAT0BIyImNDY3IR4BFAYrARUUBg8BFx4BHQEzMhYUBiUiFDMhMjQrATU0Ji8BNz4BPQEzNjQnIQYUFzMVFBYfAQcOAR0BKQE1NDY/ARceARUFITU0Ji8BBw4BFTcnLgE9ASEVFAYHJRUUFh8BNz4BPQEDEwgKCghQJiJWViImUAgKCgj92QgKAQsITwEmIlVVIiYBTwgKAQsIARRqGR0BPwEcGTb+wR0ZaWoZHAF0/dkNERENRColQkMkKkQNERENAicNERENRColQ0MlKkQNERH9zAcHAicGBlskH2dnHyRbBgb92QcHWyMgZ2cfJAFj/qsfHW9vGyD+wgEnGRdjZBcZk28cHwFVIBz+/hkWZGQXGQLVChAKAYcoQRMvLxNBJ4gLDwsLDwuIJ0ETLy8TQSeIAQoQCv6UOg4wHYiIHTAOAYKIHTAPOjoPMB398xEZEQF8KkcUJSUVRit8ERoQAQEQGhF8K0YVJSUURyp9ERkRJA0NlCQ8ETk5EjwjlAEMAQEMAZMkPBI5ORI7JJSUIDUQPT0QNSB9fRoqDTc3DSoasj0QNSGTkyA2EOJ8GisNNzcNKxp8AAAAAwC3ABIDSwLwACYANgBDAAABITU+ATMyFhcxHgE+AScuASMiDgIdASMOAQcRHgEXIT4BNxEuAQUhHgEVERQGByEuATURNDYXIgYdARQWMjY9ATQmAu7+pgFDMCc7DQUXGgwFE147JEE1G0AnNAEBNCcB3Cc0AQE0/f0B3AoQEAr+JAsPD/kOExMcExMB2mQvQSkjDQwKGA02QhszQCRlATQn/vEnNAEBNCcBDyc1QQEPC/7xCw8BAQ8LAQ8LDzIQDF0LEBALXQwQAAAAAgC1ABEDSALvACYAMgAAASE1PgE3HgEXMR4BPgEnLgEjIg4CHQEjDgEHER4BFyE+ATURLgEHFAYiJj0BNDYyFhUC7P6mAUMwJjwMBRgaCwQTXjskQjQbQCc0AQE0JwHbKDQBNPoPFA8PFA8B2WUuQQEBKSMNCwkYDTZCGzNAI2UBNCj+8Sc0AQE0JwEQJzTcCw4OC1wKDw8KAAAABQC9ABkDQwLoABkAIgAzADcAQwAAASM1LgEnDgEHFSMOAQcRHgEXIT4BNxEuASMlPgE3HgEXFSMBDgEHIS4BJxE+ATMhMhYVEQERIREFDgEiJic1PgEyFhcC7DUCZEpJZAJEJTEBATElAdglMQEBMSX+pAFDMzNEAe8BfAESDf4oDRIBARINAdgNEv3ZAj3++wEQFhABARAWEAEB1GlIYQICYUhpATEl/vMlMQEBMSUBDSUxajFCAQFCMWn+nA0SAQESDQENDRISDf7yATf+kwFtqgwQEAxcCxAQCwAAAAAEALsAFgNFAuoAGwAjADMAPwAAASM1NC4CIg4CHQEjDgEHER4BFyE+ATcRLgElPgEyFhcVIwEUBiMhIiY1ETQ2NyEeARUHFRQGIiY9ATQ2MhYC7DMbM0BHQDMbQiYyAQEyJgHYJjIBATL+gAFEYUQB6wF3EQz+KAwREQwB2AwR6xIYEhIYEgHWZyI/MhoaMj4jZwEyJv7zJjMBATMmAQ0mMmgvQkIvZ/6ZCxERCwEOCxEBARAMJVwNERENXAwSEgADALIAMQNSAtEADwAYACgAAAEhIgYHER4BFyE+ATURNCYFPgEyFhQGIiYBDgEHIS4BNwE2Fh8CFhUDFv3ZGiIBASIaAicZIyP+DQEnOicnOicB4gENCv5yEg0MATYIFQcCVwYC0SMZ/dkaIgEBIhoCJxkjzh0nJzsnJ/6aCw4BAR0NARYHAQgCeAUIAAUApQAjA1oC2QAQAB0AMQA6AEMAAAEmIyEiBhURFBYXIT4BNREmAyEBNDMyHwEWFxUOASEjIiYnET4BNyEeARcRJyYiBwEGEw4BFBYyNjQmByImNDYyFhQGAz4cJv4GJjc2JwH7JjcBXf5wAR4FAQSDBQUDFP4RGQ8SAQESDwH7DhMBYhI3Ev6/BXInNDRPMzMoDRISGhISAr4bNyb+BCY2AQE2JgH8J/2+AR0BAYMFAXIQEhMOAfoOEwEBEw7+yWIREf7DBAHlATNONDNPM3kSGhERGhIAAAAAEgDeAAEAAAAAAAAAFQAsAAEAAAAAAAEACABUAAEAAAAAAAIABwBtAAEAAAAAAAMACACHAAEAAAAAAAQACACiAAEAAAAAAAUACwDDAAEAAAAAAAYACADhAAEAAAAAAAoAKwFCAAEAAAAAAAsAEwGWAAMAAQQJAAAAKgAAAAMAAQQJAAEAEABCAAMAAQQJAAIADgBdAAMAAQQJAAMAEAB1AAMAAQQJAAQAEACQAAMAAQQJAAUAFgCrAAMAAQQJAAYAEADPAAMAAQQJAAoAVgDqAAMAAQQJAAsAJgFuAAoAQwByAGUAYQB0AGUAZAAgAGIAeQAgAGkAYwBvAG4AZgBvAG4AdAAKAAAKQ3JlYXRlZCBieSBpY29uZm9udAoAAGkAYwBvAG4AZgBvAG4AdAAAaWNvbmZvbnQAAFIAZQBnAHUAbABhAHIAAFJlZ3VsYXIAAGkAYwBvAG4AZgBvAG4AdAAAaWNvbmZvbnQAAGkAYwBvAG4AZgBvAG4AdAAAaWNvbmZvbnQAAFYAZQByAHMAaQBvAG4AIAAxAC4AMAAAVmVyc2lvbiAxLjAAAGkAYwBvAG4AZgBvAG4AdAAAaWNvbmZvbnQAAEcAZQBuAGUAcgBhAHQAZQBkACAAYgB5ACAAcwB2AGcAMgB0AHQAZgAgAGYAcgBvAG0AIABGAG8AbgB0AGUAbABsAG8AIABwAHIAbwBqAGUAYwB0AC4AAEdlbmVyYXRlZCBieSBzdmcydHRmIGZyb20gRm9udGVsbG8gcHJvamVjdC4AAGgAdAB0AHAAOgAvAC8AZgBvAG4AdABlAGwAbABvAC4AYwBvAG0AAGh0dHA6Ly9mb250ZWxsby5jb20AAAIAAAAAAAAACgAAAAAAAQAAAAAAAAAAAAAAAAAAAAAA1gAAAAEAAgECAQMBBAEFAQYBBwEIAQkBCgELAQwBDQEOAQ8BEAERARIBEwEUARUBFgEXARgBGQDvARoBGwEcAR0BHgEfASABIQEiASMBJAElASYBJwEoASkBKgErASwBLQAOAS4BLwEwATEBMgEzATQBNQE2ATcBOAE5AToBOwE8AT0BPgE/AUABQQFCAUMBRAFFAUYBRwFIAUkBSgFLAUwBTQFOAU8BUAFRAVIBUwFUAVUBVgFXAVgBWQFaAVsBXAFdAV4BXwFgAWEBYgFjAWQBZQFmAWcBaAFpAWoBawFsAW0BbgFvAXABcQFyAXMBdAF1AXYBdwF4AXkBegF7AXwBfQF+AX8BgAGBAYIBgwGEAYUBhgGHAYgBiQGKAYsBjAGNAY4BjwGQAZEBkgGTAZQBlQGWAZcBmAGZAZoBmwGcAZ0BngGfAaABoQGiAaMBpAGlAJwBpgGnACIBqAGpAaoBqwGsAa0BrgGvAbABsQGyAbMBtAG1AbYBtwG4AbkBugG7AbwBvQG+Ab8BwAHBAcIBwwHEAcUBxgHHAcgByQHKAcsBzAHNAc4BzwHQD2Fycm93LWRvd24tZmlsbA5hcnJvdy1sZWZ0d2FyZAllbXB0eS1jYXIPYXJyb3ctcmlnaHR3YXJkDmFycm93LWRvd253YXJkC2Fycm93LXJpZ2h0CGFycm93LXVwDGFycm93LXVwd2FyZANybWIEYmVsbAhib29rbWFyawxzaGFyZS1zcXVhcmUDY2FyCmFycm93LWRvd24KYXJyb3ctbGVmdAVjbG9jawpwbGF5LXJpZ2h0BWVtYWlsCGVkaXQtcGVuA2V5ZQxhY2NvdW50LWZpbGwRbWludXMtcGVvcGxlLWZpbGwIbGlzdC1kb3QIYmFnLWZpbGwDYmFnCm1hbi1kZWxldGUMbWludXMtY2lyY2xlB21hbi1hZGQDbWFwCWNoYXQtZmlsbAdzZXR0aW5nBGNoYXQNc2hvcHBpbmctY2FydAVwaG9uZQV0cmFzaAxlcnJvci1jaXJjbGUPcXVlc3Rpb24tY2lyY2xlEHBsdXMtcGVvcGxlLWZpbGwKZW1wdHktcGFnZQdhY2NvdW50BHRhZ3MGc2VhcmNoD2V5ZS1vZmYtb3V0bGluZRFlcnJvci1jaXJjbGUtZmlsbAtwbHVzLWNpcmNsZQplbXB0eS1kYXRhDW1vcmUtZG90LWZpbGwFc2hhcmUGYXR0YWNoCXZvbHVtZS11cA1jYWxlbmRhci1maWxsFWNoZWNrbWFyay1jaXJjbGUtZmlsbAhjYXItZmlsbBFjbG9zZS1jaXJjbGUtZmlsbApjbG9jay1maWxsC2VtcHR5LW9yZGVyDWJvb2ttYXJrLWZpbGwIZG93bmxvYWQQY2hlY2ttYXJrLWNpcmNsZQttb3JlLWNpcmNsZQxjbG9zZS1jaXJjbGUJYmVsbC1maWxsCGV5ZS1maWxsCmVtYWlsLWZpbGwMcGF1c2UtY2lyY2xlCnZvbHVtZS1vZmYQbW9yZS1jaXJjbGUtZmlsbA1lbXB0eS1hZGRyZXNzC3BsYXktY2lyY2xlB2V5ZS1vZmYHbWljLW9mZgNtaWMQaW5mby1jaXJjbGUtZmlsbAxtYW4tYWRkLWZpbGwMd2FybmluZy1maWxsCG1hcC1maWxsCnBob25lLWZpbGwEbGlzdAl0YWdzLWZpbGwRbWludXMtY2lyY2xlLWZpbGwEaW5mbxFwYXVzZS1jaXJjbGUtZmlsbBBwbGF5LWNpcmNsZS1maWxsCmtlZnUtZXJtYWkPcm1iLWNpcmNsZS1maWxsCnRyYXNoLWZpbGwPdm9sdW1lLW9mZi1maWxsBGdpZnQJZ2lmdC1maWxsEnNob3BwaW5nLWNhcnQtZmlsbApzaGFyZS1maWxsBHN0YXIQcGx1cy1jaXJjbGUtZmlsbARzY2FuCWZpbGUtdGV4dAxlbXB0eS1zZWFyY2gOZmlsZS10ZXh0LWZpbGwUcXVlc3Rpb24tY2lyY2xlLWZpbGwEd2lmaQh3aWZpLW9mZglzdGFyLWZpbGwScGVyc29uLWRlbGV0ZS1maWxsDWVkaXQtcGVuLWZpbGwJcGxheS1sZWZ0CGNhbGVuZGFyDHJld2luZC1yaWdodAt2b2x1bWUtZmlsbAtyZXdpbmQtbGVmdBJza2lwLWZvcndhcmQtcmlnaHQEZ3JpZA5za2lwLWJhY2stbGVmdA5wbGF5LWxlZnQtZmlsbA9wbGF5LXJpZ2h0LWZpbGwKcm1iLWNpcmNsZQlncmlkLWZpbGwQcmV3aW5kLWxlZnQtZmlsbBFyZXdpbmQtcmlnaHQtZmlsbAliYWNrc3BhY2ULZW1wdHktZmF2b3IMYW5kcm9pZC1maWxsE2FuZHJvaWQtY2lyY2xlLWZpbGwRYmFpZHUtY2lyY2xlLWZpbGwFYmFpZHUMZW1wdHktY291cG9uDWVtcHR5LWhpc3RvcnkFY2xvc2UQZW1wdHktcGVybWlzc2lvbgplbXB0eS1uZXdzCGZhY2Vib29rFGZhY2Vib29rLWNpcmNsZS1maWxsCmVtcHR5LWxpc3QRYXJyb3ctbGVmdC1kb3VibGUSYXJyb3ctcmlnaHQtZG91YmxlC2NvbHVtbi1saW5lBW9yZGVyD3JlZC1wYWNrZXQtZmlsbApyZWQtcGFja2V0AmVuBWxldmVsB3dhcm5pbmcFcXpvbmURcXpvbmUtY2lyY2xlLWZpbGwDbWFuE21vbWVudHMtY2lyY2VsLWZpbGwHbW9tZW50cwV3b21hbg5xcS1jaXJjbGUtZmlsbAdxcS1maWxsBXdlaWJvEXdlaWJvLWNpcmNsZS1maWxsBnRhb2JhbxJ0YW9iYW8tY2lyY2xlLWZpbGwJY2hlY2ttYXJrBnR3aXR0ZRN0d2l0dGVyLWNpcmNsZS1maWxsDWFycm93LXVwLWZpbGwSd2VpeGluLWNpcmNsZS1maWxsC3dlaXhpbi1maWxsFHpoaWZ1YmFvLWNpcmNsZS1maWxsCHpoaWZ1YmFvBXpoaWh1CnNlcnZlci1tYW4FZXJyb3INaW50ZWdyYWwtZmlsbBF6aGlodS1jaXJjbGUtZmlsbAJ6aA90aHVtYi1kb3duLWZpbGwKdGh1bWItZG93bg10aHVtYi11cC1maWxsCHRodW1iLXVwC3NlcnZlci1maWxsBnJlbG9hZAtpbmZvLWNpcmNsZQZjYW1lcmEFaGVhcnQHcHVzaHBpbgZmb2xkZXIFbW92aWUNY2hlY2tib3gtbWFyawpoZWFydC1maWxsEW1pbnVzLXNxdWFyZS1maWxsEHBsdXMtc3F1YXJlLWZpbGwMcHVzaHBpbi1maWxsC2NhbWVyYS1maWxsDHNldHRpbmctZmlsbAZnb29nbGUCaWUKYXBwbGUtZmlsbBJjaHJvbWUtY2lyY2xlLWZpbGwSZ2l0aHViLWNpcmNsZS1maWxsDklFLWNpcmNsZS1maWxsEmdvb2dsZS1jaXJjbGUtZmlsbAZjb3Vwb24LY291cG9uLWZpbGwFcGF1c2UDY3V0C2ZpbmdlcnByaW50CWhvbWUtZmlsbARob21lE2hvdXJnbGFzcy1oYWxmLWZpbGwJaG91cmdsYXNzCWxvY2stb3BlbhBsb2NrLW9wZW5lZC1maWxsCWxvY2stZmlsbARsb2NrCnBob3RvLWZpbGwFcGhvdG8AAAAAAAH//wACAAEAAAAMAAAAFgAAAAIAAQADANUAAQAEAAAAAgAAAAAAAAABAAAAAN4GKm4AAAAA3Do8AgAAAADcOjwC') format('truetype');\n\t}\n\n\t/* #endif */\n\n\t.u-icon {\n\t\t/* #ifndef APP-NVUE */\n\t\tdisplay: flex;\n\t\t/* #endif */\n\t\talign-items: center;\n\n\t\t&--left {\n\t\t\tflex-direction: row-reverse;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t&--right {\n\t\t\tflex-direction: row;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t&--top {\n\t\t\tflex-direction: column-reverse;\n\t\t\tjustify-content: center;\n\t\t}\n\n\t\t&--bottom {\n\t\t\tflex-direction: column;\n\t\t\tjustify-content: center;\n\t\t}\n\n\t\t&__icon {\n\t\t\tfont-family: uicon-iconfont;\n\t\t\tposition: relative;\n\t\t\t@include flex;\n\t\t\talign-items: center;\n\n\t\t\t&--primary {\n\t\t\t\tcolor: $u-icon-primary;\n\t\t\t}\n\n\t\t\t&--success {\n\t\t\t\tcolor: $u-icon-success;\n\t\t\t}\n\n\t\t\t&--error {\n\t\t\t\tcolor: $u-icon-error;\n\t\t\t}\n\n\t\t\t&--warning {\n\t\t\t\tcolor: $u-icon-warning;\n\t\t\t}\n\n\t\t\t&--info {\n\t\t\t\tcolor: $u-icon-info;\n\t\t\t}\n\t\t}\n\n\t\t&__img {\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\theight: auto;\n\t\t\twill-change: transform;\n\t\t\t/* #endif */\n\t\t}\n\n\t\t&__label {\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\tline-height: $u-icon-label-line-height;\n\t\t\t/* #endif */\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-icon.vue?vue&type=style&index=0&id=2ee87dc9&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-icon.vue?vue&type=style&index=0&id=2ee87dc9&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751873716846\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}