{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/backgroundList.vue?fe19", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/backgroundList.vue?030b", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/backgroundList.vue?41e4", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/backgroundList.vue?885b", "uni-app:///pages/subPackage/template/backgroundList.vue", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/backgroundList.vue?22e8", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/template/backgroundList.vue?b252"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "tabList", "name", "currentTab", "backgrounds", "backgroundTypes", "typeIndex", "newBackground", "type", "url", "uploadFileList", "deleteIndex", "isSelectMode", "showAddPopup", "showDeleteModal", "computed", "filteredBackgrounds", "onLoad", "methods", "loadBackgrounds", "onTabChange", "openAddPopup", "closeAddPopup", "onTypeChange", "afterRead", "newFile", "deleteUpload", "submitForm", "uni", "title", "icon", "newItem", "confirmDelete", "closeDeleteModal", "deleteBackground", "selectBackgroundImage", "success", "console", "duration", "setTimeout", "fail", "goBack"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACa;;;AAG1E;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAA8tB,CAAgB,8rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC8HlvB;EACAC;IACA;MACA;MACAC,UACA;QAAAC;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,EACA;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;QACAL;QACAM;QACAC;MACA;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;QACA;MACA;QACA;QACA;UAAA;QAAA;MACA;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA,oBACA;QACAjB;QACAM;QACAC;MACA,GACA;QACAP;QACAM;QACAC;MACA,GACA;QACAP;QACAM;QACAC;MACA,EACA;IACA;IAEA;IACAW;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACApB;QACAM;QACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAc;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;MACAA;MACAA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAC;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;;MAEA;MACA;;MAEA;MACAH;QACAC;QACAC;MACA;IACA;IAEA;IACAE;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;;QAEA;QACAN;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAK;MACA;QACA;QACAP;UACAC;QACA;;QAEA;QACAD;UACAnB;UACA2B;YACA;cACAC;;cAEA;cACAT;;cAEA;cACAA;;cAEA;cACAA;gBACAC;gBACAC;gBACAQ;cACA;;cAEA;cACAC;gBACAX;cACA;YACA;cACA;YACA;UACA;UACAY;YACAH;;YAEA;YACA;cACAT;cAEAA;cACAA;gBACAC;gBACAC;gBACAQ;cACA;cAEAC;gBACAX;cACA;YACA;cACAS;cACAT;cACAA;gBACAC;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAW;MACAb;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjZA;AAAA;AAAA;AAAA;AAA+hC,CAAgB,+8BAAG,EAAC,C;;;;;;;;;;;ACAnjC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/subPackage/template/backgroundList.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/subPackage/template/backgroundList.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./backgroundList.vue?vue&type=template&id=50f2fe31&\"\nvar renderjs\nimport script from \"./backgroundList.vue?vue&type=script&lang=js&\"\nexport * from \"./backgroundList.vue?vue&type=script&lang=js&\"\nimport style0 from \"./backgroundList.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/subPackage/template/backgroundList.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./backgroundList.vue?vue&type=template&id=50f2fe31&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uUpload: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-upload/u-upload\" */ \"@/uni_modules/uview-ui/components/u-upload/u-upload.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-modal/u-modal\" */ \"@/uni_modules/uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.filteredBackgrounds.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./backgroundList.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./backgroundList.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"background-list-container\">\n\t\t<!-- Tab导航栏 -->\n\t\t<view class=\"tab-section\">\n\t\t\t<uv-tabs \n\t\t\t\t:list=\"tabList\" \n\t\t\t\t:current=\"currentTab\" \n\t\t\t\t@change=\"onTabChange\"\n\t\t\t\tlineColor=\"#8B1538\"\n\t\t\t\t:activeStyle=\"{\n\t\t\t\t\tcolor: '#8B1538',\n\t\t\t\t\tfontWeight: 'bold'\n\t\t\t\t}\"\n\t\t\t></uv-tabs>\n\t\t</view>\n\t\t\n\t\t<!-- 内容区域 -->\n\t\t<view class=\"content-section\">\n\t\t\t<view v-if=\"filteredBackgrounds.length === 0\" class=\"empty-list\">\n\t\t\t\t<text class=\"empty-text\">暂无背景图片</text>\n\t\t\t</view>\n\t\t\t<view v-else class=\"background-grid\">\n\t\t\t\t<view \n\t\t\t\t\tclass=\"background-item\" \n\t\t\t\t\tv-for=\"(item, index) in filteredBackgrounds\" \n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t@tap=\"selectBackgroundImage(item)\"\n\t\t\t\t\t:class=\"{'selectable': isSelectMode}\"\n\t\t\t\t>\n\t\t\t\t\t<view class=\"background-preview\">\n\t\t\t\t\t\t<image \n\t\t\t\t\t\t\t:src=\"item.url\" \n\t\t\t\t\t\t\tmode=\"aspectFit\" \n\t\t\t\t\t\t\tclass=\"background-image\"\n\t\t\t\t\t\t></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"background-info\">\n\t\t\t\t\t\t<text class=\"background-name\">{{ item.name }}</text>\n\t\t\t\t\t\t<view class=\"background-type\">{{ item.type }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"!isSelectMode\" class=\"delete-icon\" @tap.stop=\"confirmDelete(index)\">\n\t\t\t\t\t\t<u-icon name=\"trash\" color=\"#8B1538\" size=\"20\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-else class=\"select-icon\">\n\t\t\t\t\t\t<u-icon name=\"checkmark\" color=\"#8B1538\" size=\"20\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 添加按钮 -->\n\t\t<view class=\"add-button\" v-if=\"!isSelectMode\" @tap=\"openAddPopup\">\n\t\t\t<u-icon name=\"plus\" color=\"#FFFFFF\" size=\"24\"></u-icon>\n\t\t</view>\n\t\t\n\t\t<!-- 添加背景弹窗 -->\n\t\t<u-popup\n\t\t\t:show=\"showAddPopup\"\n\t\t\tmode=\"center\"\n\t\t\t:closeOnClickOverlay=\"false\"\n\t\t\t:safeAreaInsetBottom=\"true\"\n\t\t\t:round=\"10\"\n\t\t\t:closeable=\"true\"\n\t\t\t@close=\"closeAddPopup\"\n\t\t>\n\t\t\t<view class=\"add-popup-content\">\n\t\t\t\t<view class=\"popup-title\">添加背景图片</view>\n\t\t\t\t`\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">图片名称</text>\n\t\t\t\t\t<input \n\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\tv-model=\"newBackground.name\" \n\t\t\t\t\t\tplaceholder=\"请输入图片名称\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">图片类型</text>\n\t\t\t\t\t<picker \n\t\t\t\t\t\t@change=\"onTypeChange\" \n\t\t\t\t\t\t:value=\"typeIndex\" \n\t\t\t\t\t\t:range=\"backgroundTypes\"\n\t\t\t\t\t\tclass=\"form-picker\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<view class=\"picker-content\">\n\t\t\t\t\t\t\t<text class=\"picker-text\">{{ backgroundTypes[typeIndex] }}</text>\n\t\t\t\t\t\t\t<u-icon name=\"arrow-down\" size=\"14\" color=\"#666\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"form-label\">上传图片</text>\n\t\t\t\t\t<u-upload\n\t\t\t\t\t\t:fileList=\"uploadFileList\"\n\t\t\t\t\t\t@afterRead=\"afterRead\"\n\t\t\t\t\t\t@delete=\"deleteUpload\"\n\t\t\t\t\t\t:maxCount=\"1\"\n\t\t\t\t\t\t:width=\"200\"\n\t\t\t\t\t\t:height=\"200\"\n\t\t\t\t\t\tuploadText=\"选择图片\"\n\t\t\t\t\t\timageMode=\"aspectFit\"\n\t\t\t\t\t></u-upload>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"popup-actions\">\n\t\t\t\t\t<view class=\"popup-btn cancel\" @tap=\"closeAddPopup\">取消</view>\n\t\t\t\t\t<view class=\"popup-btn confirm\" @tap=\"submitForm\">确认</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</u-popup>\n\t\t\n\t\t<!-- 删除确认弹窗 -->\n\t\t<u-modal\n\t\t\t:show=\"showDeleteModal\"\n\t\t\ttitle=\"删除确认\"\n\t\t\tcontent=\"确定要删除这张背景图片吗？\"\n\t\t\t:showCancelButton=\"true\"\n\t\t\t@confirm=\"deleteBackground\"\n\t\t\t@cancel=\"closeDeleteModal\"\n\t\t></u-modal>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// Tab数据\n\t\t\t\ttabList: [\n\t\t\t\t\t{ name: '全部' },\n\t\t\t\t\t{ name: '管理局' },\n\t\t\t\t\t{ name: '大院' },\n\t\t\t\t\t{ name: '其他' }\n\t\t\t\t],\n\t\t\t\tcurrentTab: 0,\n\t\t\t\t\n\t\t\t\t// 背景图片列表\n\t\t\t\tbackgrounds: [],\n\t\t\t\t\n\t\t\t\t// 背景类型\n\t\t\t\tbackgroundTypes: ['管理局', '大院', '其他'],\n\t\t\t\ttypeIndex: 0,\n\t\t\t\t\n\t\t\t\t// 新增背景数据\n\t\t\t\tnewBackground: {\n\t\t\t\t\tname: '',\n\t\t\t\t\ttype: '管理局',\n\t\t\t\t\turl: ''\n\t\t\t\t},\n\t\t\t\t\n\t\t\t\t// 上传相关\n\t\t\t\tuploadFileList: [],\n\t\t\t\t\n\t\t\t\t// 当前要删除的索引\n\t\t\t\tdeleteIndex: -1,\n\n\t\t\t\t// 是否是选择模式\n\t\t\t\tisSelectMode: false,\n\n\t\t\t\t// 弹窗控制\n\t\t\t\tshowAddPopup: false,\n\t\t\t\tshowDeleteModal: false\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 根据当前选择的Tab过滤背景图片\n\t\t\tfilteredBackgrounds() {\n\t\t\t\tif (this.currentTab === 0) {\n\t\t\t\t\treturn this.backgrounds;\n\t\t\t\t} else {\n\t\t\t\t\tconst tabType = this.tabList[this.currentTab].name;\n\t\t\t\t\treturn this.backgrounds.filter(item => item.type === tabType);\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\t// 检查是否是选择模式\n\t\t\tif (options && options.select === 'true') {\n\t\t\t\tthis.isSelectMode = true;\n\t\t\t}\n\t\t\t\n\t\t\t// 模拟加载初始数据\n\t\t\tthis.loadBackgrounds();\n\t\t},\n\t\tmethods: {\n\t\t\t// 加载背景图片数据\n\t\t\tloadBackgrounds() {\n\t\t\t\t// 这里应该是从API获取数据，现在用模拟数据\n\t\t\t\tthis.backgrounds = [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '管理局会议室背景',\n\t\t\t\t\t\ttype: '管理局',\n\t\t\t\t\t\turl: 'https://www.chuantiba.com/api/storage/upload/1701705600/1701752546213847.png'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '大院活动背景',\n\t\t\t\t\t\ttype: '大院',\n\t\t\t\t\t\turl: 'https://www.chuantiba.com/api/storage/upload/1701878400/170193741739568.png'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '其他类型背景',\n\t\t\t\t\t\ttype: '其他',\n\t\t\t\t\t\turl: 'https://www.chuantiba.com/api/storage/upload/1701878400/1701926050311878.png'\n\t\t\t\t\t}\n\t\t\t\t];\n\t\t\t},\n\t\t\t\n\t\t\t// Tab切换事件\n\t\t\tonTabChange(e) {\n\t\t\t\tthis.currentTab = e.index;\n\t\t\t},\n\t\t\t\n\t\t\t// 打开添加弹窗\n\t\t\topenAddPopup() {\n\t\t\t\tthis.showAddPopup = true;\n\t\t\t},\n\n\t\t\t// 关闭添加弹窗\n\t\t\tcloseAddPopup() {\n\t\t\t\t// 重置表单\n\t\t\t\tthis.newBackground = {\n\t\t\t\t\tname: '',\n\t\t\t\t\ttype: '管理局',\n\t\t\t\t\turl: ''\n\t\t\t\t};\n\t\t\t\tthis.typeIndex = 0;\n\t\t\t\tthis.uploadFileList = [];\n\t\t\t\tthis.showAddPopup = false;\n\t\t\t},\n\t\t\t\n\t\t\t// 类型选择变更\n\t\t\tonTypeChange(e) {\n\t\t\t\tthis.typeIndex = e.detail.value;\n\t\t\t\tthis.newBackground.type = this.backgroundTypes[this.typeIndex];\n\t\t\t},\n\t\t\t\n\t\t\t// 上传后回调\n\t\t\tafterRead(event) {\n\t\t\t\tconst file = event.file;\n\t\t\t\t// 这里应该调用上传API，现在只是更新本地预览\n\t\t\t\tconst newFile = {};\n\t\t\t\t// 复制 file 对象的所有属性\n\t\t\t\tfor (let key in file) {\n\t\t\t\t\tif (file.hasOwnProperty(key)) {\n\t\t\t\t\t\tnewFile[key] = file[key];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// 添加额外属性\n\t\t\t\tnewFile.status = 'success';\n\t\t\t\tnewFile.message = '上传成功';\n\t\t\t\tthis.uploadFileList.push(newFile);\n\t\t\t\t\n\t\t\t\tthis.newBackground.url = file.url;\n\t\t\t},\n\t\t\t\n\t\t\t// 删除上传文件\n\t\t\tdeleteUpload() {\n\t\t\t\tthis.uploadFileList = [];\n\t\t\t\tthis.newBackground.url = '';\n\t\t\t},\n\t\t\t\n\t\t\t// 提交表单\n\t\t\tsubmitForm() {\n\t\t\t\t// 表单验证\n\t\t\t\tif (!this.newBackground.name.trim()) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入图片名称',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (!this.newBackground.url) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请上传图片',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 添加到列表\n\t\t\t\tconst newItem = {};\n\t\t\t\t// 复制 newBackground 对象的所有属性\n\t\t\t\tfor (let key in this.newBackground) {\n\t\t\t\t\tif (this.newBackground.hasOwnProperty(key)) {\n\t\t\t\t\t\tnewItem[key] = this.newBackground[key];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.backgrounds.push(newItem);\n\t\t\t\t\n\t\t\t\t// 关闭弹窗\n\t\t\t\tthis.closeAddPopup();\n\t\t\t\t\n\t\t\t\t// 提示成功\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '添加成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 确认删除弹窗\n\t\t\tconfirmDelete(index) {\n\t\t\t\tthis.deleteIndex = index;\n\t\t\t\tthis.showDeleteModal = true;\n\t\t\t},\n\n\t\t\t// 关闭删除弹窗\n\t\t\tcloseDeleteModal() {\n\t\t\t\tthis.showDeleteModal = false;\n\t\t\t\tthis.deleteIndex = -1;\n\t\t\t},\n\n\t\t\t// 删除背景图片\n\t\t\tdeleteBackground() {\n\t\t\t\tif (this.deleteIndex !== -1) {\n\t\t\t\t\t// 从列表中删除\n\t\t\t\t\tthis.backgrounds.splice(this.deleteIndex, 1);\n\t\t\t\t\tthis.deleteIndex = -1;\n\t\t\t\t\tthis.showDeleteModal = false;\n\n\t\t\t\t\t// 提示成功\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '删除成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 选择背景图片并返回\n\t\t\tselectBackgroundImage(item) {\n\t\t\t\tif (this.isSelectMode) {\n\t\t\t\t\t// 显示加载中\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '正在处理图片...'\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 先下载网络图片到本地\n\t\t\t\t\tuni.downloadFile({\n\t\t\t\t\t\turl: item.url,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.statusCode === 200) {\n\t\t\t\t\t\t\t\tconsole.log('图片下载成功，临时路径:', res.tempFilePath);\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 将本地临时文件路径保存到存储\n\t\t\t\t\t\t\t\tuni.setStorageSync('selectedBackgroundImage', res.tempFilePath);\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 隐藏加载提示\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 提示成功\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '已选择背景',\n\t\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 返回上一页\n\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t\t}, 300);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthrow new Error('下载状态码异常：' + res.statusCode);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('下载图片失败:', err);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 尝试直接使用URL\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\tuni.setStorageSync('selectedBackgroundImage', item.url);\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '图片可能无法正确显示',\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t\t}, 300);\n\t\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\t\tconsole.error('保存URL失败:', e);\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '选择背景失败',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 返回上一页\n\t\t\tgoBack() {\n\t\t\t\tuni.navigateBack();\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.background-list-container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\theight: 100vh;\n\t\tbackground-color: #f5f5f5;\n\t}\n\t\n\t/* 导航栏样式 */\n\t.navbar {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\theight: 90rpx;\n\t\tpadding: 0 30rpx;\n\t\tbackground-color: #FFFFFF;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t}\n\t\n\t.navbar-left {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t\n\t.navbar-title {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tmargin-left: 8rpx;\n\t}\n\t\n\t.navbar-center {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\t\n\t.navbar-right {\n\t\twidth: 60rpx;\n\t}\n\t\n\t.tab-section {\n\t\tbackground-color: #FFFFFF;\n\t}\n\t\n\t.content-section {\n\t\tflex: 1;\n\t\tpadding: 20rpx;\n\t\toverflow-y: auto;\n\t}\n\t\n\t.empty-list {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\theight: 300rpx;\n\t}\n\t\n\t.empty-text {\n\t\tcolor: #999;\n\t\tfont-size: 28rpx;\n\t}\n\t\n\t.background-grid {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 20rpx;\n\t}\n\t\n\t.background-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbackground-color: #FFFFFF;\n\t\tborder-radius: 10rpx;\n\t\tpadding: 20rpx;\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n\t\tposition: relative;\n\t}\n\t\n\t.background-preview {\n\t\twidth: 120rpx;\n\t\theight: 120rpx;\n\t\tborder-radius: 8rpx;\n\t\toverflow: hidden;\n\t\tmargin-right: 20rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground-color: #f8f8f8;\n\t}\n\t\n\t.background-image {\n\t\tmax-width: 100%;\n\t\tmax-height: 100%;\n\t\twidth: auto;\n\t\theight: auto;\n\t}\n\t\n\t.background-info {\n\t\tflex: 1;\n\t}\n\t\n\t.background-name {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 10rpx;\n\t}\n\t\n\t.background-type {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t\tbackground-color: #f0f0f0;\n\t\tdisplay: inline-block;\n\t\tpadding: 4rpx 16rpx;\n\t\tborder-radius: 20rpx;\n\t}\n\t\n\t.delete-icon, .select-icon {\n\t\tpadding: 16rpx;\n\t}\n\t\n\t.selectable {\n\t\tcursor: pointer;\n\t\ttransition: all 0.2s;\n\t}\n\t\n\t.selectable:hover, .selectable:active {\n\t\tbackground-color: #f5f5f5;\n\t\ttransform: translateY(-2rpx);\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n\t}\n\t\n\t.add-button {\n\t\tposition: fixed;\n\t\tright: 40rpx;\n\t\tbottom: 40rpx;\n\t\twidth: 100rpx;\n\t\theight: 100rpx;\n\t\tbackground-color: #8B1538;\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tbox-shadow: 0 4rpx 16rpx rgba(139, 21, 56, 0.3);\n\t\tz-index: 10;\n\t}\n\t\n\t/* 弹窗样式 */\n\t.add-popup-content {\n\t\twidth: 600rpx;\n\t\tpadding: 30rpx;\n\t}\n\t\n\t.popup-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\ttext-align: center;\n\t\tmargin-bottom: 30rpx;\n\t\tcolor: #333;\n\t}\n\t\n\t.form-item {\n\t\tmargin-bottom: 30rpx;\n\t}\n\t\n\t.form-label {\n\t\tdisplay: block;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tmargin-bottom: 16rpx;\n\t}\n\t\n\t.form-input {\n\t\twidth: 100%;\n\t\theight: 80rpx;\n\t\tborder: 1px solid #ddd;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 0 20rpx;\n\t\tbox-sizing: border-box;\n\t\tfont-size: 28rpx;\n\t}\n\t\n\t.form-picker {\n\t\twidth: 100%;\n\t\theight: 80rpx;\n\t\tborder: 1px solid #ddd;\n\t\tborder-radius: 8rpx;\n\t\tfont-size: 28rpx;\n\t}\n\t\n\t.picker-content {\n\t\theight: 80rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 0 20rpx;\n\t}\n\t\n\t.popup-actions {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tmargin-top: 40rpx;\n\t}\n\t\n\t.popup-btn {\n\t\twidth: 45%;\n\t\theight: 80rpx;\n\t\tborder-radius: 8rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tfont-size: 28rpx;\n\t}\n\t\n\t.cancel {\n\t\tbackground-color: #f5f5f5;\n\t\tcolor: #666;\n\t}\n\t\n\t.confirm {\n\t\tbackground-color: #8B1538;\n\t\tcolor: #fff;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./backgroundList.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./backgroundList.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751873713570\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}