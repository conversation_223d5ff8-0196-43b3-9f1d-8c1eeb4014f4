{"version": 3, "sources": ["webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/components/vol-form/vol-form.vue?66b4", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/components/vol-form/vol-form.vue?5c9f", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/components/vol-form/vol-form.vue?99c9", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/components/vol-form/vol-form.vue?3e46", "uni-app:///components/vol-form/vol-form.vue", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/components/vol-form/vol-form.vue?a8cd", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/components/vol-form/vol-form.vue?c256"], "names": ["components", "lotus<PERSON><PERSON><PERSON>", "props", "formOptions", "type", "default", "formFields", "padding", "labelWidth", "labelPosition", "loadKey", "uploadBefore", "name", "data", "lotusAddressData", "visible", "provinceName", "cityName", "townName", "cityItem", "field", "region", "showFilter", "searchText", "inF<PERSON><PERSON>ields", "inFormOptions", "maxHeight", "popupHeight", "picker<PERSON><PERSON><PERSON>", "picker<PERSON>odel", "pickerCurrentItem", "pickerCurrentRangeIndex", "actionSheetModel", "actionSascaderCurrentItem", "title", "checkStrictly", "cancel", "confirm", "actionSheetCurrentItem", "min", "max", "actionSheetSelectValues", "numberModel", "numberType", "numberCurrentItem", "imgFields", "created", "option", "mounted", "uni", "success", "_this", "methods", "inputConfirm", "convertImgArr", "url", "orginUrl", "initDataSource", "result", "cascaderConfirm", "showActionSheet", "actionClick", "isMultiSelect", "actionConfirmClick", "isActionSelected", "formatDicValueList", "value", "_textArr", "getAllParentId", "id", "index", "node", "ids", "getCascaderNames", "i", "names", "formatDicValue", "showPicker", "val", "setPickerValue", "pickerConfirm", "pickerClose", "hideKeyboard", "reset", "key", "validate", "_option", "showNumber", "numberBackspace", "numberChange", "_val", "formatReadonlyValue", "getImgSrcs", "afterRead", "lists", "fileListLen", "item", "status", "message", "fileName", "lastIndex", "uploadFilePromise", "filePath", "header", "formData", "setTimeout", "resolve", "fail", "deletePic", "extraClick", "showCascaderSheet", "onCitySelect", "console", "showCitySheet", "previewImage", "urls", "current", "radioOnChange", "watch", "handler", "immediate", "deep"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACuL;AACvL,gBAAgB,2LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iPAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,2UAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,mWAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClTA;AAAA;AAAA;AAAA;AAAysB,CAAgB,wrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC0O7tB;EACAA;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;QACA;MACA;IACA;EACA;EACAO;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;MACA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;QACAC;QACAd;QACAe;QAAA;QACAC;QACAC;QACAxB;MACA;MACAyB;QACAC;QACAC;MACA;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;MACA;QACA;UACAC;QACA;UACA;YACAA;UACA;UACAA;QACA;QACA;UACAA;QACA;UACAA;QACA;QACA;UACA;QACA;MACA;MAEA;QACAA;MACA;IACA;IACA;IACA;IACA;MACA;IACA;MACA;IACA;IACA;MACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACAC;QACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;MACA;MACA;QACA;QACA;UACA;YACAhD;UACA;YACAA;cACA;gBACAiD;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;UACA;YACAX;UACA;QACA;MACA;MACA;IACA;IACAY;MACA;MACA;IACA;IACAC;MACA;QAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UAAA;UACA;YACA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;UACA;YACA;UACA;QACA;UACA;QACA;QACA;MACA;MACA;MACA;MACA,wGACAzC;IACA;IACA0C;MACA;MACA;QACA1D;MACA;QACAA;MACA;MACA;QACA;MACA;MACA;IACA;IACA2D;MACA;MACA;QACA;QACA;MACA;MACA;MACA;QACA;QACA;UACA;QACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;MACA;MAEA;QACAC;MACA;MACAA;QACA;UACA;QACA;QACA;UACAC;QACA;UACAA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;QACA;MACA;QACAC;MACA;QACAA;MACA;MACA;MAAA,2BAEAC;QACAC;UACA;QACA;QACA;UACA;YAAA;UAAA;QACA;QACA;UACA;QACA;UACAC;QACA;MAAA;MAXA;QAAA,IACAD;QAAA,iBADAD;QAAA;MAYA;MAEA;IACA;IACAG;MACA;MACA;MAAA,6BACAC;QACA;UACA;QACA;QACA;UACAC;QACA;UACAA;QACA;MAAA;MARA;QAAA;MASA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACA;UACA;UACAC;QACA;QACAA;MACA;MACA;QACA;UACAA;QACA;UACAA;QACA;UACAA;QACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;MAAA;MAEA;QACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAjC;IACA;IACAkC;MAAA;MAAA,6BACAC;QACA;UACA;QACA;UACA;YACA;YACA;cACA;YACA;cAAA;cACA;YACA;UACA;YACA;UACA;QACA;MAAA;MAdA;QAAA;MAeA;IACA;IACAC;MAAA;MACA;QACA;MACA;QACA;QACA;UACA;QACA;UACA;QACA;QACA;MACA;MACA;QACA,iGACAC,QACAlF,UACA;UACA;QACA;UACA;QACA;QACA;MACA;MACA;IACA;IACAmF;MACA;MACA;IACA;IACAC;MACA;MACA;QACAtB;QACA;MACA;IACA;IACAuB;MACA;MACA;QACAC;MACA;QACAA;MACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA9C;kBAAA;kBAAA;gBAAA;gBAAA,iCACA;cAAA;gBAEA;gBACA+C;gBACA;kBACAA;gBACA;kBACA;oBACA;kBACA;oBACAA;kBACA;gBACA;gBACAC;gBACAD;kBACA,uEACAE;oBACAC;oBACAC;kBAAA,GACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACAxB;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAhB;gBACAsC;gBACAG;gBACA;kBACAC,iDACA;kBACA;kBACAD;gBACA;gBACA;kBACAF;kBACAC;kBACA3C;kBACAC;gBACA;gBACAuC;cAAA;gBAhBArB;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAmBA;IACA2B;MAAA;MACA;QACA;UACA9C;UAAA;UACA+C;UACA1F;UACA2F;YACA;YACA;UACA;UACAC;UACAtD;YACAuD;cACAC;YACA;UACA;UACAC;YACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAC;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACAjE;QACAkE;QACAC;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;;EAEA;EACAC;IACA9F;MACA+F;QACA;UACA;QACA;QACAP;QACA;QACA;MACA;;MACAQ;MACAC;IACA;IACAnH;MACAiH;QACA;QACA;QACA;MACA;MACAC;MACAC;IACA;IACAhG;MACA8F;QACA;UACA;QACA;QACA;QACA;MACA;MACAC;MACAC;IACA;IACAtH;MACAoH;QACAP;QACA;MACA;MACAQ;MACAC;IACA;EACA;AAEA;AAAA,4B;;;;;;;;;;;;;ACx3BA;AAAA;AAAA;AAAA;AAA4zC,CAAgB,wtCAAG,EAAC,C;;;;;;;;;;;ACAh1C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/vol-form/vol-form.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./vol-form.vue?vue&type=template&id=6726b7c4&scoped=true&\"\nvar renderjs\nimport script from \"./vol-form.vue?vue&type=script&lang=js&\"\nexport * from \"./vol-form.vue?vue&type=script&lang=js&\"\nimport style0 from \"./vol-form.vue?vue&type=style&index=0&id=6726b7c4&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6726b7c4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/vol-form/vol-form.vue\"\nexport default component.exports", "export * from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vol-form.vue?vue&type=template&id=6726b7c4&scoped=true&\"", "var components\ntry {\n  components = {\n    uToast: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-toast/u-toast\" */ \"@/uni_modules/uview-ui/components/u-toast/u-toast.vue\"\n      )\n    },\n    uParse: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-parse/u-parse\" */ \"@/uni_modules/uview-ui/components/u-parse/u-parse.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uRadioGroup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-radio-group/u-radio-group\" */ \"@/uni_modules/uview-ui/components/u-radio-group/u-radio-group.vue\"\n      )\n    },\n    uRadio: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-radio/u-radio\" */ \"@/uni_modules/uview-ui/components/u-radio/u-radio.vue\"\n      )\n    },\n    uUpload: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-upload/u-upload\" */ \"@/uni_modules/uview-ui/components/u-upload/u-upload.vue\"\n      )\n    },\n    uDatetimePicker: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-datetime-picker/u-datetime-picker\" */ \"@/uni_modules/uview-ui/components/u-datetime-picker/u-datetime-picker.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uSearch: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-search/u-search\" */ \"@/uni_modules/uview-ui/components/u-search/u-search.vue\"\n      )\n    },\n    volTree: function () {\n      return import(\n        /* webpackChunkName: \"components/vol-tree/vol-tree\" */ \"@/components/vol-tree/vol-tree.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l2 = _vm.__map(_vm.formOptions, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var l0 =\n      (item.readonly || item.disabled) && item.type == \"img\"\n        ? _vm.getImgSrcs(item)\n        : null\n    var m0 =\n      (item.readonly || item.disabled) && !(item.type == \"img\")\n        ? _vm.formatReadonlyValue(item)\n        : null\n    var g0 =\n      !(item.readonly || item.disabled) &&\n      !(item.type == \"editor\") &&\n      item.type == \"city\"\n        ? _vm.base.isEmpty(_vm.inFormFields[item.field], true)\n        : null\n    var g1 =\n      !(item.readonly || item.disabled) &&\n      !(item.type == \"editor\") &&\n      item.type == \"city\"\n        ? _vm.base.isEmpty(_vm.inFormFields[item.field], true)\n        : null\n    var g2 =\n      !(item.readonly || item.disabled) &&\n      !(item.type == \"editor\") &&\n      item.type == \"city\"\n        ? _vm.inFormFields[item.field].replace(/,/g, \"\")\n        : null\n    var g3 =\n      !(item.readonly || item.disabled) &&\n      !(item.type == \"editor\") &&\n      !(item.type == \"city\") &&\n      (item.type == \"date\" ||\n        item.type == \"datetime\" ||\n        item.type == \"month\") &&\n      item.range &&\n      item.type == \"date\"\n        ? (_vm.inFormFields[item.field][0] || \"\").substr(0, 10)\n        : null\n    var g4 =\n      !(item.readonly || item.disabled) &&\n      !(item.type == \"editor\") &&\n      !(item.type == \"city\") &&\n      (item.type == \"date\" ||\n        item.type == \"datetime\" ||\n        item.type == \"month\") &&\n      item.range &&\n      item.type == \"date\"\n        ? (_vm.inFormFields[item.field][1] || \"\").substr(0, 10)\n        : null\n    var g5 =\n      !(item.readonly || item.disabled) &&\n      !(item.type == \"editor\") &&\n      !(item.type == \"city\") &&\n      (item.type == \"date\" ||\n        item.type == \"datetime\" ||\n        item.type == \"month\") &&\n      !item.range &&\n      item.type == \"date\"\n        ? (_vm.inFormFields[item.field] || \"\").substr(0, 10)\n        : null\n    var g6 =\n      !(item.readonly || item.disabled) &&\n      !(item.type == \"editor\") &&\n      !(item.type == \"city\") &&\n      (item.type == \"date\" ||\n        item.type == \"datetime\" ||\n        item.type == \"month\") &&\n      !item.range &&\n      !(item.type == \"date\") &&\n      item.type == \"month\"\n        ? (_vm.inFormFields[item.field] || \"\").substr(0, 7)\n        : null\n    var g7 =\n      !(item.readonly || item.disabled) &&\n      !(item.type == \"editor\") &&\n      !(item.type == \"city\") &&\n      !(\n        item.type == \"date\" ||\n        item.type == \"datetime\" ||\n        item.type == \"month\"\n      ) &&\n      !item.range\n        ? [\"select\", \"selectList\", \"checkbox\", \"cascader\"].indexOf(item.type)\n        : null\n    var g8 =\n      !(item.readonly || item.disabled) &&\n      !(item.type == \"editor\") &&\n      !(item.type == \"city\") &&\n      !(\n        item.type == \"date\" ||\n        item.type == \"datetime\" ||\n        item.type == \"month\"\n      ) &&\n      !item.range &&\n      g7 != -1\n        ? _vm.base.isEmpty(_vm.inFormFields[item.field], true)\n        : null\n    var g9 =\n      !(item.readonly || item.disabled) &&\n      !(item.type == \"editor\") &&\n      !(item.type == \"city\") &&\n      !(\n        item.type == \"date\" ||\n        item.type == \"datetime\" ||\n        item.type == \"month\"\n      ) &&\n      !item.range &&\n      g7 != -1\n        ? _vm.base.isEmpty(_vm.inFormFields[item.field], true)\n        : null\n    var m1 =\n      !(item.readonly || item.disabled) &&\n      !(item.type == \"editor\") &&\n      !(item.type == \"city\") &&\n      !(\n        item.type == \"date\" ||\n        item.type == \"datetime\" ||\n        item.type == \"month\"\n      ) &&\n      !item.range &&\n      g7 != -1\n        ? _vm.formatDicValue(item)\n        : null\n    var l1 =\n      !(item.readonly || item.disabled) &&\n      !(item.type == \"editor\") &&\n      !(item.type == \"city\") &&\n      !(\n        item.type == \"date\" ||\n        item.type == \"datetime\" ||\n        item.type == \"month\"\n      ) &&\n      !item.range &&\n      !(g7 != -1) &&\n      !(item.type == \"group\") &&\n      !(item.type == \"number\") &&\n      !(item.type == \"decimal\") &&\n      item.type == \"radio\"\n        ? _vm.__map(item.data, function (option, opIndex) {\n            var $orig = _vm.__get_orig(option)\n            var a0 = {\n              \"margin-bottom\": item.placement == \"column\" ? \"30rpx\" : 0,\n              \"margin-right\": item.placement == \"column\" ? \"0\" : \"30rpx\",\n            }\n            return {\n              $orig: $orig,\n              a0: a0,\n            }\n          })\n        : null\n    var a1 =\n      !(item.readonly || item.disabled) &&\n      !(item.type == \"editor\") &&\n      !(item.type == \"city\") &&\n      !(\n        item.type == \"date\" ||\n        item.type == \"datetime\" ||\n        item.type == \"month\"\n      ) &&\n      !item.range &&\n      !(g7 != -1) &&\n      !(item.type == \"group\") &&\n      !(item.type == \"number\") &&\n      !(item.type == \"decimal\") &&\n      !(item.type == \"radio\") &&\n      item.type == \"switch\"\n        ? {\n            \"margin-right\": \"40rpx\",\n          }\n        : null\n    return {\n      $orig: $orig,\n      l0: l0,\n      m0: m0,\n      g0: g0,\n      g1: g1,\n      g2: g2,\n      g3: g3,\n      g4: g4,\n      g5: g5,\n      g6: g6,\n      g7: g7,\n      g8: g8,\n      g9: g9,\n      m1: m1,\n      l1: l1,\n      a1: a1,\n    }\n  })\n  var l3 = _vm.__map(_vm.actionSheetCurrentItem.data, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g10 =\n      !item.hidden &&\n      (!_vm.searchText || item.value.indexOf(_vm.searchText) != -1)\n    var m2 = _vm.actionSheetModel && _vm.isActionSelected(item)\n    return {\n      $orig: $orig,\n      g10: g10,\n      m2: m2,\n    }\n  })\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, item, imgIndex) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item,\n        imgIndex = _temp2.imgIndex\n      var _temp, _temp2\n      return _vm.previewImage(item, imgIndex)\n    }\n    _vm.e1 = function ($event) {\n      _vm.actionSheetModel = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.searchText = \"\"\n    }\n    _vm.e3 = _vm.actionSascaderCurrentItem.cancel\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l2: l2,\n        l3: l3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vol-form.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vol-form.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<u-toast ref=\"uToast\"></u-toast>\n\t\t<view :style=\"{padding:padding+'rpx',display:item.hidden?'none':''}\"\n\t\t\t:class=\"[labelPosition=='left'?'left-form-item':'top-form-item',item.type=='group'?'f-form-group':'']\"\n\t\t\tclass=\"f-form-item\" v-for=\"(item,index) in formOptions\" :key=\"index\">\n\t\t\t<view class=\"f-form-label\" v-if=\"item.type!='group'\" :style=\"{maxWidth:labelWidth+'px',minWidth:'80px'}\">\n\t\t\t\t<text class=\"f-form-label-required\" v-if=\"item.require||item.required\">*</text>\n\t\t\t\t<text>{{item.title}}</text>\n\t\t\t</view>\n\t\t\t<view v-if=\"item.readonly||item.disabled\" style=\"flex: 1;font-size: 15px;text-align: right;\">\n\t\t\t\t<view v-if=\"item.type=='img'\" class=\"readonly-imgs\">\n\t\t\t\t\t<image style=\"width: 70px;height: 70px;margin-left: 20rpx;border-radius: 10rpx;\"\n\t\t\t\t\t\t@click=\"previewImage(item,imgIndex)\" v-for=\"(src,imgIndex) in getImgSrcs(item)\" :key=\"imgIndex\"\n\t\t\t\t\t\t:src=\"src.url\"></image>\n\t\t\t\t</view>\n\t\t\t\t<text v-else> {{formatReadonlyValue(item)}}</text>\n\t\t\t</view>\n\t\t\t<view v-else-if=\"item.type=='editor'\">\n\t\t\t\t<u-parse :content=\"inFormFields[item.field]\"></u-parse>\n\t\t\t</view>\n\n\n\t\t\t<view class=\"f-form-content f-form-content-select\" @click=\"showCitySheet(item)\"\n\t\t\t\tv-else-if=\"item.type=='city'\">\n\t\t\t\t<view style=\"flex:1;\">\n\t\t\t\t\t<view style=\"color:rgb(192 196 204);font-size:15px;padding-right: 12rpx;\"\n\t\t\t\t\t\tv-show=\"base.isEmpty(inFormFields[item.field],true)\">\n\t\t\t\t\t\t{{'请选择'+item.title}}\n\t\t\t\t\t</view>\n\t\t\t\t\t<view style=\"font-size:15px;\" v-show=\"!base.isEmpty(inFormFields[item.field],true)\">\n\t\t\t\t\t\t{{inFormFields[item.field].replace(/,/g,'')}}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<u-icon color=\"rgb(186 186 186)\" size=\"15\" name=\"arrow-right\"></u-icon>\n\t\t\t</view>\n\n\t\t\t<template v-else-if=\"item.type=='date'||item.type=='datetime'||item.type=='month'\">\n\t\t\t\t<template v-if=\"item.range\">\n\t\t\t\t\t<view style=\"flex: 1;\" :style=\"{'max-width': item.type=='date'?'120rpx':'30rpx'}\"></view>\n\t\t\t\t\t<view class=\"f-form-content f-form-content-select\" style=\"text-align: left;\"\n\t\t\t\t\t\t@click=\"showPicker(item,0)\">\n\t\t\t\t\t\t<view style=\"color:rgb(192 196 204);font-size:15px;\" v-show=\"!inFormFields[item.field][0]\">\n\t\t\t\t\t\t\t开始时间\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view style=\"flex:1;\">\n\t\t\t\t\t\t\t<view style=\"font-size:15px;\">\n\t\t\t\t\t\t\t\t{{item.type=='date'?(inFormFields[item.field][0]||'').substr(0,10):inFormFields[item.field][0]}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- <u-icon name=\"arrow-right\"></u-icon> -->\n\t\t\t\t\t</view>\n\t\t\t\t\t<text style=\"margin:0 0rpx;\">-</text>\n\t\t\t\t\t<view class=\"f-form-content f-form-content-select\" @click=\"showPicker(item,1)\">\n\t\t\t\t\t\t<view style=\"color:rgb(192 196 204);font-size:15px;text-align: right; width: 100%;\"\n\t\t\t\t\t\t\tv-show=\"!inFormFields[item.field][1]\">\n\t\t\t\t\t\t\t结束时间\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view style=\"flex:1;\">\n\t\t\t\t\t\t\t<view style=\"font-size:15px;text-align: right;\">\n\t\t\t\t\t\t\t\t{{item.type=='date'?(inFormFields[item.field][1]||'').substr(0,10):inFormFields[item.field][1]}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- <u-icon name=\"arrow-right\"></u-icon> -->\n\t\t\t\t\t</view>\n\t\t\t\t</template>\n\t\t\t\t<view v-else class=\"f-form-content f-form-content-select\" @click=\"showPicker(item)\">\n\t\t\t\t\t<view style=\"color:rgb(192 196 204);font-size:15px;width: 100%;padding-right: 10rpx;\"\n\t\t\t\t\t\tv-show=\"!inFormFields[item.field]\">\n\t\t\t\t\t\t{{'请选择'+item.title}}\n\t\t\t\t\t</view>\n\t\t\t\t\t<view style=\"flex:1;\">\n\t\t\t\t\t\t<view style=\"font-size:15px;padding-right: 12rpx;\">\n\t\t\t\t\t\t\t{{item.type=='date'?(inFormFields[item.field]||'').substr(0,10):\n\t\t\t\t\t\t\t  (item.type=='month'?(inFormFields[item.field]||'').substr(0,7):inFormFields[item.field])\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<u-icon color=\"rgb(186 186 186)\" size=\"15\" name=\"arrow-right\"></u-icon>\n\t\t\t\t</view>\n\t\t\t</template>\n\n\t\t\t<template v-else-if=\"item.range\">\n\t\t\t\t<view style=\"flex: 1;max-width: 120rpx;\"></view>\n\t\t\t\t<view class=\"f-form-content f-form-content-select\" style=\"text-align: left;\">\n\t\t\t\t\t<view style=\"flex:1;\">\n\t\t\t\t\t\t<view style=\"font-size:15px;\">\n\t\t\t\t\t\t\t<input placeholder-style=\"color:rgb(192 196 204);font-size:15px;\" type=\"number\"\n\t\t\t\t\t\t\t\tv-model=\"inFormFields[item.field][0]\" border=\"none\" :ref=\"item.field\"\n\t\t\t\t\t\t\t\t:placeholder=\"item.placeholder||('请输入'+item.title)\"></input>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<text style=\"margin:0 0rpx;\">-</text>\n\t\t\t\t<view class=\"f-form-content f-form-content-select\">\n\t\t\t\t\t<input placeholder-style=\"color:rgb(192 196 204);font-size:15px;\" type=\"number\"\n\t\t\t\t\t\tv-model=\"inFormFields[item.field][1]\" border=\"none\" :ref=\"item.field\"\n\t\t\t\t\t\t:placeholder=\"item.placeholder||('请输入'+item.title)\"></input>\n\t\t\t\t</view>\n\t\t\t</template>\n\n\t\t\t<view class=\"f-form-content f-form-content-select\" @click=\"showActionSheet(item)\"\n\t\t\t\tv-else-if=\"['select','selectList','checkbox','cascader'].indexOf(item.type)!=-1\">\n\t\t\t\t<view style=\"flex:1;\">\n\t\t\t\t\t<view style=\"color:rgb(192 196 204);font-size:15px;padding-right: 12rpx;\"\n\t\t\t\t\t\tv-show=\"base.isEmpty(inFormFields[item.field],true)\">\n\t\t\t\t\t\t{{'请选择'+item.title}}\n\t\t\t\t\t</view>\n\t\t\t\t\t<view style=\"font-size:15px;\" v-show=\"!base.isEmpty(inFormFields[item.field],true)\">\n\t\t\t\t\t\t{{formatDicValue(item)}}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<u-icon color=\"rgb(186 186 186)\" size=\"15\" name=\"arrow-right\"></u-icon>\n\t\t\t</view>\n\n\n\t\t\t<view class=\"f-form-group-content\" :style=\"item.style\" v-else-if=\"item.type=='group'\">\n\t\t\t\t{{item.title||''}}\n\t\t\t</view>\n\n\t\t\t<view class=\"f-form-content\" v-else-if=\"item.type=='number'\">\n\t\t\t\t<input :focus=\"item.focus\" :ref=\"item.field\" placeholder-style=\"color:rgb(192 196 204);font-size:15px;\"\n\t\t\t\t\ttype=\"number\" v-model=\"formFields[item.field]\" border=\"none\"\n\t\t\t\t\t:placeholder=\"item.placeholder||('请输入'+item.title)\"></input>\n\t\t\t</view>\n\t\t\t<view class=\"f-form-content\" v-else-if=\"item.type=='decimal'\">\n\t\t\t\t<input :focus=\"item.focus\" :ref=\"item.field\" placeholder-style=\"color:rgb(192 196 204);font-size:15px;\"\n\t\t\t\t\ttype=\"digit\" v-model=\"formFields[item.field]\" border=\"none\"\n\t\t\t\t\t:placeholder=\"item.placeholder||('请输入'+item.title)\"></input>\n\t\t\t</view>\n\t\t\t<view class=\"f-form-content f-form-content-group\" v-else-if=\"item.type=='radio'\">\n\t\t\t\t<!--  <view> -->\n\t\t\t\t<u-radio-group @change=\"(val)=>{radioOnChange(val,item)}\" :placement=\"item.placement\"\n\t\t\t\t\tv-model=\"formFields[item.field]\">\n\t\t\t\t\t<u-radio v-for=\"(option,opIndex) in item.data\"\n\t\t\t\t\t\t:customStyle=\"{'margin-bottom':item.placement=='column'?'30rpx':0,'margin-right':item.placement=='column'?'0':'30rpx'}\"\n\t\t\t\t\t\t:label=\"option.value\" :name=\"option.key\">\n\t\t\t\t\t</u-radio>\n\t\t\t\t</u-radio-group>\n\t\t\t\t<!-- \t  </view> -->\n\t\t\t</view>\n\t\t\t<view class=\"f-form-content f-form-content-group\" v-else-if=\"item.type=='switch'\">\n\t\t\t\t<u-radio-group @change=\"(val)=>{radioOnChange(val,item)}\" :placement=\"item.placement\"\n\t\t\t\t\tv-model=\"formFields[item.field]\" placement=\"row\">\n\t\t\t\t\t<u-radio :customStyle=\"{'margin-right': '40rpx'}\" label=\"是\" :name=\"1\">\n\t\t\t\t\t</u-radio>\n\t\t\t\t\t<u-radio label=\"否\" :name=\"0\">\n\t\t\t\t\t</u-radio>\n\t\t\t\t</u-radio-group>\n\t\t\t</view>\n\t\t\t<view class=\"f-form-content\" v-else-if=\"item.type=='textarea'\">\n\t\t\t\t<textarea :focus=\"item.focus\" :ref=\"item.field\" auto-height style=\"width: 100%;padding-right: 8rpx;\"\n\t\t\t\t\t:maxlength=\"item.maxlength||100000\" v-model=\"inFormFields[item.field]\" border=\"none\"\n\t\t\t\t\t:placeholder=\"item.placeholder||('请输入'+item.title)\"></textarea>\n\t\t\t</view>\n\t\t\t<!-- \t -->\n\t\t\t<u-upload :ref=\"item.field\" :sizeType=\"['compressed']\" v-else-if=\"item.type=='img'\"\n\t\t\t\t:fileList=\"inFormFields[item.field]\" @afterRead=\"(event)=>{afterRead(item,event)}\"\n\t\t\t\t@delete=\"(event)=>{deletePic(item,event)}\" name=\"3\" :multiple=\"item.multiple\"\n\t\t\t\t:maxCount=\"item.maxCount||item.maxFile||1\" :previewFullImage=\"true\"></u-upload>\n\t\t\t<view class=\"f-form-content\" v-else-if=\"item.type=='password'\">\n\t\t\t\t<input placeholder-style=\"color:rgb(192 196 204);font-size:15px;\" type=\"password\"\n\t\t\t\t\tv-model=\"inFormFields[item.field]\" border=\"none\" :ref=\"item.field\"\n\t\t\t\t\t:placeholder=\"item.placeholder||('请输入'+item.title)\"></input>\n\t\t\t</view>\n\t\t\t<view class=\"f-form-content\" v-else>\n\t\t\t\t<input :focus=\"item.focus\" placeholder-style=\"color:rgb(192 196 204);font-size:15px;\" type=\"text\"\n\t\t\t\t\t@confirm=\"(e)=>{inputConfirm(item.field,e)}\" v-model=\"inFormFields[item.field]\" border=\"none\"\n\t\t\t\t\t:ref=\"item.field\" :placeholder=\"item.placeholder||('请输入'+item.title)\"></input>\n\t\t\t</view>\n\t\t\t<view v-if=\"item.extra\" :style=\"item.extra.style\" style=\"display: flex;\"\n\t\t\t\t@click=\"extraClick(item,inFormFields)\">\n\t\t\t\t<u-icon v-if=\"item.extra.icon\" :name=\"item.extra.icon\" :color=\"item.extra.color\"\n\t\t\t\t\t:size=\"item.extra.size\">\n\t\t\t\t</u-icon>\n\t\t\t\t<text>{{item.extra.text}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<slot></slot>\n\t\t<!--日期 -->\n\t\t<u-datetime-picker class=\"form-popup\" :minDate=\"pickerCurrentItem.min\" :maxDate=\"pickerCurrentItem.max\"\n\t\t\t:zIndex=\"9999999\" :closeOnClickOverlay=\"true\" :show=\"pickerModel\" :value=\"pickerValue\"\n\t\t\t:mode=\"pickerCurrentItem.type=='month'?'year-month':pickerCurrentItem.type\" closeOnClickOverlay\n\t\t\t@confirm=\"pickerConfirm\" @cancel=\"pickerClose\" @close=\"pickerClose\"></u-datetime-picker>\n\t\t<!--  下拉框 -->\n\t\t<u-popup @touchmove.prevent class=\"form-popup\" :zIndex=\"999999\" :show=\"actionSheetModel\"\n\t\t\t@close=\"actionSheetModel=false;\">\n\t\t\t<view class=\"vol-action-sheet-select-container\" :style=\"{'height':(popupHeight+'px')}\">\n\t\t\t\t<view class=\"vol-action-sheet-select-title\">请选择{{actionSheetCurrentItem.title}}\n\t\t\t\t\t<text class=\"vol-action-sheet-select-confirm\" @click=\"actionConfirmClick\">确定</text>\n\t\t\t\t</view>\n\t\t\t\t<!-- \t超过10个下拉框选项默认开启搜索 -->\n\t\t\t\t<!-- \t -->\n\t\t\t\t<view v-if=\"showFilter\" class=\"vol-action-sheet-select-filter\">\n\t\t\t\t\t<u-search @custom=\"searchText=''\" placeholder=\"请输入关键字搜索\" :showAction=\"true\" actionText=\"清除\"\n\t\t\t\t\t\t:animation=\"false\" v-model=\"searchText\">\n\t\t\t\t\t</u-search>\n\t\t\t\t\t<!-- @search=\"search\" @custom=\"searchClick\" -->\n\t\t\t\t\t<!-- \t<view style=\"padding-left:20rpx;flex:1;font-size: 22px;color: #909399;background: white;\">\n\t\t\t\t\t\t<u--input placeholder=\"请输入关键字搜索\" v-model=\"searchText\">\n\t\t\t\t\t\t</u--input>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"search-btn\">\n\t\t\t\t\t\t<u-button :plain=\"true\" :hairline=\"true\" :customStyle=\"{padding:'10rpx 20rpx'}\" shape=\"circle\"\n\t\t\t\t\t\t\ttype=\"primary\" icon=\"trash\" @click=\"searchText=''\" size=\"small\">清除</u-button>\n\t\t\t\t\t</view> -->\n\t\t\t\t</view>\n\t\t\t\t<view class=\"vol-action-sheet-select-content\">\n\t\t\t\t\t<view :class=\"{'vol-action-sheet-select-actived':actionSheetModel&&isActionSelected(item)}\"\n\t\t\t\t\t\t@click=\"actionClick(item)\"\n\t\t\t\t\t\tv-show=\"!item.hidden&&(!searchText||item.value.indexOf(searchText)!=-1)\" :key=\"index\"\n\t\t\t\t\t\tv-for=\"(item,index) in actionSheetCurrentItem.data\" class=\"vol-action-sheet-select-item\">\n\t\t\t\t\t\t{{item.value}}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</u-popup>\n\n\t\t<!--  树形级联组件 -->\n\t\t<vol-tree ref=\"cascader\" :data=\"actionSascaderCurrentItem.data\" :title=\"'请选择'+actionSascaderCurrentItem.title\"\n\t\t\t:checkStrictly=\"actionSascaderCurrentItem.checkStrictly\" @cancel=\"actionSascaderCurrentItem.cancel\"\n\t\t\t@confirm=\"cascaderConfirm\">\n\t\t</vol-tree>\n\n\t\t<!-- \t\t数字键盘 -->\n\t\t<!-- \t<u-keyboard ref=\"uKeyboard\" @change=\"numberChange\" @backspace=\"numberBackspace\"\n\t\t\t:dotDisabled=\"numberCurrentItem.type=='decimal'\" :z-index='999999999' mode=\"number\" :show=\"numberModel\">\n\t\t</u-keyboard> -->\n\t\t<lotus-address v-on:choseVal=\"onCitySelect\" :lotusAddressData=\"lotusAddressData\"></lotus-address>\n\t</view>\n</template>\n\n<script>\n\timport lotusAddress from \"./../Winglau14-lotusAddress/Winglau14-lotusAddress.vue\";\n\texport default {\n\t\tcomponents: {\n\t\t\tlotusAddress\n\t\t},\n\t\tprops: {\n\t\t\tformOptions: {\n\t\t\t\ttype: Array,\n\t\t\t\tdefault: () => {\n\t\t\t\t\treturn []\n\t\t\t\t}\n\t\t\t},\n\t\t\tformFields: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault: () => {\n\t\t\t\t\treturn {}\n\t\t\t\t}\n\t\t\t},\n\t\t\tpadding: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 30\n\t\t\t},\n\t\t\tlabelWidth: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 150\n\t\t\t},\n\t\t\tlabelPosition: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'left'\n\t\t\t},\n\t\t\tloadKey: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\tuploadBefore: {\n\t\t\t\ttype: Function,\n\t\t\t\tdefault: (files) => {\n\t\t\t\t\treturn true\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tname: \"vol-form\",\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tlotusAddressData: {\n\t\t\t\t\tvisible: false,\n\t\t\t\t\tprovinceName: '',\n\t\t\t\t\tcityName: '',\n\t\t\t\t\ttownName: '',\n\t\t\t\t},\n\t\t\t\tcityItem: {\n\t\t\t\t\tfield: \"\"\n\t\t\t\t},\n\t\t\t\tregion: '',\n\t\t\t\tshowFilter: false,\n\t\t\t\tsearchText: '', //搜索的内容\n\t\t\t\tinFormFields: {},\n\t\t\t\tinFormOptions: [],\n\t\t\t\tmaxHeight: 400,\n\t\t\t\tpopupHeight: 0,\n\t\t\t\tpickerValue: '',\n\t\t\t\tpickerModel: false, //日期组件\n\t\t\t\tpickerCurrentItem: {}, //当前选项\n\t\t\t\tpickerCurrentRangeIndex: 0,\n\t\t\t\tactionSheetModel: false,\n\t\t\t\tactionSascaderCurrentItem: {\n\t\t\t\t\ttitle: \"\",\n\t\t\t\t\tfield: '',\n\t\t\t\t\tcheckStrictly: false, //是否只能选择最后一个节点\n\t\t\t\t\tcancel: () => {},\n\t\t\t\t\tconfirm: () => {},\n\t\t\t\t\tdata: []\n\t\t\t\t},\n\t\t\t\tactionSheetCurrentItem: {\n\t\t\t\t\tmin: 633715200000,\n\t\t\t\t\tmax: 0\n\t\t\t\t}, //当前选项\n\t\t\t\tactionSheetSelectValues: [], //当前选中的项\n\t\t\t\tnumberModel: false,\n\t\t\t\tnumberType: 'number',\n\t\t\t\tnumberCurrentItem: {},\n\t\t\t\timgFields: []\n\t\t\t};\n\t\t},\n\t\tcreated() {\n\t\t\t//日期最小最大值转换\n\t\t\tthis.formOptions.forEach(option => {\n\t\t\t\tif ((option.type == 'date' || option.type == 'datetime' || option.type == 'month')) {\n\t\t\t\t\tif (!option.min) {\n\t\t\t\t\t\toption.min = Number(new Date('1990/01/01 00:00:00')) //\n\t\t\t\t\t} else if (typeof option.min == 'string') {\n\t\t\t\t\t\tif (option.type == 'month' && option.min.length != 7) {\n\t\t\t\t\t\t\toption.min = option.min.substring(0, 7);\n\t\t\t\t\t\t}\n\t\t\t\t\t\toption.min = Number(new Date(option.min.replace(/-/g, \"/\")))\n\t\t\t\t\t}\n\t\t\t\t\tif (!option.max) {\n\t\t\t\t\t\toption.max = Number(new Date(new Date().getFullYear() + 10 + '/01/01 00:00:00')) //\n\t\t\t\t\t} else if (option.max && typeof option.max == 'string') {\n\t\t\t\t\t\toption.max = Number(new Date(option.max.replace(/-/g, \"/\")))\n\t\t\t\t\t}\n\t\t\t\t\tif (!this.pickerCurrentItem.max) {\n\t\t\t\t\t\tthis.pickerCurrentItem.max = option.max;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (option.hasOwnProperty('focus')) {\n\t\t\t\t\toption.focus = false;\n\t\t\t\t}\n\t\t\t})\n\t\t\tthis.inFormOptions = this.formOptions;\n\t\t\tthis.inFormFields = this.formFields;\n\t\t\tthis.imgFields = this.inFormOptions.filter(x => {\n\t\t\t\treturn x.type == 'img'\n\t\t\t}).map(x => {\n\t\t\t\treturn x.field\n\t\t\t});\n\t\t\tif (this.imgFields.length) {\n\t\t\t\tthis.convertImgArr(this.formFields)\n\t\t\t} else {\n\t\t\t\tthis.imgFields = null;\n\t\t\t}\n\t\t\tif (!this.loadKey) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tlet dicKeys = this.formOptions.filter(x => {\n\t\t\t\treturn x.key || x.dataKey\n\t\t\t}).map(m => {\n\t\t\t\treturn m.key || m.dataKey\n\t\t\t});\n\t\t\tif (!dicKeys.length) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.http.post('api/Sys_Dictionary/GetVueDictionary', dicKeys, true).then(result => {\n\t\t\t\tthis.initDataSource(result)\n\t\t\t})\n\t\t},\n\t\tmounted() {\n\t\t\tvar _this = this;\n\t\t\tuni.getSystemInfo({\n\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t_this.maxHeight = res.screenHeight * 0.85;\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tmethods: {\n\t\t\tinputConfirm(field, e) {\n\t\t\t\tthis.$emit('input-confirm', field, e);\n\t\t\t},\n\t\t\tconvertImgArr(formFields) {\n\t\t\t\tif (!this.imgFields) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tfor (let i = 0; i < this.imgFields.length; i++) {\n\t\t\t\t\tlet field = this.imgFields[i];\n\t\t\t\t\tif (!Array.isArray(formFields[field])) {\n\t\t\t\t\t\tif (this.base.isEmpty(formFields[field])) {\n\t\t\t\t\t\t\tformFields[field] = [];\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tformFields[field] = formFields[field].split(',').map(x => {\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\turl: this.http.ipAddress + x,\n\t\t\t\t\t\t\t\t\torginUrl: x //原图\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tinitDataSource(result) {\n\t\t\t\tresult.forEach(res => {\n\t\t\t\t\tthis.inFormOptions.forEach(option => {\n\t\t\t\t\t\tif ((option.key || option.dataKey) == res.dicNo) {\n\t\t\t\t\t\t\toption.data = res.data;\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t\tthis.$emit('dicInited', result);\n\t\t\t},\n\t\t\tcascaderConfirm(value, item) {\n\t\t\t\tthis.inFormFields[this.actionSascaderCurrentItem.field] = value;\n\t\t\t\tthis.$emit(\"onChange\", this.actionSascaderCurrentItem.field, value, item);\n\t\t\t},\n\t\t\tshowActionSheet(item) {\n\t\t\t\tif (item.type == 'cascader') {\n\t\t\t\t\tthis.actionSascaderCurrentItem.field = item.field;\n\t\t\t\t\tthis.actionSascaderCurrentItem.data.splice(0);\n\t\t\t\t\tthis.actionSascaderCurrentItem.checkStrictly = item.checkStrictly || false; //是否只能选择最后一个节点\n\t\t\t\t\tthis.actionSascaderCurrentItem.data.push(...item.data);\n\t\t\t\t\tthis.$refs.cascader.show(this.inFormFields[item.field]);\n\t\t\t\t\t//this.actionSascaderCurrentItem.cancel = item.cancel;\n\t\t\t\t\t//this.actionSascaderCurrentItem.confirm = item.confirm;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.searchText = '';\n\t\t\t\tthis.actionSheetSelectValues = [];\n\t\t\t\tthis.actionSheetCurrentItem = item;\n\t\t\t\tvar value = this.inFormFields[item.field];\n\t\t\t\tif (!this.base.isEmpty(value, true)) {\n\t\t\t\t\tif (Array.isArray(value)) {\n\t\t\t\t\t\tthis.actionSheetSelectValues.push(...value.map(x => {\n\t\t\t\t\t\t\treturn x;\n\t\t\t\t\t\t}));\n\t\t\t\t\t} else if (this.isMultiSelect()) {\n\t\t\t\t\t\tthis.actionSheetSelectValues = value.split(',');\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.actionSheetSelectValues.push(value);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.showFilter = item.data.length > 15;\n\t\t\t\tlet height = (item.data.length + 1 + (this.showFilter ? 1 : 0)) * 50;\n\t\t\t\tthis.popupHeight = height > this.maxHeight ? this.maxHeight : height;\n\t\t\t\tthis.actionSheetModel = true;\n\t\t\t},\n\t\t\tactionClick(item) {\n\t\t\t\t//多选\n\t\t\t\tif (this.isMultiSelect()) {\n\t\t\t\t\t//已经选中过的再次点取消选选中\n\t\t\t\t\tif (this.isActionSelected(item)) {\n\t\t\t\t\t\tthis.actionSheetSelectValues = this.actionSheetSelectValues.filter(x => {\n\t\t\t\t\t\t\treturn x + '' !== item.key + ''\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.actionSheetSelectValues.push(item.key)\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.inFormFields[this.actionSheetCurrentItem.field] = item.key;\n\t\t\t\tthis.actionSheetModel = false;\n\t\t\t\tthis.$emit(\"onChange\", this.actionSheetCurrentItem.field, this.inFormFields[this.actionSheetCurrentItem\n\t\t\t\t\t.field], item);\n\t\t\t},\n\t\t\tisMultiSelect(item) {\n\t\t\t\tvar type;\n\t\t\t\tif (item) {\n\t\t\t\t\ttype = item.type;\n\t\t\t\t} else {\n\t\t\t\t\ttype = this.actionSheetCurrentItem.type\n\t\t\t\t}\n\t\t\t\tif (!type) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\treturn ['checkbox', 'selectList'].indexOf(type) != -1;\n\t\t\t},\n\t\t\tactionConfirmClick(item) {\n\t\t\t\t//单选\n\t\t\t\tif (!this.isMultiSelect()) {\n\t\t\t\t\tthis.actionSheetModel = false;\n\t\t\t\t\t//\treturn this.actionClick(item)\n\t\t\t\t}\n\t\t\t\t//多选\n\t\t\t\tif (Array.isArray(this.inFormFields[this.actionSheetCurrentItem.field])) {\n\t\t\t\t\t//深复制原来的数据\n\t\t\t\t\tthis.inFormFields[this.actionSheetCurrentItem.field] = this.actionSheetSelectValues.map(x => {\n\t\t\t\t\t\treturn x\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tthis.inFormFields[this.actionSheetCurrentItem.field] = this.actionSheetSelectValues.join(',');\n\t\t\t\t}\n\t\t\t\tthis.actionSheetModel = false;\n\t\t\t},\n\t\t\tisActionSelected(item) {\n\t\t\t\tlet isSelect = this.actionSheetSelectValues.some(x => {\n\t\t\t\t\treturn x + '' === item.key + ''\n\t\t\t\t});\n\t\t\t\t//this.formFields[item.field]\n\t\t\t\treturn isSelect;\n\t\t\t},\n\t\t\tformatDicValueList(item) { //多选\n\t\t\t\tvar value = this.inFormFields[item.field];\n\t\t\t\tif (this.base.isEmpty(value)) {\n\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t\tvar _textArr = [];\n\n\t\t\t\tif (!(Array.isArray(value))) {\n\t\t\t\t\tvalue = (value + '').split(',')\n\t\t\t\t}\n\t\t\t\tvalue.forEach(x => {\n\t\t\t\t\tvar obj = item.data.find(c => {\n\t\t\t\t\t\treturn x + '' === c.key + '';\n\t\t\t\t\t});\n\t\t\t\t\tif (obj) {\n\t\t\t\t\t\t_textArr.push(obj.value);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t_textArr.push(x);\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\treturn _textArr.join(\",\");\n\t\t\t},\n\t\t\tgetAllParentId(id, data) {\n\t\t\t\tif (id === null || id === '' || id === undefined) {\n\t\t\t\t\treturn []\n\t\t\t\t}\n\t\t\t\tif (data.some((x) => {\n\t\t\t\t\t\treturn typeof(x.id) == 'string'\n\t\t\t\t\t})) {\n\t\t\t\t\tid = id + '';\n\t\t\t\t} else {\n\t\t\t\t\tid = id * 1;\n\t\t\t\t}\n\t\t\t\tlet ids = [id];\n\n\t\t\t\tfor (let index = 0; index < ids.length; index++) {\n\t\t\t\t\tvar node = data.find((x) => {\n\t\t\t\t\t\treturn x.id === ids[index]\n\t\t\t\t\t});\n\t\t\t\t\tif (!node || (node.parentId === null && node.parentId === undefined)) {\n\t\t\t\t\t\treturn ids;\n\t\t\t\t\t}\n\t\t\t\t\tif (data.some(x => {\n\t\t\t\t\t\t\treturn x.id === node.parentId\n\t\t\t\t\t\t})) {\n\t\t\t\t\t\tids.push(node.parentId);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn ids.reverse();\n\t\t\t},\n\t\t\tgetCascaderNames(value, item) {\n\t\t\t\tlet ids = this.getAllParentId(value, item.data);\n\t\t\t\tlet names = [];\n\t\t\t\tfor (let i = 0; i < ids.length; i++) {\n\t\t\t\t\tlet obj = item.data.find(x => {\n\t\t\t\t\t\treturn x.id === ids[i]\n\t\t\t\t\t});\n\t\t\t\t\tif (obj) {\n\t\t\t\t\t\tnames.push(obj.value || obj.name)\n\t\t\t\t\t} else {\n\t\t\t\t\t\tnames.push(ids[i])\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn names.join('/');\n\t\t\t},\n\t\t\tformatDicValue(item) {\n\t\t\t\tlet value = this.inFormFields[item.field];\n\t\t\t\tif (this.base.isEmpty(value)) {\n\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t\tif (item.type == 'cascader') {\n\t\t\t\t\treturn this.getCascaderNames(value, item);\n\t\t\t\t}\n\t\t\t\tif (this.isMultiSelect(item)) {\n\t\t\t\t\treturn this.formatDicValueList(item);\n\t\t\t\t}\n\t\t\t\tlet _kv = item.data.find(x => {\n\t\t\t\t\treturn x.key + '' == value + ''\n\t\t\t\t});\n\t\t\t\tif (!_kv) {\n\t\t\t\t\treturn value;\n\t\t\t\t}\n\t\t\t\treturn _kv.value;\n\t\t\t},\n\t\t\tshowPicker(item, index) {\n\t\t\t\tthis.pickerCurrentItem = item;\n\t\t\t\tlet val = this.inFormFields[this.pickerCurrentItem.field];\n\t\t\t\tif (item.range) {\n\t\t\t\t\tthis.pickerCurrentRangeIndex = index;\n\t\t\t\t\tif (!Array.isArray(val)) {\n\t\t\t\t\t\tthis.inFormFields[this.pickerCurrentItem.field] = ['', ''];\n\t\t\t\t\t\tval = ['', ''];\n\t\t\t\t\t}\n\t\t\t\t\tval = val[index];\n\t\t\t\t}\n\t\t\t\tif (!val) {\n\t\t\t\t\tif (item.type == 'date') {\n\t\t\t\t\t\tval = this.base.getDate();\n\t\t\t\t\t} else if (item.type == 'month') {\n\t\t\t\t\t\tval = this.base.getDate();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tval = this.base.getDateTime().substring(0, 16)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.pickerValue = Number(new Date(val.replace(/-/g, \"/\")))\n\t\t\t\tthis.pickerModel = true;\n\t\t\t\tthis.hideKeyboard();\n\t\t\t},\n\t\t\tsetPickerValue(value) {\n\t\t\t\tif (this.pickerCurrentItem.range) {\n\t\t\t\t\tthis.inFormFields[this.pickerCurrentItem.field][this.pickerCurrentRangeIndex] = value\n\t\t\t\t} else {\n\t\t\t\t\tthis.inFormFields[this.pickerCurrentItem.field] = value\n\t\t\t\t}\n\t\t\t\tthis.$emit(\"onChange\", this.pickerCurrentItem.field, value);\n\t\t\t},\n\t\t\tpickerConfirm(e) {\n\t\t\t\tthis.pickerModel = false;\n\t\t\t\tif (this.pickerCurrentItem.range && this.pickerCurrentRangeIndex == 1) {\n\t\t\t\t\t//判断结束时间大于开始时间\n\t\t\t\t}\n\t\t\t\tif (typeof e.value == 'number') {\n\t\t\t\t\tlet timeFormat = this.pickerCurrentItem.type == 'date' ? 'yyyy-mm-dd' : 'yyyy-mm-dd hh:MM';\n\t\t\t\t\tthis.setPickerValue(uni.$u.timeFormat(e.value, timeFormat))\n\t\t\t\t} else {\n\t\t\t\t\tthis.setPickerValue(uni.$u.timeFormat(e.value))\n\t\t\t\t}\n\t\t\t},\n\t\t\tpickerClose() {\n\t\t\t\tthis.pickerModel = false;\n\t\t\t},\n\t\t\thideKeyboard() {\n\t\t\t\tuni.hideKeyboard()\n\t\t\t},\n\t\t\treset(source) {\n\t\t\t\tfor (const key in this.inFormFields) {\n\t\t\t\t\tif (source && source.hasOwnProperty(key)) {\n\t\t\t\t\t\tthis.inFormFields[key] = source[key];\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (Array.isArray(this.inFormFields[key])) {\n\t\t\t\t\t\t\tthis.inFormFields[key].splice(0);\n\t\t\t\t\t\t\tif (this.inFormOptions.some(x => {\n\t\t\t\t\t\t\t\t\treturn x.field == key && x.range\n\t\t\t\t\t\t\t\t})) {\n\t\t\t\t\t\t\t\tthis.inFormFields[key].push(...['', '']);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.inFormFields[key] = \"\"\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tvalidate() {\n\t\t\t\tlet _option = this.inFormOptions.filter(c => {\n\t\t\t\t\treturn c.require || c.required\n\t\t\t\t}).find(x => {\n\t\t\t\t\tlet val = this.inFormFields[x.field];\n\t\t\t\t\tif (Array.isArray(val)) {\n\t\t\t\t\t\treturn !val.length\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn (this.base.isEmpty(val))\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t});\n\t\t\t\tif (_option) {\n\t\t\t\t\tif (['date', 'datetime', 'month', 'checkbox', 'select', 'selectList', 'radio', 'switch'].indexOf(\n\t\t\t\t\t\t\t_option\n\t\t\t\t\t\t\t.type) != -\n\t\t\t\t\t\t1) {\n\t\t\t\t\t\tthis.$toast('请选择' + _option.title, )\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.$toast(_option.title + '不能为空')\n\t\t\t\t\t}\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t},\n\t\t\tshowNumber(item) {\n\t\t\t\tthis.numberCurrentItem = item;\n\t\t\t\tthis.numberModel = true;\n\t\t\t},\n\t\t\tnumberBackspace() {\n\t\t\t\tlet value = this.inFormFields[this.numberCurrentItem.field];\n\t\t\t\tif (value) {\n\t\t\t\t\tvalue = value + '';\n\t\t\t\t\tthis.inFormFields[this.numberCurrentItem.field] = value.substr(0, value - 1);\n\t\t\t\t}\n\t\t\t},\n\t\t\tnumberChange(val) {\n\t\t\t\tlet _val = this.inFormFields[this.numberCurrentItem.field];\n\t\t\t\tif (this.base.isEmpty(_val)) {\n\t\t\t\t\t_val = '';\n\t\t\t\t} else {\n\t\t\t\t\t_val = _val + '';\n\t\t\t\t}\n\t\t\t\tif (val == '.' && _val.indexOf('.') != -1) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.inFormFields[this.numberCurrentItem.field] = _val + val;\n\t\t\t},\n\t\t\tformatReadonlyValue(item) {\n\t\t\t\tif (item.data) {\n\t\t\t\t\treturn this.formatDicValue(item);\n\t\t\t\t}\n\t\t\t\tif (item.type == 'date') {\n\t\t\t\t\treturn (this.inFormFields[item.field] || '').substr(0, 10);\n\t\t\t\t}\n\t\t\t\treturn this.inFormFields[item.field] || '';\n\t\t\t},\n\t\t\tgetImgSrcs(item) {\n\t\t\t\tlet imgs = this.inFormFields[item.field];\n\t\t\t\tif (!imgs) {\n\t\t\t\t\treturn []\n\t\t\t\t}\n\t\t\t\tif (Array.isArray(imgs)) {\n\t\t\t\t\treturn imgs;\n\t\t\t\t}\n\t\t\t\tlet imgArr = imgs.split(',');\n\t\t\t\treturn imgArr.filter(x => {\n\t\t\t\t\treturn x\n\t\t\t\t}).map(m => {\n\t\t\t\t\t//return this.http.ipAddress+'m'\n\t\t\t\t\treturn m;\n\t\t\t\t})\n\t\t\t\t//this.http.ipAddress\n\t\t\t},\n\t\t\tasync afterRead(option, event) {\n\t\t\t\tif (!option.url) {\n\t\t\t\t\treturn this.$toast('未配置好url')\n\t\t\t\t}\n\t\t\t\t// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式\n\t\t\t\tlet lists = [];\n\t\t\t\tif (option.multiple) {\n\t\t\t\t\tlists = [].concat(event.file)\n\t\t\t\t} else {\n\t\t\t\t\tif (Array.isArray(event.file)) {\n\t\t\t\t\t\tlists.push(...event.file)\n\t\t\t\t\t} else {\n\t\t\t\t\t\tlists.push(event.file)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tlet fileListLen = this.inFormFields[option.field].length\n\t\t\t\tlists.map((item) => {\n\t\t\t\t\tthis.inFormFields[option.field].push({\n\t\t\t\t\t\t...item,\n\t\t\t\t\t\tstatus: 'uploading',\n\t\t\t\t\t\tmessage: '上传中'\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t\t// if (!this.uploadBefore(lists, option)) {\n\t\t\t\t// \treturn;\n\t\t\t\t// }\n\t\t\t\t//this.$emit('uploadBefore', lists, option, async () => {\n\t\t\t\tfor (let i = 0; i < lists.length; i++) {\n\t\t\t\t\tconst result = await this.uploadFilePromise(lists[i].url, option.url)\n\t\t\t\t\tlet item = this.inFormFields[option.field][fileListLen];\n\t\t\t\t\tlet fileName = lists[i].name;\n\t\t\t\t\tif (!fileName && lists[i].thumb) {\n\t\t\t\t\t\tlet lastIndex = lists[i].thumb.lastIndexOf('/') + 1;\n\t\t\t\t\t\t// let arr = lists[i].thumb.substr(0,lastIndex);\n\t\t\t\t\t\t// let _obj = arr[0].split('/');\n\t\t\t\t\t\tfileName = lists[i].thumb.substr(lastIndex)\n\t\t\t\t\t}\n\t\t\t\t\tthis.inFormFields[option.field].splice(fileListLen, 1, Object.assign(item, {\n\t\t\t\t\t\tstatus: 'success',\n\t\t\t\t\t\tmessage: '',\n\t\t\t\t\t\turl: this.http.ipAddress + result + fileName,\n\t\t\t\t\t\torginUrl: result + fileName\n\t\t\t\t\t}))\n\t\t\t\t\tfileListLen++\n\t\t\t\t}\n\t\t\t\t//})\n\t\t\t},\n\t\t\tuploadFilePromise(url, apiUrl) {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\tlet a = uni.uploadFile({\n\t\t\t\t\t\turl: this.http.ipAddress + apiUrl, // 仅为示例，非真实的接口地址\n\t\t\t\t\t\tfilePath: url,\n\t\t\t\t\t\tname: 'fileInput',\n\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t\"uapp\": 1,\n\t\t\t\t\t\t\t\"Authorization\": this.$store.getters.getToken()\n\t\t\t\t\t\t},\n\t\t\t\t\t\tformData: {},\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tresolve(JSON.parse(res.data).data)\n\t\t\t\t\t\t\t}, 500)\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail(res) {\n\t\t\t\t\t\t\tthis.$toast('上传失败')\n\t\t\t\t\t\t\t//console.log(res)\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 删除图片\n\t\t\tdeletePic(item, event) {\n\t\t\t\tthis.inFormFields[item.field].splice(event.index, 1)\n\t\t\t},\n\t\t\textraClick(item, inFormFields) {\n\t\t\t\tthis.$emit('extraClick', item, inFormFields)\n\t\t\t},\n\t\t\tshowCascaderSheet(item) {\n\t\t\t\tthis.$refs[item.field][0].show();\n\t\t\t},\n\t\t\tonCitySelect(res) {\n\t\t\t\t//res数据源包括已选省市区与省市区code\n\t\t\t\tconsole.log(res);\n\t\t\t\tthis.lotusAddressData.visible = res.visible; //visible为显示与关闭组件标识true显示false隐藏\n\t\t\t\t//res.isChose = 1省市区已选 res.isChose = 0;未选\n\t\t\t\tif (res.isChose) {\n\t\t\t\t\tthis.lotusAddressData.provinceName = res.province; //省\n\t\t\t\t\tthis.lotusAddressData.cityName = res.city; //市\n\t\t\t\t\tthis.lotusAddressData.townName = res.town; //区\n\t\t\t\t\tthis.inFormFields[this.cityItem.field] = res.province + ',' + res.city + ',' + res.town\n\t\t\t\t\t//this.region = `${res.province}${res.city}${res.town}`; //region为已选的省市区的值\n\t\t\t\t}\n\t\t\t},\n\t\t\tshowCitySheet(item) {\n\t\t\t\tthis.cityItem = item;\n\t\t\t\tconst arr = (this.inFormFields[item.field] || '').split(',');\n\t\t\t\tthis.lotusAddressData.provinceName = arr[0] || ''; //省\n\t\t\t\tthis.lotusAddressData.cityName = arr[1] || ''; //市\n\t\t\t\tthis.lotusAddressData.townName = arr[2] || ''; //区\n\t\t\t\tthis.lotusAddressData.visible = true;\n\t\t\t},\n\t\t\tpreviewImage(item, imgIndex) {\n\t\t\t\tconst imgs = this.getImgSrcs(item).map(x => {\n\t\t\t\t\treturn x.url\n\t\t\t\t})\n\t\t\t\tuni.previewImage({\n\t\t\t\t\turls: imgs,\n\t\t\t\t\tcurrent: imgIndex\n\t\t\t\t})\n\t\t\t},\n\t\t\tradioOnChange(value, item) {\n\t\t\t\tthis.$emit(\"onChange\", item.field, value, item, item.data);\n\t\t\t\t//@change=\"(val)=>{radioOnChange(val,item)}\" :placement=\"item.placement\"\n\t\t\t}\n\t\t},\n\t\t// #ifdef MP-WEIXIN\n\t\t// 小程序不要使用循环生成表单,否则这里会死循环,与初始化的时候设置默认值有关,后面再处理\n\t\twatch: {\n\t\t\tinFormFields: {\n\t\t\t\thandler(val) {\n\t\t\t\t\tif (!val || !Object.keys(val).length) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tconsole.log('inFormFields')\n\t\t\t\t\tthis.$emit('update:form-fields', val);\n\t\t\t\t\t//console.log(\"wc\")\n\t\t\t\t},\n\t\t\t\timmediate: true,\n\t\t\t\tdeep: true\n\t\t\t},\n\t\t\tformFields: {\n\t\t\t\thandler(val) {\n\t\t\t\t\t// console.log('formFields')\n\t\t\t\t\tthis.convertImgArr(val)\n\t\t\t\t\tthis.inFormFields = val;\n\t\t\t\t},\n\t\t\t\timmediate: true,\n\t\t\t\tdeep: true\n\t\t\t},\n\t\t\tinFormOptions: {\n\t\t\t\thandler(newValue, oldValue) {\n\t\t\t\t\tif (!newValue || !newValue.length) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tthis.convertImgArr(newValue)\n\t\t\t\t\tthis.$emit('update:formOptions', newValue)\n\t\t\t\t},\n\t\t\t\timmediate: true,\n\t\t\t\tdeep: true\n\t\t\t},\n\t\t\tformOptions: {\n\t\t\t\thandler(newValue, oldValue) {\n\t\t\t\t\tconsole.log('formOptions')\n\t\t\t\t\tthis.inOptions = newValue;\n\t\t\t\t},\n\t\t\t\timmediate: true,\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\t// #endif\n\t}\n</script>\n\n<style lang=\"less\" scoped>\n\t.vol-action-sheet-select-container {\n\t\t// min-height: 200rpx;\n\t\t// display: flex;\n\t\t// flex-direction: column;\n\n\t\t// .vol-action-sheet-select-title {\n\t\t// \tpadding: 24rpx;\n\t\t// \ttext-align: center;\n\t\t// \tposition: relative;\n\t\t// \tborder-bottom: 1px solid rgb(233 233 233);\n\n\t\t// \t.vol-action-sheet-select-confirm {\n\t\t// \t\tposition: absolute;\n\t\t// \t\tright: 30rpx;\n\t\t// \t\tcolor: #007AFF;\n\t\t// \t\tfont-weight: 500;\n\t\t// \t}\n\t\t// }\n\t\t.vol-action-sheet-select-filter {\n\t\t\tdisplay: flex;\n\t\t\tbackground: #ffff;\n\t\t\tpadding: 10rpx;\n\t\t\tborder-bottom: 1px solid #eeee;\n\n\t\t\t.search-btn {\n\t\t\t\tposition: relative;\n\t\t\t\ttop: 3px;\n\t\t\t\t// margin-left: 20rpx;\n\t\t\t\t// padding-right: 20rpx;\n\t\t\t\t// width: 100rpx;\n\t\t\t}\n\t\t}\n\n\t\t.vol-action-sheet-select-content {\n\n\t\t\t// flex: 1;\n\t\t\t// height: 0;\n\t\t\t// overflow: scroll;\n\t\t\t.vol-action-sheet-select-item {\n\t\t\t\tborder-bottom: 1px solid rgb(247 247 247);\n\t\t\t\tpadding: 26rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tposition: relative;\n\t\t\t\tcolor: #5a5a5a;\n\n\t\t\t\t.vol-action-sheet-select-icon {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\t// display: none;\n\t\t\t\t\twidth: 70rpx;\n\t\t\t\t\tz-index: 999;\n\t\t\t\t\tright: 20rpx;\n\t\t\t\t\tpadding: 0 10rpx;\n\t\t\t\t\tbackground: #FFFFFF;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.vol-action-sheet-select-actived {\n\t\t\t\tcolor: red;\n\t\t\t}\n\n\t\t\t.vol-action-sheet-select-item:last-child {\n\t\t\t\tborder-bottom: none;\n\t\t\t}\n\t\t}\n\t}\n\n\t.f-form-item {\n\n\t\tpadding: 28rpx 0 24rpx 0;\n\t\tborder-bottom: 1px solid #f5f5f5;\n\n\t\t.f-form-label {\n\t\t\tposition: relative;\n\t\t\tcolor: #4c4c4c;\n\t\t\tfont-size: 30rpx;\n\t\t\tmargin-right: 20rpx;\n\t\t}\n\n\t\t.f-form-content-select {\n\t\t\ttext-align: right;\n\t\t\tdisplay: flex;\n\t\t}\n\n\t\t.f-form-label-required {\n\t\t\tcolor: red;\n\t\t\tfont-size: 24rpx;\n\t\t}\n\t}\n\n\t.left-form-item {\n\t\tdisplay: flex;\n\t\tbackground: #FFFFFF;\n\n\t\t.f-form-content {\n\t\t\tflex: 1;\n\t\t\twidth: 0;\n\t\t\ttext-align: right;\n\t\t}\n\n\t\t.f-form-label-required {\n\t\t\tmargin-left: -12rpx;\n\t\t\tposition: relative;\n\t\t}\n\n\t\t.f-form-label {\n\t\t\tposition: relative;\n\t\t\t// padding-left: 10rpx;\n\t\t\tcolor: #4c4c4c;\n\t\t\tfont-size: 30rpx;\n\t\t}\n\t}\n\n\t.top-form-item {\n\t\tbackground: #FFFFFF;\n\n\t\t.f-form-label {\n\t\t\twidth: 100% !important;\n\t\t\tpadding-bottom: 16rpx;\n\t\t}\n\t}\n\n\t.f-form-group {\n\t\tpadding: 8rpx 20rpx !important;\n\t\tbackground: none;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\n\t\t.f-form-group-content {}\n\t}\n\n\t/deep/ .u-icon {\n\t\tdisplay: inline-flex;\n\t}\n\t.f-form-content /deep/ .u-radio-group--row {\n\t\t    justify-content: flex-end !important;\n\t}\n\t.left-form-item /deep/ .u-upload__wrap {\n\t\t    justify-content: flex-end !important;\n\t}\n</style>\n", "import mod from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vol-form.vue?vue&type=style&index=0&id=6726b7c4&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vol-form.vue?vue&type=style&index=0&id=6726b7c4&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751873715201\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}