{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/login/forget-password.vue?d43d", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/login/forget-password.vue?df2b", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/login/forget-password.vue?ca03", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/login/forget-password.vue?2441", "uni-app:///pages/login/forget-password.vue", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/login/forget-password.vue?afde", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/login/forget-password.vue?0aa8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "platform", "console", "data", "countdown", "timer", "formData", "phone", "newPassword", "confirmPassword", "codeData", "verificationCode", "formOptions", "field", "title", "type", "placeholder", "codeOptions", "currentPlatform", "created", "methods", "getVerificationCode", "uni", "icon", "startCountdown", "clearInterval", "resetPassword", "setTimeout", "goBack", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACqC;;;AAGnG;AACuL;AACvL,gBAAgB,2LAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAgtB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoCpuB;AACA;EACA;EAWAC;EAcAC;EACA;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;MACA;MACAC,cACA;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,EACA;MACAC,cACA;QACAJ;QACAC;QACAC;QACAC;MACA,EACA;MACAE;IACA;EACA;EACAC;IACA;IACA;MACAjB;IACA;EACA;EACAkB;IACAC;MACA;MACA;QACA;MACA;;MAEA;MACAnB;MACA;QACAoB;UACAR;UACAS;QACA;QACA;MACA;;MAEA;MACA;;MAEA;MACAD;QACAR;QACAS;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;QACA;UACAC;UACA;QACA;MACA;IACA;IACAC;MACA;MACAxB;MACA;QACAoB;UACAR;UACAS;QACA;QACA;MACA;MACA;QACAD;UACAR;UACAS;QACA;QACA;MACA;MACA;MACAD;QACAR;QACAS;MACA;MACA;MACAI;QACAL;MACA;IACA;IACAM;MACA;MACAN;IACA;EACA;EACAO;IACA;IACA;MACAJ;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChMA;AAAA;AAAA;AAAA;AAAmiC,CAAgB,w+BAAG,EAAC,C;;;;;;;;;;;ACAvjC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/forget-password.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/forget-password.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./forget-password.vue?vue&type=template&id=35706aac&scoped=true&\"\nvar renderjs\nimport script from \"./forget-password.vue?vue&type=script&lang=js&\"\nexport * from \"./forget-password.vue?vue&type=script&lang=js&\"\nimport style0 from \"./forget-password.vue?vue&type=style&index=0&id=35706aac&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"35706aac\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/forget-password.vue\"\nexport default component.exports", "export * from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./forget-password.vue?vue&type=template&id=35706aac&scoped=true&\"", "var components\ntry {\n  components = {\n    volForm: function () {\n      return import(\n        /* webpackChunkName: \"components/vol-form/vol-form\" */ \"@/components/vol-form/vol-form.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./forget-password.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./forget-password.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<view class=\"logo-container\">\n\t\t\t<image src=\"http://*************:8890/i/2025/06/12/logo.png\" class=\"logo\" mode=\"aspectFit\"></image>\n\t\t</view>\n\t\t\n\t\t<view class=\"form-container\">\n\t\t\t<view class=\"input-group\">\n\t\t\t\t<vol-form :formFields=\"formData\" :formOptions=\"formOptions\" :labelWidth=\"0\" padding=\"0\">\n\t\t\t\t</vol-form>\n\t\t\t</view>\n\n\t\t\t<view class=\"verification-group\">\n\t\t\t\t<view class=\"verification-input\">\n\t\t\t\t\t<vol-form :formFields=\"codeData\" :formOptions=\"codeOptions\" :labelWidth=\"0\" padding=\"0\">\n\t\t\t\t\t</vol-form>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"get-code-btn\" @click=\"getVerificationCode\" :class=\"{'disabled': countdown > 0}\">\n\t\t\t\t\t<text class=\"get-code-text\">{{countdown > 0 ? countdown + 's' : '获取验证码'}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"reset-btn-container\">\n\t\t\t\t<view class=\"reset-btn\" @click=\"resetPassword\">\n\t\t\t\t\t<text class=\"reset-text\">修改密码</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"back-container\">\n\t\t\t<text class=\"back-text\" @click=\"goBack\">返回登录</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\t// 添加平台检测\n\tconst getPlatform = () => {\n\t\tlet platform = '';\n\t\t// #ifdef APP-PLUS\n\t\tplatform = 'APP-PLUS';\n\t\t// #endif\n\t\t// #ifdef APP-PLUS-NVUE\n\t\tplatform = 'APP-NVUE';\n\t\t// #endif\n\t\t// #ifdef H5\n\t\tplatform = 'H5';\n\t\t// #endif\n\t\t// #ifdef MP-WEIXIN\n\t\tplatform = 'MP-WEIXIN';\n\t\t// #endif\n\t\t// #ifdef MP-ALIPAY\n\t\tplatform = 'MP-ALIPAY';\n\t\t// #endif\n\t\t// #ifdef MP-BAIDU\n\t\tplatform = 'MP-BAIDU';\n\t\t// #endif\n\t\t// #ifdef MP-TOUTIAO\n\t\tplatform = 'MP-TOUTIAO';\n\t\t// #endif\n\t\t// #ifdef MP-QQ\n\t\tplatform = 'MP-QQ';\n\t\t// #endif\n\t\tconsole.log('页面运行平台:', platform);\n\t\treturn platform;\n\t};\n\t\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcountdown: 0,\n\t\t\t\ttimer: null,\n\t\t\t\tformData: {\n\t\t\t\t\tphone: '',\n\t\t\t\t\tnewPassword: '',\n\t\t\t\t\tconfirmPassword: ''\n\t\t\t\t},\n\t\t\t\tcodeData: {\n\t\t\t\t\tverificationCode: ''\n\t\t\t\t},\n\t\t\t\tformOptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tfield: 'phone',\n\t\t\t\t\t\ttitle: '手机号',\n\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\tplaceholder: '请输入手机号'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tfield: 'newPassword',\n\t\t\t\t\t\ttitle: '新密码',\n\t\t\t\t\t\ttype: 'password',\n\t\t\t\t\t\tplaceholder: '请输入新密码'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tfield: 'confirmPassword',\n\t\t\t\t\t\ttitle: '确认密码',\n\t\t\t\t\t\ttype: 'password',\n\t\t\t\t\t\tplaceholder: '请确认新密码'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tcodeOptions: [\n\t\t\t\t\t{\n\t\t\t\t\t\tfield: 'verificationCode',\n\t\t\t\t\t\ttitle: '验证码',\n\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\tplaceholder: '请输入验证码'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tcurrentPlatform: getPlatform()\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\t// 添加平台兼容性处理\n\t\t\tif (this.currentPlatform !== 'MP-WEIXIN' && typeof wx === 'undefined') {\n\t\t\t\tconsole.log('当前平台不支持wx对象，将使用uni API替代');\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tgetVerificationCode() {\n\t\t\t\t// 如果正在倒计时，不允许重复点击\n\t\t\t\tif (this.countdown > 0) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 获取验证码逻辑\n\t\t\t\tconsole.log('获取验证码', this.formData.phone);\n\t\t\t\tif (!this.formData.phone) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入手机号',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 开始倒计时\n\t\t\t\tthis.startCountdown();\n\n\t\t\t\t// 这里添加发送验证码的API调用\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '验证码已发送',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t},\n\t\t\tstartCountdown() {\n\t\t\t\tthis.countdown = 60;\n\t\t\t\tthis.timer = setInterval(() => {\n\t\t\t\t\tthis.countdown--;\n\t\t\t\t\tif (this.countdown <= 0) {\n\t\t\t\t\t\tclearInterval(this.timer);\n\t\t\t\t\t\tthis.timer = null;\n\t\t\t\t\t}\n\t\t\t\t}, 1000);\n\t\t\t},\n\t\t\tresetPassword() {\n\t\t\t\t// 重置密码逻辑\n\t\t\t\tconsole.log('重置密码', this.formData.phone, this.formData.newPassword, this.formData.confirmPassword, this.codeData.verificationCode);\n\t\t\t\tif (!this.formData.phone || !this.formData.newPassword || !this.formData.confirmPassword || !this.codeData.verificationCode) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请填写完整信息',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (this.formData.newPassword !== this.formData.confirmPassword) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '两次密码输入不一致',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t// 这里添加重置密码的API调用\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '密码修改成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t\t// 延迟跳转到登录页面\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t}, 1500);\n\t\t\t},\n\t\t\tgoBack() {\n\t\t\t\t// 返回登录页面\n\t\t\t\tuni.navigateBack();\n\t\t\t}\n\t\t},\n\t\tbeforeDestroy() {\n\t\t\t// 清理定时器\n\t\t\tif (this.timer) {\n\t\t\t\tclearInterval(this.timer);\n\t\t\t\tthis.timer = null;\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n.container {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: space-between;\n\theight: 100vh;\n\tbackground-image: url('http://*************:8890/i/2025/06/12/layout_ornament.png');\n\tbackground-size: cover;\n\tbackground-repeat: no-repeat;\n\tbackground-position: center;\n\tpadding: 0 20px;\n\tbox-sizing: border-box;\n\toverflow: hidden;\n}\n\n.logo-container {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tmargin-top: 50px;\n\tmargin-bottom: 40px;\n}\n\n.logo {\n\twidth: 100px;\n\theight: 100px;\n}\n\n.form-container {\n\tflex: 1;\n\twidth: 100%;\n\tmax-width: 350px;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n}\n\n.input-group {\n\twidth: 100%;\n\tmargin-bottom: 20px;\n}\n\n.title-container {\n\tmargin-bottom: 30px;\n}\n\n.title-text {\n\tcolor: #2C1810;\n\tfont-size: 28px;\n\tfont-weight: 900;\n\tletter-spacing: 4px;\n\tfont-family: 'STKaiti', 'KaiTi', '楷体', serif;\n\ttext-shadow: 2px 2px 4px rgba(0,0,0,0.3), 1px 1px 2px rgba(139,69,19,0.4);\n\ttext-align: center;\n}\n\n.verification-group {\n\twidth: 100%;\n\tmargin-bottom: 30px;\n\tdisplay: flex;\n\tflex-direction: row;\n\talign-items: flex-end;\n\tgap: 12px;\n}\n\n.verification-input {\n\tflex: 1;\n}\n\n.get-code-btn {\n\twidth: 110px;\n\theight: 48px;\n\tbackground: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);\n\tborder-radius: 8px;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tposition: relative;\n\toverflow: hidden;\n\tbox-shadow: 0 2px 8px rgba(139, 69, 19, 0.2);\n\tborder: 1px solid rgba(139, 69, 19, 0.3);\n\ttransition: all 0.3s ease;\n}\n\n.get-code-btn:active {\n\ttransform: translateY(1px);\n\tbox-shadow: 0 1px 4px rgba(139, 69, 19, 0.3);\n}\n\n.get-code-btn.disabled {\n\tbackground: rgba(139, 69, 19, 0.3);\n\tcolor: rgba(255, 255, 255, 0.6);\n\tcursor: not-allowed;\n}\n\n.get-code-text {\n\tcolor: #FFFFFF;\n\tfont-size: 13px;\n\tfont-weight: 600;\n\tletter-spacing: 0.5px;\n\tfont-family: 'STKaiti', 'KaiTi', '楷体', serif;\n\ttext-shadow: 1px 1px 2px rgba(0,0,0,0.2);\n}\n\n.reset-btn-container {\n\twidth: 100%;\n\tmargin-top: 30px;\n\tmargin-bottom: 20px;\n}\n\n.reset-btn {\n\twidth: 100%;\n\theight: 52px;\n\tbackground: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);\n\tborder-radius: 26px;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tposition: relative;\n\toverflow: hidden;\n\tbox-shadow: 0 4px 16px rgba(139, 69, 19, 0.3);\n\tborder: none;\n\ttransition: all 0.3s ease;\n}\n\n.reset-btn:active {\n\ttransform: translateY(2px);\n\tbox-shadow: 0 2px 8px rgba(139, 69, 19, 0.4);\n}\n\n.reset-text {\n\tcolor: #FFFFFF;\n\tfont-size: 18px;\n\tfont-weight: 600;\n\tletter-spacing: 2px;\n\tfont-family: 'STKaiti', 'KaiTi', '楷体', serif;\n\ttext-shadow: 1px 1px 2px rgba(0,0,0,0.3);\n\tposition: relative;\n\tz-index: 10;\n}\n\n.back-container {\n\tdisplay: flex;\n\tflex-direction: row;\n\talign-items: center;\n\tmargin-bottom: 40px;\n\tpadding: 10px;\n}\n\n.back-text {\n\tcolor: #8B4513;\n\tfont-size: 16px;\n\tfont-weight: 500;\n\tfont-family: 'STKaiti', 'KaiTi', '楷体', serif;\n\ttext-shadow: 1px 1px 2px rgba(0,0,0,0.2);\n\tletter-spacing: 1px;\n\ttext-decoration: underline;\n\ttext-decoration-color: rgba(139, 69, 19, 0.5);\n}\n</style>\n", "import mod from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./forget-password.vue?vue&type=style&index=0&id=35706aac&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./forget-password.vue?vue&type=style&index=0&id=35706aac&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751873713554\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}