<view class="background-list-container"><view class="tab-section"><uv-tabs vue-id="65256e16-1" list="{{tabList}}" current="{{currentTab}}" lineColor="#8B1538" activeStyle="{{({color:'#8B1538',fontWeight:'bold'})}}" data-event-opts="{{[['^change',[['onTabChange']]]]}}" bind:change="__e" bind:__l="__l"></uv-tabs></view><view class="content-section"><block wx:if="{{$root.g0===0}}"><view class="empty-list"><text class="empty-text">暂无背景图片</text></view></block><block wx:else><view class="background-grid"><block wx:for="{{filteredBackgrounds}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectBackgroundImage',['$0'],[[['filteredBackgrounds','',index]]]]]]]}}" class="{{['background-item',(isSelectMode)?'selectable':'']}}" bindtap="__e"><view class="background-preview"><image class="background-image" src="{{item.url}}" mode="aspectFit"></image></view><view class="background-info"><text class="background-name">{{item.name}}</text><view class="background-type">{{item.type}}</view></view><block wx:if="{{!isSelectMode}}"><view data-event-opts="{{[['tap',[['confirmDelete',[index]]]]]}}" class="delete-icon" catchtap="__e"><u-icon vue-id="{{'65256e16-2-'+index}}" name="trash" color="#8B1538" size="20" bind:__l="__l"></u-icon></view></block><block wx:else><view class="select-icon"><u-icon vue-id="{{'65256e16-3-'+index}}" name="checkmark" color="#8B1538" size="20" bind:__l="__l"></u-icon></view></block></view></block></view></block></view><block wx:if="{{!isSelectMode}}"><view data-event-opts="{{[['tap',[['openAddPopup',['$event']]]]]}}" class="add-button" bindtap="__e"><u-icon vue-id="65256e16-4" name="plus" color="#FFFFFF" size="24" bind:__l="__l"></u-icon></view></block><u-popup vue-id="65256e16-5" show="{{showAddPopup}}" mode="center" closeOnClickOverlay="{{false}}" safeAreaInsetBottom="{{true}}" round="{{10}}" closeable="{{true}}" data-event-opts="{{[['^close',[['closeAddPopup']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="add-popup-content"><view class="popup-title">添加背景图片</view>`<view class="form-item"><text class="form-label">图片名称</text><input class="form-input" placeholder="请输入图片名称" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['newBackground']]]]]}}" value="{{newBackground.name}}" bindinput="__e"/></view><view class="form-item"><text class="form-label">图片类型</text><picker class="form-picker" value="{{typeIndex}}" range="{{backgroundTypes}}" data-event-opts="{{[['change',[['onTypeChange',['$event']]]]]}}" bindchange="__e"><view class="picker-content"><text class="picker-text">{{backgroundTypes[typeIndex]}}</text><u-icon vue-id="{{('65256e16-6')+','+('65256e16-5')}}" name="arrow-down" size="14" color="#666" bind:__l="__l"></u-icon></view></picker></view><view class="form-item"><text class="form-label">上传图片</text><u-upload vue-id="{{('65256e16-7')+','+('65256e16-5')}}" fileList="{{uploadFileList}}" maxCount="{{1}}" width="{{200}}" height="{{200}}" uploadText="选择图片" imageMode="aspectFit" data-event-opts="{{[['^afterRead',[['afterRead']]],['^delete',[['deleteUpload']]]]}}" bind:afterRead="__e" bind:delete="__e" bind:__l="__l"></u-upload></view><view class="popup-actions"><view data-event-opts="{{[['tap',[['closeAddPopup',['$event']]]]]}}" class="popup-btn cancel" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['submitForm',['$event']]]]]}}" class="popup-btn confirm" bindtap="__e">确认</view></view></view></u-popup><u-modal vue-id="65256e16-8" show="{{showDeleteModal}}" title="删除确认" content="确定要删除这张背景图片吗？" showCancelButton="{{true}}" data-event-opts="{{[['^confirm',[['deleteBackground']]],['^cancel',[['closeDeleteModal']]]]}}" bind:confirm="__e" bind:cancel="__e" bind:__l="__l"></u-modal></view>