<view class="container"><view class="content"><view class="edit-form"><view class="form-title"><text>{{isEdit?'编辑会议室':'添加会议室'}}</text></view><view class="form-item"><text class="form-label">会议室名称</text><input class="form-input" placeholder="请输入会议室名称" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['roomInfo']]]]]}}" value="{{roomInfo.name}}" bindinput="__e"/></view><view class="form-item"><text class="form-label">设备型号</text><input class="form-input" placeholder="请输入设备型号" data-event-opts="{{[['input',[['__set_model',['$0','model','$event',[]],['roomInfo']]]]]}}" value="{{roomInfo.model}}" bindinput="__e"/></view><view class="form-item"><text class="form-label">设备版本</text><input class="form-input" placeholder="请输入设备版本" data-event-opts="{{[['input',[['__set_model',['$0','version','$event',[]],['roomInfo']]]]]}}" value="{{roomInfo.version}}" bindinput="__e"/></view><view class="form-item"><text class="form-label">设备类型</text><input class="form-input" placeholder="请输入设备类型" data-event-opts="{{[['input',[['__set_model',['$0','type','$event',[]],['roomInfo']]]]]}}" value="{{roomInfo.type}}" bindinput="__e"/></view><view class="form-item"><text class="form-label">设备编号</text><input class="form-input" placeholder="请输入设备编号" data-event-opts="{{[['input',[['__set_model',['$0','code','$event',[]],['roomInfo']]]]]}}" value="{{roomInfo.code}}" bindinput="__e"/></view><view class="form-item"><text class="form-label">设备频道</text><input class="form-input" placeholder="请输入设备频道" data-event-opts="{{[['input',[['__set_model',['$0','channel','$event',[]],['roomInfo']]]]]}}" value="{{roomInfo.channel}}" bindinput="__e"/></view><view class="form-item"><text class="form-label">设备状态</text><view class="status-selector"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="{{['status-option',(roomInfo.status==='active')?'active':'']}}" bindtap="__e"><text>启用</text></view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="{{['status-option',(roomInfo.status==='inactive')?'active':'']}}" bindtap="__e"><text>禁用</text></view></view></view></view></view><view class="footer"><view class="button-group"><view data-event-opts="{{[['tap',[['cancelEdit',['$event']]]]]}}" class="cancel-button" bindtap="__e"><text>取消</text></view><view data-event-opts="{{[['tap',[['saveRoom',['$event']]]]]}}" class="save-button" bindtap="__e"><text>保存</text></view></view></view></view>