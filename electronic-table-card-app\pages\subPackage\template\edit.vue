<template>
	<view class="template-editor">
		<!-- 顶部工具栏 -->
		<view class="toolbar">
			<view class="toolbar-left">
				<view class="back-btn" @click="goBack">
					<text class="icon">←</text>
				</view>
				<input
					class="template-name-input"
					v-model="templateName"
					placeholder="输入模板名称"
					maxlength="20"
				/>
			</view>
			<view class="toolbar-right">
				<view class="tool-btn" @click="openPreview" :disabled="!hasElements">
					<text class="icon">👁</text>
					<text class="text">预览</text>
				</view>
				<view class="tool-btn primary" @click="saveTemplate" :disabled="!canSave">
					<text class="icon">💾</text>
					<text class="text">保存</text>
				</view>
			</view>
		</view>

		<!-- Canvas画布区域 (占屏幕高度1/3) -->
		<view class="canvas-container" :style="canvasContainerStyle">
			<view class="canvas-wrapper">
				<canvas
					type="2d"
					class="skyline-canvas"
					canvas-id="skylineCanvas"
					:style="canvasStyle"
					@touchstart="handleTouchStart"
					@touchmove="handleTouchMove"
					@touchend="handleTouchEnd"
					@tap="handleCanvasTap"
				></canvas>

				<!-- 画布工具提示 -->
				<view class="canvas-hint" v-if="!hasElements && !insertType">
					<text class="hint-text">选择下方工具开始创建模板</text>
				</view>
			</view>
		</view>

		<!-- 工具栏区域 (占屏幕高度2/3) -->
		<view class="toolbar-container" :style="toolbarContainerStyle">
			<!-- 插入工具 -->
			<view class="tool-section">
				<view class="section-title">
					<text class="title">插入元素</text>
				</view>
				<view class="tool-buttons">
					<view
						v-for="tool in insertTools"
						:key="tool.type"
						class="tool-button"
						:class="{ active: insertType === tool.type }"
						@click="setInsertType(tool.type)"
					>
						<text class="tool-icon">{{ tool.icon }}</text>
						<text class="tool-label">{{ tool.label }}</text>
					</view>
				</view>
			</view>

			<!-- 背景工具 -->
			<view class="tool-section">
				<view class="section-title">
					<text class="title">背景设置</text>
				</view>
				<view class="tool-buttons">
					<view
						v-for="bg in backgroundTools"
						:key="bg.type"
						class="tool-button small"
						:class="{ active: backgroundType === bg.type }"
						@click="setBackgroundType(bg.type)"
					>
						<text class="tool-icon">{{ bg.icon }}</text>
						<text class="tool-label">{{ bg.label }}</text>
					</view>
				</view>
			</view>

			<!-- 属性设置 -->
			<view class="tool-section" v-if="selectedElement">
				<view class="section-title">
					<text class="title">属性设置</text>
				</view>
				<view class="property-controls">
					<!-- 字体大小 -->
					<view class="property-item">
						<text class="label">字号</text>
						<view class="number-control">
							<view class="control-btn" @click="adjustFontSize(-2)">-</view>
							<input class="number-input" v-model.number="currentFontSize" type="number" @input="updateFontSize" />
							<view class="control-btn" @click="adjustFontSize(2)">+</view>
						</view>
					</view>

					<!-- 颜色选择 -->
					<view class="property-item">
						<text class="label">颜色</text>
						<view class="color-options">
							<view
								v-for="color in colorOptions"
								:key="color"
								class="color-item"
								:class="{ active: currentTextColor === color }"
								:style="{ backgroundColor: color }"
								@click="selectColor(color)"
							></view>
						</view>
					</view>

					<!-- 操作按钮 -->
					<view class="element-actions">
						<view class="action-btn" @click="editElementText" v-if="selectedElement.type === 'text'">
							<text class="icon">✏️</text>
							<text class="text">编辑</text>
						</view>
						<view class="action-btn" @click="duplicateElement">
							<text class="icon">📋</text>
							<text class="text">复制</text>
						</view>
						<view class="action-btn danger" @click="deleteElement">
							<text class="icon">🗑️</text>
							<text class="text">删除</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 快捷操作 -->
			<view class="tool-section">
				<view class="section-title">
					<text class="title">快捷操作</text>
				</view>
				<view class="quick-actions">
					<view class="action-btn" @click="centerAllElements" :disabled="!hasElements">
						<text class="icon">⚡</text>
						<text class="text">居中对齐</text>
					</view>
					<view class="action-btn danger" @click="clearAllElements" :disabled="!hasElements">
						<text class="icon">🗑</text>
						<text class="text">清空画布</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 文字编辑弹窗 -->
		<u-popup
			:show="showEditTextPopup"
			mode="center"
			@close="closeEditTextPopup"
			:round="16"
			closeable
		>
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">编辑文字内容</text>
				</view>
				<view class="popup-body">
					<textarea
						v-model="editingText"
						class="text-input"
						placeholder="请输入文字内容"
						maxlength="100"
						auto-height
					></textarea>
				</view>
				<view class="popup-footer">
					<view class="popup-btn secondary" @click="closeEditTextPopup">取消</view>
					<view class="popup-btn primary" @click="confirmEditText">确认</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script> 

export default {
	data() {
		return {
			// 模板基本信息
			templateName: '新增模板',
			
			// 画布相关
			canvasContext: null,
			canvasWidth: 800,  // 桌面牌宽度
			canvasHeight: 480, // 桌面牌高度
			screenInfo: null,
			
			// 元素管理
			canvasElements: [],
			selectedElement: null,
			insertType: null,
			backgroundType: 'solid',
			
			// 触摸交互
			dragging: false,
			lastTouchX: 0,
			lastTouchY: 0,
			
			// 文字属性
			currentFontSize: 24,
			currentTextColor: '#000000',
			
			// 弹窗状态
			showEditTextPopup: false,
			editingText: ''
		}
	},
	
	computed: {
		// 画布容器样式 (占屏幕高度1/3)
		canvasContainerStyle() {
			const screenHeight = this.screenInfo?.windowHeight || 800
			const toolbarHeight = 60 // 顶部工具栏高度
			const availableHeight = screenHeight - toolbarHeight
			const canvasContainerHeight = Math.floor(availableHeight / 3)
			
			return {
				height: `${canvasContainerHeight}px`,
				backgroundColor: '#f5f5f5',
				display: 'flex',
				alignItems: 'center',
				justifyContent: 'center'
			}
		},
		
		// 工具栏容器样式 (占屏幕高度2/3)
		toolbarContainerStyle() {
			const screenHeight = this.screenInfo?.windowHeight || 800
			const toolbarHeight = 60 // 顶部工具栏高度
			const availableHeight = screenHeight - toolbarHeight
			const toolbarContainerHeight = Math.floor(availableHeight * 2 / 3)
			
			return {
				height: `${toolbarContainerHeight}px`,
				overflowY: 'auto'
			}
		},
		
		// Canvas样式 (保持800:480比例)
		canvasStyle() {
			const screenWidth = this.screenInfo?.windowWidth || 375
			const maxWidth = screenWidth * 0.9 // 留出边距
			
			// 计算适合的尺寸，保持800:480比例
			const aspectRatio = 800 / 480
			let displayWidth = maxWidth
			let displayHeight = displayWidth / aspectRatio
			
			// 确保高度不超过容器
			const maxHeight = (this.screenInfo?.windowHeight || 800) / 3 - 40
			if (displayHeight > maxHeight) {
				displayHeight = maxHeight
				displayWidth = displayHeight * aspectRatio
			}
			
			return {
				width: `${displayWidth}px`,
				height: `${displayHeight}px`,
				border: '2px solid #ddd',
				borderRadius: '8px',
				backgroundColor: '#fff'
			}
		},
		
		// 插入工具
		insertTools() {
			return [
				{ type: 'text', icon: '📝', label: '文字' },
				{ type: 'image', icon: '🖼️', label: '图片' }
			]
		},
		
		// 背景工具
		backgroundTools() {
			return [
				{ type: 'solid', icon: '🎨', label: '纯色' },
				{ type: 'clear', icon: '🗑️', label: '透明' }
			]
		},
		
		// 颜色选项
		colorOptions() {
			return [
				'#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF',
				'#FFFF00', '#FF00FF', '#00FFFF', '#FFA500', '#800080'
			]
		},
		
		// 是否有元素
		hasElements() {
			return this.canvasElements.length > 0
		},
		
		// 是否可以保存
		canSave() {
			return this.templateName.trim() && this.hasElements
		}
	},
	
	onLoad() {
		this.initCanvas()
	},
	
	onReady() {
		this.getSystemInfo()
	},
	
	methods: {
		// 获取系统信息
		getSystemInfo() {
			uni.getSystemInfo({
				success: (res) => {
					this.screenInfo = res
					this.$forceUpdate()
				}
			})
		},
		
		// 初始化Canvas
		async initCanvas() {
			try {
				// 使用skyline渲染的2d context
				const query = uni.createSelectorQuery().in(this)
				const canvas = await new Promise((resolve) => {
					query.select('.skyline-canvas')
						.fields({ node: true, size: true })
						.exec((res) => {
							if (res[0]) {
								resolve(res[0].node)
							}
						})
				})
				
				if (canvas) {
					this.canvasContext = canvas.getContext('2d')
					
					// 设置Canvas实际尺寸为桌面牌分辨率
					canvas.width = this.canvasWidth
					canvas.height = this.canvasHeight
					
					// 初始绘制
					this.drawCanvas()
				}
			} catch (error) {
				console.error('Canvas初始化失败:', error)
				uni.showToast({
					title: 'Canvas初始化失败',
					icon: 'none'
				})
			}
		},
		
		// 绘制Canvas
		drawCanvas() {
			if (!this.canvasContext) return
			
			const ctx = this.canvasContext
			
			// 清空画布
			ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)
			
			// 绘制背景
			this.drawBackground()
			
			// 绘制所有元素
			this.canvasElements.forEach(element => {
				this.drawElement(element)
			})
			
			// 绘制选中元素的高亮
			if (this.selectedElement) {
				this.drawElementHighlight(this.selectedElement)
			}
		},
		
		// 绘制背景
		drawBackground() {
			const ctx = this.canvasContext
			
			switch (this.backgroundType) {
				case 'solid':
					ctx.fillStyle = '#FFFFFF'
					ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)
					break
				case 'clear':
					// 透明背景，不绘制
					break
				default:
					ctx.fillStyle = '#FFFFFF'
					ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)
			}
		},
		
		// 绘制元素
		drawElement(element) {
			if (element.type !== 'text') return
			
			const ctx = this.canvasContext
			
			// 设置文字样式
			ctx.fillStyle = element.color || '#000000'
			ctx.font = `${element.fontSize || 24}px Arial`
			ctx.textAlign = 'center'
			ctx.textBaseline = 'middle'
			
			// 绘制文字
			ctx.fillText(element.text || '', element.x, element.y)
		},
		
		// 绘制元素高亮
		drawElementHighlight(element) {
			if (element.type !== 'text') return
			
			const ctx = this.canvasContext
			const bounds = this.getElementBounds(element)
			
			// 绘制选中边框
			ctx.strokeStyle = '#007AFF'
			ctx.lineWidth = 2
			ctx.setLineDash([5, 5])
			ctx.strokeRect(bounds.x, bounds.y, bounds.width, bounds.height)
			ctx.setLineDash([])
		},
		
		// 获取元素边界
		getElementBounds(element) {
			if (element.type === 'text') {
				const fontSize = element.fontSize || 24
				const text = element.text || ''
				const textWidth = text.length * fontSize * 0.6 // 估算文字宽度
				const textHeight = fontSize
				
				return {
					x: element.x - textWidth / 2 - 5,
					y: element.y - textHeight / 2 - 5,
					width: textWidth + 10,
					height: textHeight + 10
				}
			}
			return { x: 0, y: 0, width: 0, height: 0 }
		},
		
		// 触摸事件处理
		handleTouchStart(e) {
			const touch = this.getTouchPosition(e)
			if (!touch) return
			
			this.lastTouchX = touch.x
			this.lastTouchY = touch.y
			
			// 检查是否点击到元素
			const element = this.getElementAtPosition(touch.x, touch.y)
			if (element) {
				this.selectElement(element)
				this.dragging = true
			} else {
				this.deselectElement()
			}
		},
		
		handleTouchMove(e) {
			if (!this.dragging || !this.selectedElement) return
			
			const touch = this.getTouchPosition(e)
			if (!touch) return
			
			const deltaX = touch.x - this.lastTouchX
			const deltaY = touch.y - this.lastTouchY
			
			// 移动元素
			this.moveElement(this.selectedElement, deltaX, deltaY)
			
			// 更新触摸位置
			this.lastTouchX = touch.x
			this.lastTouchY = touch.y
			
			// 重绘画布
			this.drawCanvas()
		},
		
		handleTouchEnd() {
			this.dragging = false
		},
		
		handleCanvasTap(e) {
			if (this.dragging) return
			
			const touch = this.getTouchPosition(e)
			if (!touch) return
			
			const element = this.getElementAtPosition(touch.x, touch.y)
			if (element) {
				this.selectElement(element)
			} else if (this.insertType) {
				this.addElementAtPosition(touch.x, touch.y)
			}
		},
		
		// 获取触摸位置（转换为Canvas坐标）
		getTouchPosition(e) {
			const touch = e.detail || e.touches?.[0] || e
			if (!touch) return null
			
			// 获取Canvas显示尺寸
			const canvasStyle = this.canvasStyle
			const displayWidth = parseFloat(canvasStyle.width)
			const displayHeight = parseFloat(canvasStyle.height)
			
			// 转换为Canvas实际坐标
			const scaleX = this.canvasWidth / displayWidth
			const scaleY = this.canvasHeight / displayHeight
			
			return {
				x: touch.x * scaleX,
				y: touch.y * scaleY
			}
		},
		
		// 获取指定位置的元素
		getElementAtPosition(x, y) {
			// 从后往前遍历（后添加的元素在上层）
			for (let i = this.canvasElements.length - 1; i >= 0; i--) {
				const element = this.canvasElements[i]
				if (this.isPointInElement(x, y, element)) {
					return element
				}
			}
			return null
		},
		
		// 检查点是否在元素内
		isPointInElement(x, y, element) {
			if (element.type === 'text') {
				const bounds = this.getElementBounds(element)
				return x >= bounds.x && x <= bounds.x + bounds.width &&
					   y >= bounds.y && y <= bounds.y + bounds.height
			}
			return false
		},
		
		// 选中元素
		selectElement(element) {
			this.selectedElement = element
			this.currentFontSize = element.fontSize || 24
			this.currentTextColor = element.color || '#000000'
			this.drawCanvas()
		},
		
		// 取消选中
		deselectElement() {
			this.selectedElement = null
			this.drawCanvas()
		},
		
		// 移动元素
		moveElement(element, deltaX, deltaY) {
			element.x += deltaX
			element.y += deltaY
			
			// 边界限制
			element.x = Math.max(20, Math.min(element.x, this.canvasWidth - 20))
			element.y = Math.max(20, Math.min(element.y, this.canvasHeight - 20))
		},
		
		// 在指定位置添加元素
		addElementAtPosition(x, y) {
			if (this.insertType === 'text') {
				const newElement = {
					id: Date.now(),
					type: 'text',
					text: '新文字',
					x: x,
					y: y,
					color: this.currentTextColor,
					fontSize: this.currentFontSize
				}
				
				this.canvasElements.push(newElement)
				this.selectElement(newElement)
				this.insertType = null // 清除插入模式
				
				uni.showToast({
					title: '元素已添加',
					icon: 'success',
					duration: 1000
				})
			}
		},
		
		// 设置插入类型
		setInsertType(type) {
			this.insertType = this.insertType === type ? null : type
		},
		
		// 设置背景类型
		setBackgroundType(type) {
			this.backgroundType = type
			this.drawCanvas()
		},
		
		// 调整字体大小
		adjustFontSize(delta) {
			if (this.selectedElement) {
				this.selectedElement.fontSize = Math.max(12, Math.min(100, (this.selectedElement.fontSize || 24) + delta))
				this.currentFontSize = this.selectedElement.fontSize
				this.drawCanvas()
			}
		},
		
		// 更新字体大小
		updateFontSize() {
			if (this.selectedElement) {
				this.selectedElement.fontSize = this.currentFontSize
				this.drawCanvas()
			}
		},
		
		// 选择颜色
		selectColor(color) {
			this.currentTextColor = color
			if (this.selectedElement) {
				this.selectedElement.color = color
				this.drawCanvas()
			}
		},
		
		// 编辑元素文字
		editElementText() {
			if (this.selectedElement && this.selectedElement.type === 'text') {
				this.editingText = this.selectedElement.text
				this.showEditTextPopup = true
			}
		},
		
		// 确认编辑文字
		confirmEditText() {
			if (this.selectedElement && this.editingText.trim()) {
				this.selectedElement.text = this.editingText.trim()
				this.drawCanvas()
			}
			this.closeEditTextPopup()
		},
		
		// 关闭编辑文字弹窗
		closeEditTextPopup() {
			this.showEditTextPopup = false
			this.editingText = ''
		},
		
		// 复制元素
		duplicateElement() {
			if (this.selectedElement) {
				const newElement = {
					...this.selectedElement,
					id: Date.now(),
					x: this.selectedElement.x + 20,
					y: this.selectedElement.y + 20
				}
				
				this.canvasElements.push(newElement)
				this.selectElement(newElement)
				
				uni.showToast({
					title: '元素已复制',
					icon: 'success'
				})
			}
		},
		
		// 删除元素
		deleteElement() {
			if (this.selectedElement) {
				const index = this.canvasElements.findIndex(el => el.id === this.selectedElement.id)
				if (index > -1) {
					this.canvasElements.splice(index, 1)
					this.selectedElement = null
					this.drawCanvas()
					
					uni.showToast({
						title: '元素已删除',
						icon: 'success'
					})
				}
			}
		},
		
		// 居中对齐所有元素
		centerAllElements() {
			this.canvasElements.forEach(element => {
				element.x = this.canvasWidth / 2
				element.y = this.canvasHeight / 2
			})
			this.drawCanvas()
			
			uni.showToast({
				title: '已居中对齐',
				icon: 'success'
			})
		},
		
		// 清空所有元素
		clearAllElements() {
			uni.showModal({
				title: '确认清空',
				content: '确定要清空画布上的所有元素吗？此操作不可撤销。',
				success: (res) => {
					if (res.confirm) {
						this.canvasElements = []
						this.selectedElement = null
						this.drawCanvas()
						
						uni.showToast({
							title: '画布已清空',
							icon: 'success'
						})
					}
				}
			})
		},
		
		// 打开预览
		openPreview() {
			if (!this.hasElements) {
				uni.showToast({
					title: '请先添加元素',
					icon: 'none'
				})
				return
			}
			
			// 生成预览图片
			this.generatePreviewImage()
		},
		
		// 生成预览图片
		generatePreviewImage() {
			if (!this.canvasContext) return
			
			try {
				// 使用Canvas生成图片
				uni.canvasToTempFilePath({
					canvasId: 'skylineCanvas',
					width: this.canvasWidth,
					height: this.canvasHeight,
					destWidth: this.canvasWidth,
					destHeight: this.canvasHeight,
					fileType: 'png',
					quality: 1,
					success: (res) => {
						uni.previewImage({
							urls: [res.tempFilePath],
							current: 0
						})
					},
					fail: (error) => {
						console.error('生成预览图片失败:', error)
						uni.showToast({
							title: '预览生成失败',
							icon: 'none'
						})
					}
				}, this)
			} catch (error) {
				console.error('预览失败:', error)
			}
		},
		
		// 保存模板
		saveTemplate() {
			if (!this.canSave) {
				uni.showToast({
					title: '请完善模板信息',
					icon: 'none'
				})
				return
			}
			
			// 生成模板数据
			const templateData = {
				name: this.templateName,
				elements: this.canvasElements,
				backgroundType: this.backgroundType,
				canvasWidth: this.canvasWidth,
				canvasHeight: this.canvasHeight,
				createTime: new Date().toISOString()
			}
			
			// 保存到本地存储
			try {
				const savedTemplates = uni.getStorageSync('savedTemplates') || []
				savedTemplates.push(templateData)
				uni.setStorageSync('savedTemplates', savedTemplates)
				
				uni.showToast({
					title: '模板已保存',
					icon: 'success'
				})
				
				// 延迟返回上一页
				setTimeout(() => {
					this.goBack()
				}, 1500)
			} catch (error) {
				console.error('保存失败:', error)
				uni.showToast({
					title: '保存失败',
					icon: 'none'
				})
			}
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack()
		}
	}
}
</script>

<style scoped>
.template-editor {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f8f9fa;
}

/* 顶部工具栏 */
.toolbar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 60px;
	padding: 0 16px;
	background-color: #fff;
	border-bottom: 1px solid #e5e5e5;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
	display: flex;
	align-items: center;
	gap: 12px;
}

.back-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 40px;
	height: 40px;
	border-radius: 8px;
	background-color: #f5f5f5;
	cursor: pointer;
	transition: background-color 0.2s;
}

.back-btn:hover {
	background-color: #e9ecef;
}

.back-btn .icon {
	font-size: 18px;
	color: #333;
}

.template-name-input {
	padding: 8px 12px;
	border: 1px solid #ddd;
	border-radius: 6px;
	font-size: 14px;
	min-width: 150px;
	outline: none;
}

.template-name-input:focus {
	border-color: #007AFF;
}

.toolbar-right {
	display: flex;
	gap: 8px;
}

.tool-btn {
	display: flex;
	align-items: center;
	gap: 4px;
	padding: 8px 12px;
	border-radius: 6px;
	background-color: #f5f5f5;
	cursor: pointer;
	transition: all 0.2s;
	font-size: 14px;
}

.tool-btn:hover {
	background-color: #e9ecef;
}

.tool-btn.primary {
	background-color: #007AFF;
	color: white;
}

.tool-btn.primary:hover {
	background-color: #0056b3;
}

.tool-btn:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

/* Canvas画布区域 */
.canvas-container {
	position: relative;
	border-bottom: 1px solid #e5e5e5;
}

.canvas-wrapper {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
}

.skyline-canvas {
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.canvas-hint {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	pointer-events: none;
}

.hint-text {
	color: #999;
	font-size: 16px;
	text-align: center;
}

/* 工具栏区域 */
.toolbar-container {
	padding: 16px;
	background-color: #fff;
}

.tool-section {
	margin-bottom: 20px;
}

.section-title {
	margin-bottom: 12px;
}

.section-title .title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
}

.tool-buttons {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
}

.tool-button {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 4px;
	padding: 12px 8px;
	border: 1px solid #ddd;
	border-radius: 8px;
	background-color: #fff;
	cursor: pointer;
	transition: all 0.2s;
	min-width: 60px;
}

.tool-button:hover {
	border-color: #007AFF;
	background-color: #f8f9ff;
}

.tool-button.active {
	border-color: #007AFF;
	background-color: #007AFF;
	color: white;
}

.tool-button.small {
	min-width: 50px;
	padding: 8px 6px;
}

.tool-icon {
	font-size: 20px;
}

.tool-label {
	font-size: 12px;
	text-align: center;
}

/* 属性控制 */
.property-controls {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

.property-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 12px;
}

.property-item .label {
	font-size: 14px;
	color: #333;
	min-width: 40px;
}

.number-control {
	display: flex;
	align-items: center;
	gap: 8px;
}

.control-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 32px;
	height: 32px;
	border: 1px solid #ddd;
	border-radius: 4px;
	background-color: #fff;
	cursor: pointer;
	font-size: 16px;
	transition: all 0.2s;
}

.control-btn:hover {
	border-color: #007AFF;
	background-color: #f8f9ff;
}

.number-input {
	width: 60px;
	padding: 6px 8px;
	border: 1px solid #ddd;
	border-radius: 4px;
	text-align: center;
	font-size: 14px;
	outline: none;
}

.number-input:focus {
	border-color: #007AFF;
}

.color-options {
	display: flex;
	flex-wrap: wrap;
	gap: 6px;
}

.color-item {
	width: 24px;
	height: 24px;
	border: 2px solid #ddd;
	border-radius: 4px;
	cursor: pointer;
	transition: all 0.2s;
}

.color-item:hover {
	transform: scale(1.1);
}

.color-item.active {
	border-color: #007AFF;
	border-width: 3px;
}

/* 元素操作按钮 */
.element-actions {
	display: flex;
	gap: 8px;
	margin-top: 12px;
}

.action-btn {
	display: flex;
	align-items: center;
	gap: 4px;
	padding: 8px 12px;
	border: 1px solid #ddd;
	border-radius: 6px;
	background-color: #fff;
	cursor: pointer;
	transition: all 0.2s;
	font-size: 12px;
}

.action-btn:hover {
	border-color: #007AFF;
	background-color: #f8f9ff;
}

.action-btn.danger {
	border-color: #dc3545;
	color: #dc3545;
}

.action-btn.danger:hover {
	background-color: #dc3545;
	color: white;
}

.action-btn:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

/* 快捷操作 */
.quick-actions {
	display: flex;
	gap: 12px;
}

/* 弹窗样式 */
.popup-content {
	width: 320px;
	max-width: 90vw;
	background-color: #fff;
	border-radius: 12px;
	overflow: hidden;
}

.popup-header {
	padding: 16px;
	border-bottom: 1px solid #e5e5e5;
	text-align: center;
}

.popup-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
}

.popup-body {
	padding: 16px;
}

.text-input {
	width: 100%;
	min-height: 80px;
	padding: 12px;
	border: 1px solid #ddd;
	border-radius: 6px;
	font-size: 14px;
	resize: vertical;
	outline: none;
}

.text-input:focus {
	border-color: #007AFF;
}

.popup-footer {
	display: flex;
	gap: 12px;
	padding: 16px;
	border-top: 1px solid #e5e5e5;
}

.popup-btn {
	flex: 1;
	padding: 12px;
	border: none;
	border-radius: 6px;
	font-size: 14px;
	cursor: pointer;
	transition: all 0.2s;
	text-align: center;
}

.popup-btn.secondary {
	background-color: #f5f5f5;
	color: #333;
}

.popup-btn.secondary:hover {
	background-color: #e9ecef;
}

.popup-btn.primary {
	background-color: #007AFF;
	color: white;
}

.popup-btn.primary:hover {
	background-color: #0056b3;
}

.popup-btn:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.toolbar {
		padding: 0 12px;
	}
	
	.template-name-input {
		min-width: 120px;
		font-size: 13px;
	}
	
	.tool-btn {
		padding: 6px 8px;
		font-size: 12px;
	}
	
	.toolbar-container {
		padding: 12px;
	}
	
	.tool-buttons {
		gap: 6px;
	}
	
	.tool-button {
		min-width: 50px;
		padding: 8px 6px;
	}
	
	.tool-icon {
		font-size: 16px;
	}
	
	.tool-label {
		font-size: 11px;
	}
	
	.popup-content {
		width: 280px;
	}
}
</style>
