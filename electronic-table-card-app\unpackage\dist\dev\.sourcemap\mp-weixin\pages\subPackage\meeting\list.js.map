{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/list.vue?ba53", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/list.vue?ee0a", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/list.vue?8a6c", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/list.vue?114e", "uni-app:///pages/subPackage/meeting/list.vue", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/list.vue?a701", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/list.vue?d486"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "meetingRooms", "id", "name", "model", "version", "type", "code", "channel", "status", "selectedRooms", "showBindPopup", "bindModalVisible", "currentBindItem", "selectedGateway", "gatewayList", "mac", "onLoad", "methods", "stopPropagation", "loadMeetingRooms", "showBindModal", "hideBindModal", "setTimeout", "selectGateway", "confirmBind", "uni", "title", "icon", "duration", "bindDevice", "unbindDevice", "console", "deleteRoom", "content", "success", "editRoom", "url", "addRoom", "confirmSelection"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAotB,CAAgB,orBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgIxuB;EACAC;IACA;MACAC,eACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAP;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,cACA;QACAb;QACAC;QACAa;MACA,GACA;QACAd;QACAC;QACAa;MACA,GACA;QACAd;QACAC;QACAa;MACA,GACA;QACAd;QACAC;QACAa;MACA,GACA;QACAd;QACAC;QACAa;MACA,GACA;QACAd;QACAC;QACAa;MACA,GACA;QACAd;QACAC;QACAa;MACA;IAEA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IAAA,CACA;IAEA;IACAC;MACA;MACA;IAAA,CACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACAC;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACAC;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACAF;QACAC;MACA;;MAEA;MACAJ;QACAG;QACAA;UACAC;UACAC;UACAC;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACAC;MACAN;QACAC;QACAC;MACA;IACA;IAEA;IACAK;MAAA;MACAD;MACAN;QACAC;QACAO;QACAC;UACA;YACA;YACA;cAAA;YAAA;YACAT;cACAC;cACAC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAQ;MACAJ;MACA;MACAN;QACAW;MACA;IACA;IAEA;IACAC;MACAN;MACA;MACAN;QACAW;MACA;IACA;IAEA;IACAE;MACAP;MACAN;QACAC;QACAC;MACA;MACA;MACAL;QACAG;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnUA;AAAA;AAAA;AAAA;AAA21C,CAAgB,2tCAAG,EAAC,C;;;;;;;;;;;ACA/2C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/subPackage/meeting/list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/subPackage/meeting/list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list.vue?vue&type=template&id=74ef4cdc&\"\nvar renderjs\nimport script from \"./list.vue?vue&type=script&lang=js&\"\nexport * from \"./list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/subPackage/meeting/list.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=template&id=74ef4cdc&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<!-- 主内容区域 -->\n\t\t<view class=\"content\">\n\t\t\t<!-- 会议室列表 -->\n\t\t\t<view class=\"meeting-list\">\n\t\t\t\t<!-- 会议室项 -->\n\t\t\t\t<view class=\"meeting-item\" v-for=\"(item, index) in meetingRooms\" :key=\"index\">\n\t\t\t\t\t<view class=\"meeting-header\">\n\t\t\t\t\t\t<image class=\"header-icon\" src=\"/static/images/logo_icon_text.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t<text class=\"meeting-name-header\">{{ item.name }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"meeting-body\">\n\t\t\t\t\t\t<view class=\"meeting-icon-container\">\n\t\t\t\t\t\t\t<view class=\"status-dot-outer\" :class=\"{'active-outer': item.status === 'active'}\">\n\t\t\t\t\t\t\t\t<view class=\"status-dot-inner\" :class=\"{active: item.status === 'active'}\"></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"main-icon-bg\">\n\t\t\t\t\t\t\t\t<image class=\"main-icon\" src=\"/static/images/logo_icon.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"meeting-details\">\n\t\t\t\t\t\t\t<view class=\"spec-row\">\n\t\t\t\t\t\t\t\t<text class=\"spec-label\">型号：</text>\n\t\t\t\t\t\t\t\t<text class=\"spec-value\">{{ item.model || '未绑定' }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"spec-row\">\n\t\t\t\t\t\t\t\t<text class=\"spec-label\">版本：</text>\n\t\t\t\t\t\t\t\t<text class=\"spec-value\">{{ item.version || '未绑定' }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"spec-row\">\n\t\t\t\t\t\t\t\t<text class=\"spec-label\">类型：</text>\n\t\t\t\t\t\t\t\t<text class=\"spec-value\">{{ item.type || '未绑定' }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"spec-row\">\n\t\t\t\t\t\t\t\t<text class=\"spec-label\">编号：</text>\n\t\t\t\t\t\t\t\t<text class=\"spec-value\">{{ item.code || '未绑定' }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"spec-row\">\n\t\t\t\t\t\t\t\t<text class=\"spec-label\">频道：</text>\n\t\t\t\t\t\t\t\t<text class=\"spec-value\">{{ item.channel || '未绑定' }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"meeting-actions\">\n\t\t\t\t\t\t<view class=\"action-button unbind\" @click=\"unbindDevice(item)\">\n\t\t\t\t\t\t\t<image class=\"action-icon\" src=\"/static/images/gateway_unbinding.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t<text>未绑定</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"action-button bind\" @click=\"showBindModal(item)\">\n\t\t\t\t\t\t\t<text>绑定</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"action-button delete\" @click=\"deleteRoom(item)\">\n\t\t\t\t\t\t\t<text>删除</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"action-button edit\" @click=\"editRoom(item)\">\n\t\t\t\t\t\t\t<text>编辑</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 添加按钮 -->\n\t\t<view class=\"add-button\" @click=\"addRoom\">\n\t\t\t<text class=\"add-icon\">+</text>\n\t\t</view>\n\t\t\n\t\t<!-- Bottom Confirm Button -->\n\t\t<view class=\"confirm-button-container\">\n\t\t\t<view class=\"confirm-button-wrapper\">\n\t\t\t\t<image src=\"http://14.103.146.84:8890/i/2025/06/12/btn.png\" class=\"confirm-button-bg\" mode=\"aspectFill\"></image>\n\t\t\t\t<view class=\"confirm-button\" @click=\"confirmSelection\">\n\t\t\t\t\t<text>确定</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 绑定弹窗 -->\n\t\t<view class=\"bind-modal-overlay\" v-if=\"showBindPopup\" @click=\"hideBindModal\">\n\t\t\t<view class=\"bind-modal\" :class=\"{show: bindModalVisible}\" @click.stop=\"stopPropagation\">\n\t\t\t\t<view class=\"bind-modal-header\">\n\t\t\t\t\t<text class=\"bind-modal-title\">绑定设备</text>\n\t\t\t\t\t<view class=\"bind-modal-close\" @click=\"hideBindModal\">\n\t\t\t\t\t\t<text>×</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"bind-modal-content\">\n\t\t\t\t\t<view class=\"bind-info\">\n\t\t\t\t\t\t<text class=\"bind-info-title\">{{ currentBindItem && currentBindItem.name }}</text>\n\t\t\t\t\t\t<text class=\"bind-info-desc\">请选择要绑定的网关设备</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 网关列表 -->\n\t\t\t\t\t<view class=\"gateway-list\">\n\t\t\t\t\t\t<view class=\"gateway-header\">\n\t\t\t\t\t\t\t<text class=\"header-cell\">选择</text>\n\t\t\t\t\t\t\t<text class=\"header-cell\">网关名称</text>\n\t\t\t\t\t\t\t<text class=\"header-cell\">网关MAC</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"gateway-item\" v-for=\"(gateway, index) in gatewayList\" :key=\"index\" @click=\"selectGateway(gateway)\">\n\t\t\t\t\t\t\t<view class=\"gateway-cell radio-cell\">\n\t\t\t\t\t\t\t\t<radio :checked=\"selectedGateway && selectedGateway.id === gateway.id\" color=\"#8B0000\" />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"gateway-cell name-cell\">\n\t\t\t\t\t\t\t\t<text>{{ gateway.name }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"gateway-cell mac-cell\">\n\t\t\t\t\t\t\t\t<text>{{ gateway.mac }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"bind-actions\">\n\t\t\t\t\t\t<view class=\"bind-action-btn cancel\" @click=\"hideBindModal\">\n\t\t\t\t\t\t\t<text>取消</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"bind-action-btn confirm\" @click=\"confirmBind\">\n\t\t\t\t\t\t\t<text>确认绑定</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tmeetingRooms: [\n\t\t\t\t\t{\n\t\t\t\t\t\tid: '1',\n\t\t\t\t\t\tname: '1号会议室',\n\t\t\t\t\t\tmodel: '未绑定',\n\t\t\t\t\t\tversion: '未绑定',\n\t\t\t\t\t\ttype: '未绑定',\n\t\t\t\t\t\tcode: '未绑定',\n\t\t\t\t\t\tchannel: '未绑定',\n\t\t\t\t\t\tstatus: 'active'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: '2',\n\t\t\t\t\t\tname: '121212',\n\t\t\t\t\t\tmodel: '未绑定',\n\t\t\t\t\t\tversion: '未绑定',\n\t\t\t\t\t\ttype: '未绑定',\n\t\t\t\t\t\tcode: '未绑定',\n\t\t\t\t\t\tchannel: '未绑定',\n\t\t\t\t\t\tstatus: 'inactive'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tselectedRooms: [],\n\t\t\t\tshowBindPopup: false,\n\t\t\t\tbindModalVisible: false,\n\t\t\t\tcurrentBindItem: null,\n\t\t\t\tselectedGateway: null,\n\t\t\t\tgatewayList: [\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 1,\n\t\t\t\t\t\tname: '网关设备-001',\n\t\t\t\t\t\tmac: 'AA:BB:CC:DD:EE:01'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 2,\n\t\t\t\t\t\tname: '网关设备-002',\n\t\t\t\t\t\tmac: 'AA:BB:CC:DD:EE:02'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 3,\n\t\t\t\t\t\tname: '网关设备-003',\n\t\t\t\t\t\tmac: 'AA:BB:CC:DD:EE:03'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 4,\n\t\t\t\t\t\tname: '网关设备-003',\n\t\t\t\t\t\tmac: 'AA:BB:CC:DD:EE:03'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 5,\n\t\t\t\t\t\tname: '网关设备-003',\n\t\t\t\t\t\tmac: 'AA:BB:CC:DD:EE:03'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 6,\n\t\t\t\t\t\tname: '网关设备-003',\n\t\t\t\t\t\tmac: 'AA:BB:CC:DD:EE:03'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 7,\n\t\t\t\t\t\tname: '网关设备-003',\n\t\t\t\t\t\tmac: 'AA:BB:CC:DD:EE:03'\n\t\t\t\t\t}\n\t\t\t\t]\n\t\t\t};\n\t\t},\n\t\tonLoad() {\n\t\t\t// 加载会议室列表数据\n\t\t\tthis.loadMeetingRooms();\n\t\t},\n\t\tmethods: {\n\t\t\t// 阻止事件冒泡\n\t\t\tstopPropagation() {\n\t\t\t\t// 空函数，用于阻止事件冒泡\n\t\t\t},\n\n\t\t\t// 加载会议室列表\n\t\t\tloadMeetingRooms() {\n\t\t\t\t// 这里应该调用API获取会议室列表\n\t\t\t\t// 目前使用模拟数据\n\t\t\t},\n\t\t\t\n\t\t\t// 显示绑定弹窗\n\t\t\tshowBindModal(item) {\n\t\t\t\tthis.currentBindItem = item;\n\t\t\t\tthis.selectedGateway = null; // 重置选中的网关\n\t\t\t\tthis.bindModalVisible = true;\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tthis.showBindPopup = true;\n\t\t\t\t});\n\t\t\t},\n\t\t\thideBindModal() {\n\t\t\t\tthis.showBindPopup = false;\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.bindModalVisible = false;\n\t\t\t\t\tthis.currentBindItem = null;\n\t\t\t\t\tthis.selectedGateway = null;\n\t\t\t\t}, 300);\n\t\t\t},\n\t\t\tselectGateway(gateway) {\n\t\t\t\tthis.selectedGateway = gateway;\n\t\t\t},\n\t\t\tconfirmBind() {\n\t\t\t\tif (!this.selectedGateway) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请选择要绑定的网关设备',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 执行绑定逻辑\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '绑定中...'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 模拟绑定请求\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: `${this.currentBindItem.name} 已成功绑定到 ${this.selectedGateway.name}`,\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\tthis.hideBindModal();\n\t\t\t\t}, 1500);\n\t\t\t},\n\t\t\t\n\t\t\t// 绑定设备（保留原方法以防其他地方调用）\n\t\t\tbindDevice(item) {\n\t\t\t\tthis.showBindModal(item);\n\t\t\t},\n\t\t\t\n\t\t\t// 解绑设备\n\t\t\tunbindDevice(item) {\n\t\t\t\tconsole.log('解绑设备:', item);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '已切换为未绑定状态',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 删除会议室\n\t\t\tdeleteRoom(item) {\n\t\t\t\tconsole.log('删除会议室:', item);\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认删除',\n\t\t\t\t\tcontent: `确定要删除${item.name}吗？`,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t// 从列表中移除\n\t\t\t\t\t\t\tthis.meetingRooms = this.meetingRooms.filter(room => room.id !== item.id);\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '删除成功',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 编辑会议室\n\t\t\teditRoom(item) {\n\t\t\t\tconsole.log('编辑会议室:', item);\n\t\t\t\t// 跳转到编辑页面\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/meeting/edit?id=${item.id}`\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 添加会议室\n\t\t\taddRoom() {\n\t\t\t\tconsole.log('添加会议室');\n\t\t\t\t// 跳转到添加页面\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/meeting/edit'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 确认选择\n\t\t\tconfirmSelection() {\n\t\t\t\tconsole.log('确认选择');\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '操作成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t\t// 返回上一页\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t}, 1500);\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n.container {\n\tflex: 1; \n\tdisplay: flex;\n\tflex-direction: column;\n\t/* Add padding to the bottom to prevent content from being hidden by the fixed button */\n\tpadding-bottom: 160rpx; /* Increased padding to accommodate the button and safe area */\n}\n\n.content {\n\tpadding: 20rpx;\n\tflex: 1; \n}\n\n/* 会议室列表样式 */\n.meeting-list {\n\t// No specific styles needed here for now\n}\n\n.meeting-item {\n\tbackground-color: #ffffff;\n\tborder-radius: 16rpx; // More rounded corners\n\toverflow: hidden;\n\tbox-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.08); // Softer shadow\n\tmargin-bottom: 30rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.meeting-header {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 20rpx 30rpx;\n\tborder-bottom: 1rpx solid #f0f0f0; // Light border for separation\n}\n\n.header-icon {\n\twidth: 40rpx;\n\theight: 40rpx;\n\tmargin-right: 15rpx;\n}\n\n.meeting-name-header {\n\tfont-size: 30rpx; // Slightly smaller to match UI\n\tfont-weight: 500; // Medium weight\n\tcolor: #333333;\n}\n\n.meeting-body {\n\tdisplay: flex;\n\tflex-direction: row;\n\tpadding: 30rpx;\n\talign-items: center; // Vertically align items in the body\n}\n\n.meeting-icon-container {\n\tposition: relative;\n\tmargin-right: 30rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.status-dot-outer {\n\twidth: 30rpx; // Larger outer circle\n\theight: 30rpx;\n\tborder-radius: 15rpx;\n\tbackground-color: #e0e0e0; // Light grey for inactive\n\tposition: absolute;\n\ttop: -5rpx; // Position slightly above the icon\n\tleft: -15rpx; // Position to the left of the icon\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.status-dot-outer.active-outer {\n\tbackground-color: #8B0000; // Dark red for active\n}\n\n.status-dot-inner {\n\twidth: 16rpx; // Inner dot\n\theight: 16rpx;\n\tborder-radius: 8rpx;\n\tbackground-color: #8B0000; // Dark red for active dot\n}\n\n.status-dot-inner.active {\n\tbackground-color: #8B0000;\n}\n\n// For inactive state, the outer circle is grey, inner can be white or transparent\n.status-dot-inner:not(.active) {\n\tbackground-color: #FFFFFF; \n}\n\n.main-icon-bg {\n\twidth: 100rpx; // Slightly smaller icon background\n\theight: 100rpx;\n\tborder-radius: 12rpx; // More rounded\n\tbackground-color: #8B0000; // Dark red background\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.main-icon {\n\twidth: 60rpx;\n\theight: 60rpx;\n}\n\n.meeting-details {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.spec-row {\n\tdisplay: flex;\n\tflex-direction: row;\n\tmargin-bottom: 8rpx; // Increased spacing\n\talign-items: center;\n}\n\n.spec-label {\n\tfont-size: 26rpx; // Slightly larger label\n\tcolor: #555555; // Darker grey for labels\n\twidth: 90rpx; // Fixed width for alignment\n}\n\n.spec-value {\n\tfont-size: 26rpx;\n\tcolor: #333333; // Black for values\n\tflex: 1;\n}\n\n/* 操作按钮样式 */\n.meeting-actions {\n\tpadding: 20rpx 30rpx;\n\tdisplay: flex;\n\tjustify-content: flex-end; // Align buttons to the right\n\talign-items: center;\n\tborder-top: 1rpx solid #f0f0f0; // Light border for separation\n}\n\n.action-button {\n\tpadding: 0 25rpx; // Adjusted padding\n\tborder-radius: 8rpx;\n\tmargin-left: 15rpx; // Reduced margin\n\theight: 56rpx; // Slightly smaller height\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 24rpx; // Consistent font size\n\tbox-shadow: 0 2rpx 5rpx rgba(0,0,0,0.1);\n}\n\n.action-button text {\n\tfont-weight: 500;\n}\n\n.action-button.unbind {\n\tbackground-color: #FFFFFF;\n\tborder: 1rpx solid #cccccc; // Grey border\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.action-button.unbind text {\n\tcolor: #555555; // Dark grey text\n}\n\n.action-icon {\n\twidth: 28rpx; // Adjusted icon size\n\theight: 28rpx;\n\tmargin-right: 8rpx;\n}\n\n.action-button.bind {\n\tbackground-color: #8B0000; // Dark red\n\tcolor: #FFFFFF;\n}\n\n.action-button.bind text {\n\tcolor: #FFFFFF;\n}\n\n.action-button.delete {\n\tbackground-color: #FFFFFF; // White background\n\tborder: 1rpx solid #8B0000; // Dark red border\n}\n\n.action-button.delete text {\n\tcolor: #8B0000; // Dark red text\n}\n\n.action-button.edit {\n\tbackground-color: #8B0000; // Dark red\n\tcolor: #FFFFFF;\n}\n\n.action-button.edit text {\n\tcolor: #FFFFFF;\n}\n\n/* 添加按钮样式 */\n.add-button {\n\tposition: fixed;\n\tright: 40rpx;\n\tbottom: 200rpx; // Adjusted position to be above confirm button\n\twidth: 100rpx;\n\theight: 100rpx;\n\tborder-radius: 50rpx;\n\tbackground-color: #8B0000; // Dark red\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tbox-shadow: 0 6rpx 15rpx rgba(139, 0, 0, 0.35); // Stronger shadow\n\tz-index: 1000;\n}\n\n.add-icon {\n\tfont-size: 50rpx; // Slightly smaller icon\n\tcolor: #ffffff;\n\tfont-weight: bold;\n}\n\n \n \n/* New styles for the fixed bottom button */\n.confirm-button-container {\n\tposition: fixed;\n\tbottom: 0; /* Stick to the very bottom */\n\tleft: 0;\n\tright: 0;\n\twidth: 100%;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tpadding: 20rpx 0 env(safe-area-inset-bottom); /* Add padding and account for safe area */\n \tz-index: 1000; /* Ensure it's above other content */\n}\n\n.confirm-button-wrapper {\n\tposition: relative;\n\twidth: 80%; /* Adjust as needed */\n\tmax-width: 600rpx;\n}\n\n.confirm-button-bg {\n\twidth: 100%;\n\theight: 88rpx; /* Adjust based on your image aspect ratio or desired height */\n\tdisplay: block;\n\tborder-radius: 44rpx; /* Optional: if you want rounded corners for the image itself */\n}\n\n.confirm-button {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tcolor: white;\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n}\n\n.add-button {\n\tposition: fixed;\n\tright: 40rpx;\n\t/* Adjust bottom to be above the new confirm button height + padding + safe area */\n\tbottom: calc(88rpx + 40rpx + env(safe-area-inset-bottom) + 30rpx); \n\twidth: 100rpx;\n\theight: 100rpx;\n\tborder-radius: 50rpx;\n\tbackground-color: #8B0000; \n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tbox-shadow: 0 6rpx 15rpx rgba(139, 0, 0, 0.35); \n\tz-index: 1001; /* Ensure it's above the confirm button container */\n}\n\n.add-icon {\n\tfont-size: 50rpx;\n\tline-height: 50rpx;\n}\n\n/* Ensure the main content area has enough padding at the bottom \n   so it doesn't get obscured by the fixed button container */\n.meeting-list {\n\t/* padding-bottom: 120rpx; */ /* Moved to .container for overall page structure */\n}\n\n/* 绑定弹窗样式 */\n.bind-modal-overlay {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground-color: rgba(0, 0, 0, 0.5);\n\tz-index: 2000;\n\tdisplay: flex;\n\talign-items: flex-end;\n\tjustify-content: center;\n}\n\n.bind-modal {\n\twidth: 100%;\n\tmax-width: 750rpx;\n\tbackground-color: #ffffff;\n\tborder-radius: 20rpx 20rpx 0 0;\n\tpadding: 40rpx;\n\tbox-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);\n\ttransform: translateY(100%);\n\ttransition: transform 0.3s ease-out;\n\tmax-height: 80vh;\n\toverflow-y: auto;\n}\n\n.bind-modal.show {\n\ttransform: translateY(0);\n}\n\n.bind-modal-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 30rpx;\n\tpadding-bottom: 20rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.bind-modal-title {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\tcolor: #333333;\n}\n\n.bind-modal-close {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder-radius: 30rpx;\n\tbackground-color: #f5f5f5;\n}\n\n.bind-modal-close text {\n\tfont-size: 40rpx;\n\tcolor: #666666;\n\tline-height: 1;\n}\n\n.bind-modal-content {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.bind-info {\n\ttext-align: center;\n\tmargin-bottom: 30rpx;\n}\n\n.bind-info-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333333;\n\tmargin-bottom: 15rpx;\n\tdisplay: block;\n}\n\n.bind-info-desc {\n\tfont-size: 28rpx;\n\tcolor: #666666;\n\tdisplay: block;\n}\n\n/* 网关列表样式 */\n.gateway-list {\n\tmargin-bottom: 40rpx;\n\tborder: 1rpx solid #e0e0e0;\n\tborder-radius: 12rpx;\n\toverflow: hidden;\n\tmax-height: 400rpx;\n\toverflow-y: auto;\n}\n\n.gateway-header {\n\tdisplay: flex;\n\tbackground-color: #f8f9fa;\n\tpadding: 20rpx 0;\n\tborder-bottom: 1rpx solid #e0e0e0;\n\tposition: sticky;\n\ttop: 0;\n\tz-index: 10;\n}\n\n.gateway-item {\n\tdisplay: flex;\n\tpadding: 24rpx 0;\n\tborder-bottom: 1rpx solid #f0f0f0;\n\ttransition: background-color 0.2s ease;\n}\n\n.gateway-item:last-child {\n\tborder-bottom: none;\n}\n\n.gateway-item:active {\n\tbackground-color: #f8f9fa;\n}\n\n.header-cell {\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tcolor: #666666;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.header-cell:first-child {\n\tflex: 0 0 120rpx;\n}\n\n.header-cell:nth-child(2) {\n\tflex: 1;\n}\n\n.header-cell:nth-child(3) {\n\tflex: 1;\n}\n\n.gateway-cell {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 26rpx;\n\tcolor: #333333;\n}\n\n.radio-cell {\n\tflex: 0 0 120rpx;\n}\n\n.name-cell {\n\tflex: 1;\n\ttext-align: center;\n}\n\n.mac-cell {\n\tflex: 1;\n\ttext-align: center;\n\tfont-family: monospace;\n}\n\n.bind-actions {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tgap: 20rpx;\n}\n\n.bind-action-btn {\n\tflex: 1;\n\theight: 80rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder-radius: 40rpx;\n\tfont-size: 30rpx;\n\tfont-weight: bold;\n\ttransition: all 0.2s ease;\n}\n\n.bind-action-btn.cancel {\n\tbackground-color: #f5f5f5;\n\tcolor: #666666;\n\tborder: 1rpx solid #e0e0e0;\n}\n\n.bind-action-btn.cancel:active {\n\tbackground-color: #e8e8e8;\n}\n\n.bind-action-btn.confirm {\n\tbackground-color: #8B0000;\n\tcolor: #ffffff;\n}\n\n.bind-action-btn.confirm:active {\n\tbackground-color: #6B0000;\n}\n\n.bind-action-btn text {\n\tfont-size: 30rpx;\n\tfont-weight: bold;\n}\n</style>", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751873716609\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}