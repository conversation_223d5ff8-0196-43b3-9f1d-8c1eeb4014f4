<view class="data-v-0c772d04"><view data-event-opts="{{[['tap',[['maskClick',['$event']]]]]}}" class="{{['vol-tree-mask','data-v-0c772d04',(showTree)?'show':'']}}" bindtap="__e"></view><view class="{{['vol-tree-container','data-v-0c772d04',(showTree)?'show':'']}}"><block wx:if="{{inited}}"><view style="display:flex;flex-direction:column;height:100%;" class="data-v-0c772d04"><view class="vol-tree-header data-v-0c772d04"><view class="vol-tree-header-title data-v-0c772d04">{{title}}</view><view class="vol-tree-header-confirm data-v-0c772d04" hover-class="hover-c" data-event-opts="{{[['tap',[['confirm',['$event']]]]]}}" bindtap="__e">确定</view></view><view style="flex:1;height:0;overflow-x:scroll;" class="data-v-0c772d04"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['vol-tree-item','data-v-0c772d04',(item.$orig.show||item.$orig.lv===0)?'show':'',(item.$orig.checked)?'checked':'']}}"><view data-event-opts="{{[['tap',[['treeItemClick',['$0',index],[[['treeList','',index]]]]]]]}}" class="vol-tree-item-child data-v-0c772d04" catchtap="__e"><view class="vol-tree-item-child-label data-v-0c772d04" style="{{'margin-left:'+(item.$orig.lv*30+'rpx')+';'}}"><image class="tree-left-icon data-v-0c772d04" src="{{item.m0}}"></image>{{''+item.$orig.name+''}}</view><view class="vol-tree-item-child-check data-v-0c772d04"><block wx:if="{{item.$orig.checked}}"><u-icon vue-id="{{'fb2216fc-1-'+index}}" name="checkbox-mark" size="16" color="#2d92ff" class="data-v-0c772d04" bind:__l="__l"></u-icon></block></view></view></view></block></view></view></block></view></view>