<view class="container data-v-80842834"><view class="user-info data-v-80842834"><image class="u-img data-v-80842834" src="{{userInfo.img}}" data-event-opts="{{[['error',[['onHeaderError']]]]}}" binderror="__e"></image><view class="u-text data-v-80842834"><view class="username data-v-80842834">{{userInfo.userName}}</view><view class="small-text data-v-80842834">{{'欢迎使用,'+$root.m0+''}}</view></view><view data-event-opts="{{[['tap',[['toUserInfo',['$event']]]]]}}" class="u-icon-setting data-v-80842834" bindtap="__e"><u-icon vue-id="380011e0-1" name="arrow-right" color="rgb(227 227 227)" size="18" class="data-v-80842834" bind:__l="__l"></u-icon></view></view><view class="u-menu-list data-v-80842834"><block wx:for="{{menu}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['itemClick',['$0'],[[['menu','',index]]]]]]]}}" class="u-menu-item data-v-80842834" bindtap="__e"><view class="u-menu-icon data-v-80842834"><image style="width:36rpx;height:36rpx;" src="{{item.icon}}" class="data-v-80842834"></image></view><view class="u-menu-text data-v-80842834">{{item.name}}</view><view class="u-menu-icon-rigth data-v-80842834"><u-icon vue-id="{{'380011e0-2-'+index}}" name="arrow-right" color="rgb(231 231 231)" size="15" class="data-v-80842834" bind:__l="__l"></u-icon></view></view></block></view></view>