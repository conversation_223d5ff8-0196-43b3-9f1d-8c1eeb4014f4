<template>
	<view class="container">
		<!-- 主内容区域 -->
		<view class="content">
			<!-- 会议室列表 -->
			<view class="meeting-list">
				<!-- 会议室项 -->
				<view class="meeting-item" v-for="(item, index) in meetingRooms" :key="index">
					<view class="meeting-header">
						<image class="header-icon" src="/static/images/logo_icon_text.png" mode="aspectFit"></image>
						<text class="meeting-name-header">{{ item.name }}</text>
					</view>
					<view class="meeting-body">
						<view class="meeting-icon-container">
							<view class="status-dot-outer" :class="{'active-outer': item.status === 'active'}">
								<view class="status-dot-inner" :class="{active: item.status === 'active'}"></view>
							</view>
							<view class="main-icon-bg">
								<image class="main-icon" src="/static/images/logo_icon.png" mode="aspectFit"></image>
							</view>
						</view>
						<view class="meeting-details">
							<view class="spec-row">
								<text class="spec-label">型号：</text>
								<text class="spec-value">{{ item.model || '未绑定' }}</text>
							</view>
							<view class="spec-row">
								<text class="spec-label">版本：</text>
								<text class="spec-value">{{ item.version || '未绑定' }}</text>
							</view>
							<view class="spec-row">
								<text class="spec-label">类型：</text>
								<text class="spec-value">{{ item.type || '未绑定' }}</text>
							</view>
							<view class="spec-row">
								<text class="spec-label">编号：</text>
								<text class="spec-value">{{ item.code || '未绑定' }}</text>
							</view>
							<view class="spec-row">
								<text class="spec-label">频道：</text>
								<text class="spec-value">{{ item.channel || '未绑定' }}</text>
							</view>
						</view>
					</view>
					<view class="meeting-actions">
						<view class="action-button unbind" @click="unbindDevice(item)">
							<image class="action-icon" src="/static/images/gateway_unbinding.png" mode="aspectFit"></image>
							<text>未绑定</text>
						</view>
						<view class="action-button bind" @click="showBindModal(item)">
							<text>绑定</text>
						</view>
						<view class="action-button delete" @click="deleteRoom(item)">
							<text>删除</text>
						</view>
						<view class="action-button edit" @click="editRoom(item)">
							<text>编辑</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 添加按钮 -->
		<view class="add-button" @click="addRoom">
			<text class="add-icon">+</text>
		</view>
		
		<!-- Bottom Confirm Button -->
		<view class="confirm-button-container">
			<view class="confirm-button-wrapper">
				<image src="http://14.103.146.84:8890/i/2025/06/12/btn.png" class="confirm-button-bg" mode="aspectFill"></image>
				<view class="confirm-button" @click="confirmSelection">
					<text>确定</text>
				</view>
			</view>
		</view>
		
		<!-- 绑定弹窗 -->
		<view class="bind-modal-overlay" v-if="showBindPopup" @click="hideBindModal">
			<view class="bind-modal" :class="{show: bindModalVisible}" @click.stop="stopPropagation">
				<view class="bind-modal-header">
					<text class="bind-modal-title">绑定设备</text>
					<view class="bind-modal-close" @click="hideBindModal">
						<text>×</text>
					</view>
				</view>
				<view class="bind-modal-content">
					<view class="bind-info">
						<text class="bind-info-title">{{ currentBindItem && currentBindItem.name }}</text>
						<text class="bind-info-desc">请选择要绑定的网关设备</text>
					</view>
					
					<!-- 网关列表 -->
					<view class="gateway-list">
						<view class="gateway-header">
							<text class="header-cell">选择</text>
							<text class="header-cell">网关名称</text>
							<text class="header-cell">网关MAC</text>
						</view>
						<view class="gateway-item" v-for="(gateway, index) in gatewayList" :key="index" @click="selectGateway(gateway)">
							<view class="gateway-cell radio-cell">
								<radio :checked="selectedGateway && selectedGateway.id === gateway.id" color="#8B0000" />
							</view>
							<view class="gateway-cell name-cell">
								<text>{{ gateway.name }}</text>
							</view>
							<view class="gateway-cell mac-cell">
								<text>{{ gateway.mac }}</text>
							</view>
						</view>
					</view>
					
					<view class="bind-actions">
						<view class="bind-action-btn cancel" @click="hideBindModal">
							<text>取消</text>
						</view>
						<view class="bind-action-btn confirm" @click="confirmBind">
							<text>确认绑定</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				meetingRooms: [
					{
						id: '1',
						name: '1号会议室',
						model: '未绑定',
						version: '未绑定',
						type: '未绑定',
						code: '未绑定',
						channel: '未绑定',
						status: 'active'
					},
					{
						id: '2',
						name: '121212',
						model: '未绑定',
						version: '未绑定',
						type: '未绑定',
						code: '未绑定',
						channel: '未绑定',
						status: 'inactive'
					}
				],
				selectedRooms: [],
				showBindPopup: false,
				bindModalVisible: false,
				currentBindItem: null,
				selectedGateway: null,
				gatewayList: [
					{
						id: 1,
						name: '网关设备-001',
						mac: 'AA:BB:CC:DD:EE:01'
					},
					{
						id: 2,
						name: '网关设备-002',
						mac: 'AA:BB:CC:DD:EE:02'
					},
					{
						id: 3,
						name: '网关设备-003',
						mac: 'AA:BB:CC:DD:EE:03'
					},
					{
						id: 4,
						name: '网关设备-003',
						mac: 'AA:BB:CC:DD:EE:03'
					},
					{
						id: 5,
						name: '网关设备-003',
						mac: 'AA:BB:CC:DD:EE:03'
					},
					{
						id: 6,
						name: '网关设备-003',
						mac: 'AA:BB:CC:DD:EE:03'
					},
					{
						id: 7,
						name: '网关设备-003',
						mac: 'AA:BB:CC:DD:EE:03'
					}
				]
			};
		},
		onLoad() {
			// 加载会议室列表数据
			this.loadMeetingRooms();
		},
		methods: {
			// 阻止事件冒泡
			stopPropagation() {
				// 空函数，用于阻止事件冒泡
			},

			// 加载会议室列表
			loadMeetingRooms() {
				// 这里应该调用API获取会议室列表
				// 目前使用模拟数据
			},
			
			// 显示绑定弹窗
			showBindModal(item) {
				this.currentBindItem = item;
				this.selectedGateway = null; // 重置选中的网关
				this.bindModalVisible = true;
				this.$nextTick(() => {
					this.showBindPopup = true;
				});
			},
			hideBindModal() {
				this.showBindPopup = false;
				setTimeout(() => {
					this.bindModalVisible = false;
					this.currentBindItem = null;
					this.selectedGateway = null;
				}, 300);
			},
			selectGateway(gateway) {
				this.selectedGateway = gateway;
			},
			confirmBind() {
				if (!this.selectedGateway) {
					uni.showToast({
						title: '请选择要绑定的网关设备',
						icon: 'none'
					});
					return;
				}
				
				// 执行绑定逻辑
				uni.showLoading({
					title: '绑定中...'
				});
				
				// 模拟绑定请求
				setTimeout(() => {
					uni.hideLoading();
					uni.showToast({
						title: `${this.currentBindItem.name} 已成功绑定到 ${this.selectedGateway.name}`,
						icon: 'success',
						duration: 2000
					});
					this.hideBindModal();
				}, 1500);
			},
			
			// 绑定设备（保留原方法以防其他地方调用）
			bindDevice(item) {
				this.showBindModal(item);
			},
			
			// 解绑设备
			unbindDevice(item) {
				console.log('解绑设备:', item);
				uni.showToast({
					title: '已切换为未绑定状态',
					icon: 'none'
				});
			},
			
			// 删除会议室
			deleteRoom(item) {
				console.log('删除会议室:', item);
				uni.showModal({
					title: '确认删除',
					content: `确定要删除${item.name}吗？`,
					success: (res) => {
						if (res.confirm) {
							// 从列表中移除
							this.meetingRooms = this.meetingRooms.filter(room => room.id !== item.id);
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
						}
					}
				});
			},
			
			// 编辑会议室
			editRoom(item) {
				console.log('编辑会议室:', item);
				// 跳转到编辑页面
				uni.navigateTo({
					url: `/pages/meeting/edit?id=${item.id}`
				});
			},
			
			// 添加会议室
			addRoom() {
				console.log('添加会议室');
				// 跳转到添加页面
				uni.navigateTo({
					url: '/pages/meeting/edit'
				});
			},
			
			// 确认选择
			confirmSelection() {
				console.log('确认选择');
				uni.showToast({
					title: '操作成功',
					icon: 'success'
				});
				// 返回上一页
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		}
	}
</script>

<style lang="scss">
.container {
	flex: 1; 
	display: flex;
	flex-direction: column;
	/* Add padding to the bottom to prevent content from being hidden by the fixed button */
	padding-bottom: 160rpx; /* Increased padding to accommodate the button and safe area */
}

.content {
	padding: 20rpx;
	flex: 1; 
}

/* 会议室列表样式 */
.meeting-list {
	// No specific styles needed here for now
}

.meeting-item {
	background-color: #ffffff;
	border-radius: 16rpx; // More rounded corners
	overflow: hidden;
	box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.08); // Softer shadow
	margin-bottom: 30rpx;
	display: flex;
	flex-direction: column;
}

.meeting-header {
	display: flex;
	align-items: center;
	padding: 20rpx 30rpx;
	border-bottom: 1rpx solid #f0f0f0; // Light border for separation
}

.header-icon {
	width: 40rpx;
	height: 40rpx;
	margin-right: 15rpx;
}

.meeting-name-header {
	font-size: 30rpx; // Slightly smaller to match UI
	font-weight: 500; // Medium weight
	color: #333333;
}

.meeting-body {
	display: flex;
	flex-direction: row;
	padding: 30rpx;
	align-items: center; // Vertically align items in the body
}

.meeting-icon-container {
	position: relative;
	margin-right: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.status-dot-outer {
	width: 30rpx; // Larger outer circle
	height: 30rpx;
	border-radius: 15rpx;
	background-color: #e0e0e0; // Light grey for inactive
	position: absolute;
	top: -5rpx; // Position slightly above the icon
	left: -15rpx; // Position to the left of the icon
	display: flex;
	align-items: center;
	justify-content: center;
}

.status-dot-outer.active-outer {
	background-color: #8B0000; // Dark red for active
}

.status-dot-inner {
	width: 16rpx; // Inner dot
	height: 16rpx;
	border-radius: 8rpx;
	background-color: #8B0000; // Dark red for active dot
}

.status-dot-inner.active {
	background-color: #8B0000;
}

// For inactive state, the outer circle is grey, inner can be white or transparent
.status-dot-inner:not(.active) {
	background-color: #FFFFFF; 
}

.main-icon-bg {
	width: 100rpx; // Slightly smaller icon background
	height: 100rpx;
	border-radius: 12rpx; // More rounded
	background-color: #8B0000; // Dark red background
	display: flex;
	align-items: center;
	justify-content: center;
}

.main-icon {
	width: 60rpx;
	height: 60rpx;
}

.meeting-details {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.spec-row {
	display: flex;
	flex-direction: row;
	margin-bottom: 8rpx; // Increased spacing
	align-items: center;
}

.spec-label {
	font-size: 26rpx; // Slightly larger label
	color: #555555; // Darker grey for labels
	width: 90rpx; // Fixed width for alignment
}

.spec-value {
	font-size: 26rpx;
	color: #333333; // Black for values
	flex: 1;
}

/* 操作按钮样式 */
.meeting-actions {
	padding: 20rpx 30rpx;
	display: flex;
	justify-content: flex-end; // Align buttons to the right
	align-items: center;
	border-top: 1rpx solid #f0f0f0; // Light border for separation
}

.action-button {
	padding: 0 25rpx; // Adjusted padding
	border-radius: 8rpx;
	margin-left: 15rpx; // Reduced margin
	height: 56rpx; // Slightly smaller height
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx; // Consistent font size
	box-shadow: 0 2rpx 5rpx rgba(0,0,0,0.1);
}

.action-button text {
	font-weight: 500;
}

.action-button.unbind {
	background-color: #FFFFFF;
	border: 1rpx solid #cccccc; // Grey border
	display: flex;
	align-items: center;
}

.action-button.unbind text {
	color: #555555; // Dark grey text
}

.action-icon {
	width: 28rpx; // Adjusted icon size
	height: 28rpx;
	margin-right: 8rpx;
}

.action-button.bind {
	background-color: #8B0000; // Dark red
	color: #FFFFFF;
}

.action-button.bind text {
	color: #FFFFFF;
}

.action-button.delete {
	background-color: #FFFFFF; // White background
	border: 1rpx solid #8B0000; // Dark red border
}

.action-button.delete text {
	color: #8B0000; // Dark red text
}

.action-button.edit {
	background-color: #8B0000; // Dark red
	color: #FFFFFF;
}

.action-button.edit text {
	color: #FFFFFF;
}

/* 添加按钮样式 */
.add-button {
	position: fixed;
	right: 40rpx;
	bottom: 200rpx; // Adjusted position to be above confirm button
	width: 100rpx;
	height: 100rpx;
	border-radius: 50rpx;
	background-color: #8B0000; // Dark red
	display: flex;
	justify-content: center;
	align-items: center;
	box-shadow: 0 6rpx 15rpx rgba(139, 0, 0, 0.35); // Stronger shadow
	z-index: 1000;
}

.add-icon {
	font-size: 50rpx; // Slightly smaller icon
	color: #ffffff;
	font-weight: bold;
}

 
 
/* New styles for the fixed bottom button */
.confirm-button-container {
	position: fixed;
	bottom: 0; /* Stick to the very bottom */
	left: 0;
	right: 0;
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 20rpx 0 env(safe-area-inset-bottom); /* Add padding and account for safe area */
 	z-index: 1000; /* Ensure it's above other content */
}

.confirm-button-wrapper {
	position: relative;
	width: 80%; /* Adjust as needed */
	max-width: 600rpx;
}

.confirm-button-bg {
	width: 100%;
	height: 88rpx; /* Adjust based on your image aspect ratio or desired height */
	display: block;
	border-radius: 44rpx; /* Optional: if you want rounded corners for the image itself */
}

.confirm-button {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	justify-content: center;
	align-items: center;
	color: white;
	font-size: 32rpx;
	font-weight: bold;
}

.add-button {
	position: fixed;
	right: 40rpx;
	/* Adjust bottom to be above the new confirm button height + padding + safe area */
	bottom: calc(88rpx + 40rpx + env(safe-area-inset-bottom) + 30rpx); 
	width: 100rpx;
	height: 100rpx;
	border-radius: 50rpx;
	background-color: #8B0000; 
	display: flex;
	justify-content: center;
	align-items: center;
	box-shadow: 0 6rpx 15rpx rgba(139, 0, 0, 0.35); 
	z-index: 1001; /* Ensure it's above the confirm button container */
}

.add-icon {
	font-size: 50rpx;
	line-height: 50rpx;
}

/* Ensure the main content area has enough padding at the bottom 
   so it doesn't get obscured by the fixed button container */
.meeting-list {
	/* padding-bottom: 120rpx; */ /* Moved to .container for overall page structure */
}

/* 绑定弹窗样式 */
.bind-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 2000;
	display: flex;
	align-items: flex-end;
	justify-content: center;
}

.bind-modal {
	width: 100%;
	max-width: 750rpx;
	background-color: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
	padding: 40rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
	transform: translateY(100%);
	transition: transform 0.3s ease-out;
	max-height: 80vh;
	overflow-y: auto;
}

.bind-modal.show {
	transform: translateY(0);
}

.bind-modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.bind-modal-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333333;
}

.bind-modal-close {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 30rpx;
	background-color: #f5f5f5;
}

.bind-modal-close text {
	font-size: 40rpx;
	color: #666666;
	line-height: 1;
}

.bind-modal-content {
	display: flex;
	flex-direction: column;
}

.bind-info {
	text-align: center;
	margin-bottom: 30rpx;
}

.bind-info-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 15rpx;
	display: block;
}

.bind-info-desc {
	font-size: 28rpx;
	color: #666666;
	display: block;
}

/* 网关列表样式 */
.gateway-list {
	margin-bottom: 40rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 12rpx;
	overflow: hidden;
	max-height: 400rpx;
	overflow-y: auto;
}

.gateway-header {
	display: flex;
	background-color: #f8f9fa;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #e0e0e0;
	position: sticky;
	top: 0;
	z-index: 10;
}

.gateway-item {
	display: flex;
	padding: 24rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.2s ease;
}

.gateway-item:last-child {
	border-bottom: none;
}

.gateway-item:active {
	background-color: #f8f9fa;
}

.header-cell {
	font-size: 28rpx;
	font-weight: bold;
	color: #666666;
	display: flex;
	align-items: center;
	justify-content: center;
}

.header-cell:first-child {
	flex: 0 0 120rpx;
}

.header-cell:nth-child(2) {
	flex: 1;
}

.header-cell:nth-child(3) {
	flex: 1;
}

.gateway-cell {
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 26rpx;
	color: #333333;
}

.radio-cell {
	flex: 0 0 120rpx;
}

.name-cell {
	flex: 1;
	text-align: center;
}

.mac-cell {
	flex: 1;
	text-align: center;
	font-family: monospace;
}

.bind-actions {
	display: flex;
	justify-content: space-between;
	gap: 20rpx;
}

.bind-action-btn {
	flex: 1;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 40rpx;
	font-size: 30rpx;
	font-weight: bold;
	transition: all 0.2s ease;
}

.bind-action-btn.cancel {
	background-color: #f5f5f5;
	color: #666666;
	border: 1rpx solid #e0e0e0;
}

.bind-action-btn.cancel:active {
	background-color: #e8e8e8;
}

.bind-action-btn.confirm {
	background-color: #8B0000;
	color: #ffffff;
}

.bind-action-btn.confirm:active {
	background-color: #6B0000;
}

.bind-action-btn text {
	font-size: 30rpx;
	font-weight: bold;
}
</style>