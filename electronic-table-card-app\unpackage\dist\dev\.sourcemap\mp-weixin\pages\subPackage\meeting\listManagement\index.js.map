{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/listManagement/index.vue?5dc5", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/listManagement/index.vue?1398", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/listManagement/index.vue?0d3e", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/listManagement/index.vue?590b", "uni-app:///pages/subPackage/meeting/listManagement/index.vue", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/listManagement/index.vue?8c8b", "webpack:///D:/Project/电子桌牌/vol.uniapp/electronic-table-card-app/pages/subPackage/meeting/listManagement/index.vue?4ee6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "roomName", "has<PERSON><PERSON>ple", "showRoomModal", "roomList", "id", "name", "showPersonModal", "personModalType", "showDeskModal", "<PERSON><PERSON><PERSON>", "deskNumber", "title", "employeeId", "company", "selected", "deskDeviceList", "number", "gatewayId", "gatewayName", "status", "boundPersonId", "peopleList", "computed", "isAllSelected", "hasSelected", "availableDeskList", "onLoad", "methods", "stopPropagation", "loadPeopleData", "toggleSelectAll", "person", "togglePersonSelect", "add<PERSON><PERSON>", "console", "batchImport", "uni", "icon", "edit<PERSON><PERSON>", "deletePerson", "content", "success", "deleteSelected", "importExcel", "showDeskSelector", "selectDesk", "d", "showCancel", "closeDeskModal", "getStatusText", "updateDeskBinding", "desk", "showRoomSelector", "selectRoom", "closeRoomModal", "closePersonModal", "<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1BA;AAAA;AAAA;AAAA;AAAouB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCwLxvB;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QAAA;QACAL;QACAM;QACAL;QACAM;QACAC;QACAC;QACAC;MACA;MACA;MACAC,iBACA;QAAAX;QAAAY;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAhB;QAAAY;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAhB;QAAAY;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAhB;QAAAY;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAhB;QAAAY;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAhB;QAAAY;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAhB;QAAAY;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,EACA;MACAC,aACA;QACAjB;QACAM;QACAL;QACAM;QACAC;QACAC;QACAC;MACA;IAEA;EACA;EACAQ;IACAC;MACA;QAAA;MAAA;IACA;IACAC;MACA;QAAA;MAAA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;UACA;QACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IAAA,CACA;IAEA;IACAC;MACA;MACA;MACA;IAAA,CACA;IAEA;IACAC;MACA;MACA;QACAC;MACA;IACA;IAEA;IACAC;MACAD;IACA;IAEA;IACAE;MACAC;MACA;MACA;QACA9B;QACAM;QACAL;QACAM;QACAC;QACAC;QACAC;MACA;MACA;IACA;IAEA;IACAqB;MACAD;MACAE;QACAzB;QACA0B;MACA;IACA;IAEA;IACAC;MACAJ;MACA;MACA;MACA;MACA;QACA9B;QACAM;QACAL;QACAM;QACAC;QACAC;QACAC;MACA;MACA;QACA;UACA;QACA;MACA;MACA;IACA;IAEA;IACAyB;MAAA;MACAH;QACAzB;QACA6B;QACAC;UACA;YACA;cAAA;YAAA;YACA;YACA;cACA;YACA;YACAL;cACAzB;cACA0B;YACA;UACA;QACA;MACA;IACA;IAEA;IACAK;MAAA;MACA;QAAA;MAAA;MACA;QACAN;UACAzB;UACA0B;QACA;QACA;MACA;MAEAD;QACAzB;QACA6B;QACAC;UACA;YACA;cAAA;YAAA;YACA;YACA;cACA;YACA;YACAL;cACAzB;cACA0B;YACA;UACA;QACA;MACA;IACA;IAEA;IACAM;MACAT;MACAE;QACAzB;QACA0B;MACA;IACA;IAEA;IACAO;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACAT;UACAzB;UACA0B;QACA;QACA;MACA;;MAEA;MACA;QAAA,OACAS,kCACAA,wBACAA;MAAA,EACA;MAEA;QACAV;UACAzB;UACA6B;UACAO;QACA;QACA;MACA;MAEA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;IAEA;IACAC;MACA;QAAA;MAAA;MACA;QACA;UACA;UACAC;UACAA;QACA;UACA;UACAA;UACAA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACAjB;QACAzB;QACA0B;MACA;IACA;IAEA;IACAiB;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA,kEACA,+DACA;QACApB;UACAzB;UACA0B;QACA;QACA;MACA;MAEA;QACA;QACA;UACAjC;UAAA;UACAM;UACAL;UACAM;UACAC;UACAC;UACAC;QACA;QACA;QACA;;QAEA;QACA;UACA;QACA;QAEAsB;UACAzB;UACA0B;QACA;MACA;QACA;QACA;UAAA;QAAA;QACA;UACA;;UAEA;UACA;YACA;YACA;cACA;YACA;YACA;YACA;cACA;YACA;UACA;UAEA;YACAjC;YACAM;YACAL;YACAM;YACAC;YACAC;YACAC;UACA;UACAsB;YACAzB;YACA0B;UACA;QACA;MACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACljBA;AAAA;AAAA;AAAA;AAAu3C,CAAgB,4tCAAG,EAAC,C;;;;;;;;;;;ACA34C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/subPackage/meeting/listManagement/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/subPackage/meeting/listManagement/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=3931ceb8&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/subPackage/meeting/listManagement/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=3931ceb8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.showDeskModal\n    ? _vm.__map(_vm.availableDeskList, function (desk, __i0__) {\n        var $orig = _vm.__get_orig(desk)\n        var m0 = _vm.getStatusText(desk.status)\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<!-- 会议室信息头部 -->\n\t\t<view class=\"room-header\">\n\t\t\t<view class=\"room-info\" @click=\"showRoomSelector\">\n\t\t\t\t<text class=\"room-name\">会议室名称：{{ roomName }}</text>\n\t\t\t\t<!-- <text class=\"switch-hint\">点击切换</text> -->\n\t\t\t</view>\n\t\t\t<view class=\"select-all\" v-if=\"hasPeople\">\n\t\t\t\t<checkbox :checked=\"isAllSelected\" @click=\"toggleSelectAll\" color=\"#8B0000\" />\n\t\t\t\t<text class=\"select-all-text\">全选</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 无人员状态 -->\n\t\t<view class=\"empty-state\" v-if=\"!hasPeople\">\t\t\t\n\t\t\t<view class=\"empty-content\">\n\t\t\t\t<view class=\"action-button add-person\" @click=\"addPerson\">\n\t\t\t\t\t<image class=\"button-bg\" src=\"http://*************:8890/i/2025/06/12/btn.png\" mode=\"aspectFill\"></image>\n\t\t\t\t\t<text class=\"action-text\">新增人员</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"action-button batch-import\" @click=\"batchImport\">\n\t\t\t\t\t<image class=\"button-bg\" src=\"http://*************:8890/i/2025/06/12/btn.png\" mode=\"aspectFill\"></image>\n\t\t\t\t\t<text class=\"action-text\">批量导入人员</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 有人员状态 -->\n\t\t<view class=\"people-list\" v-if=\"hasPeople\">\n\t\t\t<!-- 人员项 -->\n\t\t\t<view class=\"person-item\" v-for=\"(person, index) in peopleList\" :key=\"index\">\n\t\t\t\t<view class=\"person-header\">\n\t\t\t\t\t<text class=\"desk-number\">桌牌编号：{{ person.deskNumber }}</text>\n\t\t\t\t\t<checkbox :checked=\"person.selected\" @click=\"togglePersonSelect(person)\" color=\"#8B0000\" />\n\t\t\t\t</view>\n\t\t\t\t<view class=\"person-details\">\n\t\t\t\t\t<view class=\"detail-row\">\n\t\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t\t<text class=\"detail-label\">姓名：</text>\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{ person.name }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t\t<text class=\"detail-label\">职称：</text>\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{ person.title }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"detail-row\">\n\t\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t\t<text class=\"detail-label\">工号：</text>\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{ person.employeeId }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t\t<text class=\"detail-label\">公司名称：</text>\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{ person.company }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"person-actions\">\n\t\t\t\t\t<view class=\"action-btn delete-btn\" @click=\"deletePerson(person)\">\n\t\t\t\t\t\t<text>删除人员</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-btn edit-btn\" @click=\"editPerson(person)\">\n\t\t\t\t\t\t<text>编辑人员</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 浮动按钮 -->\n\t\t<view class=\"floating-buttons\" v-if=\"hasPeople\">\n\t\t\t<!-- 添加按钮 -->\n\t\t\t<view class=\"float-btn add-btn\" @click=\"addPerson\">\n\t\t\t\t<text class=\"float-btn-text\">+</text>\n\t\t\t</view>\n\t\t\t<!-- Excel导入按钮 -->\n\t\t\t<view class=\"float-btn excel-btn\" @click=\"importExcel\">\n\t\t\t\t<text class=\"excel-text\">EXL</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 底部删除选中按钮 -->\n\t\t<view class=\"bottom-action\" v-if=\"hasPeople && hasSelected\">\n\t\t\t<view class=\"delete-selected-btn\" @click=\"deleteSelected\">\n\t\t\t\t<text>删除选中</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 桌牌选择弹窗 -->\n\t\t<view class=\"desk-modal\" v-if=\"showDeskModal\" @click=\"closeDeskModal\">\n\t\t\t<view class=\"desk-modal-content\" @click.stop=\"stopPropagation\">\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<text class=\"modal-title\">选择桌牌编号</text>\n\t\t\t\t\t<text class=\"close-btn\" @click=\"closeDeskModal\">×</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"desk-list\">\n\t\t\t\t\t<view \n\t\t\t\t\t\tv-for=\"desk in availableDeskList\" \n\t\t\t\t\t\t:key=\"desk.id\" \n\t\t\t\t\t\tclass=\"desk-option\" \n\t\t\t\t\t\t:class=\"{disabled: desk.status !== 'available'}\"\n\t\t\t\t\t\t@click=\"selectDesk(desk)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<view class=\"desk-info\">\n\t\t\t\t\t\t\t<text class=\"desk-number\">{{ desk.number }}</text>\n\t\t\t\t\t\t\t<text class=\"desk-gateway\">{{ desk.gatewayName }}</text>\n\t\t\t\t\t\t\t<text class=\"desk-status\" :class=\"desk.status\">{{ getStatusText(desk.status) }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"desk-check\" v-if=\"currentPerson.deskNumber === desk.number\">✓</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 会议室选择弹窗 -->\n\t\t<view class=\"room-modal\" v-if=\"showRoomModal\" @click=\"closeRoomModal\">\n\t\t\t<view class=\"room-modal-content\" @click.stop=\"stopPropagation\">\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<text class=\"modal-title\">选择会议室</text>\n\t\t\t\t\t<view class=\"close-btn\" @click=\"closeRoomModal\">\n\t\t\t\t\t\t<text>×</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"room-list\">\n\t\t\t\t\t<view class=\"room-option\" v-for=\"room in roomList\" :key=\"room.id\" @click=\"selectRoom(room)\">\n\t\t\t\t\t\t<text class=\"room-option-name\">{{ room.name }}</text>\n\t\t\t\t\t\t<view class=\"room-option-check\" v-if=\"room.name === roomName\">\n\t\t\t\t\t\t\t<text>✓</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 新增/编辑人员弹窗 -->\n\t\t<view class=\"person-modal\" v-if=\"showPersonModal\" @click=\"closePersonModal\">\n\t\t\t<view class=\"person-modal-content\" @click.stop=\"stopPropagation\">\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<text class=\"modal-title\">{{ personModalType === 'add' ? '新增人员' : '编辑人员' }}</text>\n\t\t\t\t\t<view class=\"close-btn\" @click=\"closePersonModal\">\n\t\t\t\t\t\t<text>×</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"person-form\">\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">桌牌编号：</text>\n\t\t\t\t\t\t<view class=\"form-select\" @click=\"showDeskSelector\">\n\t\t\t\t\t\t\t<text class=\"select-text\" :class=\"{placeholder: !currentPerson.deskNumber}\">\n\t\t\t\t\t\t\t\t{{ currentPerson.deskNumber || '请选择桌牌编号' }}\n\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t<text class=\"select-arrow\">▼</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">姓名</text>\n\t\t\t\t\t\t<input class=\"form-input\" v-model=\"currentPerson.name\" placeholder=\"例如：姓名\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">工号</text>\n\t\t\t\t\t\t<input class=\"form-input\" v-model=\"currentPerson.employeeId\" placeholder=\"例如：编号\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">职称</text>\n\t\t\t\t\t\t<input class=\"form-input\" v-model=\"currentPerson.title\" placeholder=\"例如：职称\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">公司名称</text>\n\t\t\t\t\t\t<input class=\"form-input\" v-model=\"currentPerson.company\" placeholder=\"例如：公司\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"modal-actions\">\n\t\t\t\t\t<view class=\"action-btn cancel-btn\" @click=\"closePersonModal\">\n\t\t\t\t\t\t<text>取消</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-btn confirm-btn\" @click=\"confirmPerson\">\n\t\t\t\t\t\t<text>确定</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\troomName: '1号会议室',\n\t\t\t\thasPeople: true, // 控制显示状态，true显示人员列表，false显示空状态\n\t\t\t\tshowRoomModal: false, // 控制会议室选择弹窗\n\t\t\t\troomList: [ // 会议室列表\n\t\t\t\t\t{ id: 1, name: '1号会议室' },\n\t\t\t\t\t{ id: 2, name: '2号会议室' },\n\t\t\t\t\t{ id: 3, name: '3号会议室' },\n\t\t\t\t\t{ id: 4, name: '4号会议室' },\n\t\t\t\t\t{ id: 5, name: '5号会议室' },\n\t\t\t\t\t{ id: 6, name: '6号会议室' },\n\t\t\t\t\t{ id: 7, name: '7号会议室' },\n\t\t\t\t\t{ id: 8, name: '8号会议室' }\n\t\t\t\t],\n\t\t\t\tshowPersonModal: false, // 控制新增/编辑人员弹窗\n\t\t\t\tpersonModalType: 'add', // 弹窗类型：add新增，edit编辑\n\t\t\t\tshowDeskModal: false, // 控制桌牌选择弹窗\n\t\t\t\tcurrentPerson: { // 当前编辑的人员信息\n\t\t\t\t\tid: null,\n\t\t\t\t\tdeskNumber: '',\n\t\t\t\t\tname: '',\n\t\t\t\t\ttitle: '',\n\t\t\t\t\temployeeId: '',\n\t\t\t\t\tcompany: '',\n\t\t\t\t\tselected: false\n\t\t\t\t},\n\t\t\t\t// 桌牌设备列表（模拟数据，实际应从API获取）\n\t\t\t\tdeskDeviceList: [\n\t\t\t\t\t{ id: 1, number: '001', gatewayId: 'GW001', gatewayName: '网关1', status: 'available', boundPersonId: null },\n\t\t\t\t\t{ id: 2, number: '002', gatewayId: 'GW001', gatewayName: '网关1', status: 'bound', boundPersonId: 1 },\n\t\t\t\t\t{ id: 3, number: '003', gatewayId: 'GW001', gatewayName: '网关1', status: 'available', boundPersonId: null },\n\t\t\t\t\t{ id: 4, number: '004', gatewayId: 'GW002', gatewayName: '网关2', status: 'available', boundPersonId: null },\n\t\t\t\t\t{ id: 5, number: '005', gatewayId: 'GW002', gatewayName: '网关2', status: 'available', boundPersonId: null },\n\t\t\t\t\t{ id: 6, number: '006', gatewayId: 'GW003', gatewayName: '网关3', status: 'offline', boundPersonId: null },\n\t\t\t\t\t{ id: 7, number: '007', gatewayId: 'GW003', gatewayName: '网关3', status: 'available', boundPersonId: null }\n\t\t\t\t],\n\t\t\t\tpeopleList: [\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 1,\n\t\t\t\t\t\tdeskNumber: '12121',\n\t\t\t\t\t\tname: '21212',\n\t\t\t\t\t\ttitle: '2121',\n\t\t\t\t\t\temployeeId: '2121',\n\t\t\t\t\t\tcompany: '2121',\n\t\t\t\t\t\tselected: false\n\t\t\t\t\t}\n\t\t\t\t]\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tisAllSelected() {\n\t\t\t\treturn this.peopleList.length > 0 && this.peopleList.every(person => person.selected);\n\t\t\t},\n\t\t\thasSelected() {\n\t\t\t\treturn this.peopleList.some(person => person.selected);\n\t\t\t},\n\t\t\t// 可用的桌牌列表（根据网关限制和绑定状态过滤）\n\t\t\tavailableDeskList() {\n\t\t\t\treturn this.deskDeviceList.filter(desk => {\n\t\t\t\t\t// 如果是编辑模式且当前桌牌已绑定给当前人员，则显示\n\t\t\t\t\tif (this.personModalType === 'edit' && desk.boundPersonId === this.currentPerson.id) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t\t// 只显示可用状态的桌牌\n\t\t\t\t\treturn desk.status === 'available';\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\t// 获取传递的会议室信息\n\t\t\tif (options.roomName) {\n\t\t\t\tthis.roomName = decodeURIComponent(options.roomName);\n\t\t\t}\n\t\t\t// 模拟数据加载\n\t\t\tthis.loadPeopleData();\n\t\t},\n\t\tmethods: {\n\t\t\t// 阻止事件冒泡\n\t\t\tstopPropagation() {\n\t\t\t\t// 空函数，用于阻止事件冒泡\n\t\t\t},\n\n\t\t\t// 加载人员数据\n\t\t\tloadPeopleData() {\n\t\t\t\t// 这里可以根据实际需求调用API\n\t\t\t\t// 如果没有人员数据，设置hasPeople为false\n\t\t\t\t// this.hasPeople = this.peopleList.length > 0;\n\t\t\t},\n\t\t\t\n\t\t\t// 切换全选状态\n\t\t\ttoggleSelectAll() {\n\t\t\t\tconst newState = !this.isAllSelected;\n\t\t\t\tthis.peopleList.forEach(person => {\n\t\t\t\t\tperson.selected = newState;\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 切换单个人员选择状态\n\t\t\ttogglePersonSelect(person) {\n\t\t\t\tperson.selected = !person.selected;\n\t\t\t},\n\t\t\t\n\t\t\t// 新增人员\n\t\t\taddPerson() {\n\t\t\t\tconsole.log('新增人员');\n\t\t\t\tthis.personModalType = 'add';\n\t\t\t\tthis.currentPerson = {\n\t\t\t\t\tid: null,\n\t\t\t\t\tdeskNumber: '',\n\t\t\t\t\tname: '',\n\t\t\t\t\ttitle: '',\n\t\t\t\t\temployeeId: '',\n\t\t\t\t\tcompany: '',\n\t\t\t\t\tselected: false\n\t\t\t\t};\n\t\t\t\tthis.showPersonModal = true;\n\t\t\t},\n\t\t\t\n\t\t\t// 批量导入人员\n\t\t\tbatchImport() {\n\t\t\t\tconsole.log('批量导入人员');\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '批量导入功能开发中',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 编辑人员\n\t\t\teditPerson(person) {\n\t\t\t\tconsole.log('编辑人员:', person);\n\t\t\t\tthis.personModalType = 'edit';\n\t\t\t\t// this.currentPerson = { ...person };\n\t\t\t\t// 使用传统方式复制对象\n\t\t\t\tthis.currentPerson = {\n\t\t\t\t\tid: null,\n\t\t\t\t\tdeskNumber: '',\n\t\t\t\t\tname: '',\n\t\t\t\t\ttitle: '',\n\t\t\t\t\temployeeId: '',\n\t\t\t\t\tcompany: '',\n\t\t\t\t\tselected: false\n\t\t\t\t};\n\t\t\t\tfor (let key in person) {\n\t\t\t\t\tif (person.hasOwnProperty(key)) {\n\t\t\t\t\t\tthis.currentPerson[key] = person[key];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.showPersonModal = true;\n\t\t\t},\n\t\t\t\n\t\t\t// 删除人员\n\t\t\tdeletePerson(person) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认删除',\n\t\t\t\t\tcontent: `确定要删除${person.name}吗？`,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tthis.peopleList = this.peopleList.filter(p => p.id !== person.id);\n\t\t\t\t\t\t\t// 如果删除后没有人员了，切换到空状态\n\t\t\t\t\t\t\tif (this.peopleList.length === 0) {\n\t\t\t\t\t\t\t\tthis.hasPeople = false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '删除成功',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 删除选中的人员\n\t\t\tdeleteSelected() {\n\t\t\t\tconst selectedPeople = this.peopleList.filter(person => person.selected);\n\t\t\t\tif (selectedPeople.length === 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请先选择要删除的人员',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认删除',\n\t\t\t\t\tcontent: `确定要删除选中的${selectedPeople.length}个人员吗？`,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tthis.peopleList = this.peopleList.filter(person => !person.selected);\n\t\t\t\t\t\t\t// 如果删除后没有人员了，切换到空状态\n\t\t\t\t\t\t\tif (this.peopleList.length === 0) {\n\t\t\t\t\t\t\t\tthis.hasPeople = false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '删除成功',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// Excel导入\n\t\t\timportExcel() {\n\t\t\t\tconsole.log('Excel导入');\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: 'Excel导入功能开发中',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 显示桌牌选择器\n\t\t\tshowDeskSelector() {\n\t\t\t\tthis.showDeskModal = true;\n\t\t\t},\n\t\t\t\n\t\t\t// 选择桌牌\n\t\t\tselectDesk(desk) {\n\t\t\t\tif (desk.status !== 'available') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '该桌牌不可用',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 检查网关限制：同一网关下只能绑定一个参会人员\n\t\t\t\tconst sameGatewayBoundDesk = this.deskDeviceList.find(d => \n\t\t\t\t\td.gatewayId === desk.gatewayId && \n\t\t\t\t\td.status === 'bound' && \n\t\t\t\t\td.boundPersonId !== this.currentPerson.id\n\t\t\t\t);\n\t\t\t\t\n\t\t\t\tif (sameGatewayBoundDesk) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '网关限制',\n\t\t\t\t\t\tcontent: `${desk.gatewayName}下已有其他人员绑定桌牌，同一网关下只能绑定一个参会人员，不可跨网关使用。`,\n\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.currentPerson.deskNumber = desk.number;\n\t\t\t\tthis.closeDeskModal();\n\t\t\t},\n\t\t\t\n\t\t\t// 关闭桌牌选择弹窗\n\t\t\tcloseDeskModal() {\n\t\t\t\tthis.showDeskModal = false;\n\t\t\t},\n\t\t\t\n\t\t\t// 获取桌牌状态文本\n\t\t\tgetStatusText(status) {\n\t\t\t\tswitch(status) {\n\t\t\t\t\tcase 'available': return '可用';\n\t\t\t\t\tcase 'bound': return '已绑定';\n\t\t\t\t\tcase 'offline': return '离线';\n\t\t\t\t\tdefault: return '未知';\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 更新桌牌设备绑定状态\n\t\t\tupdateDeskBinding(deskNumber, personId) {\n\t\t\t\tconst desk = this.deskDeviceList.find(d => d.number === deskNumber);\n\t\t\t\tif (desk) {\n\t\t\t\t\tif (personId) {\n\t\t\t\t\t\t// 绑定桌牌\n\t\t\t\t\t\tdesk.status = 'bound';\n\t\t\t\t\t\tdesk.boundPersonId = personId;\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 解绑桌牌\n\t\t\t\t\t\tdesk.status = 'available';\n\t\t\t\t\t\tdesk.boundPersonId = null;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 显示会议室选择器\n\t\t\tshowRoomSelector() {\n\t\t\t\tthis.showRoomModal = true;\n\t\t\t},\n\t\t\t\n\t\t\t// 选择会议室\n\t\t\tselectRoom(room) {\n\t\t\t\tthis.roomName = room.name;\n\t\t\t\tthis.showRoomModal = false;\n\t\t\t\t// 切换会议室后重新加载数据\n\t\t\t\tthis.loadPeopleData();\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: `已切换到${room.name}`,\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 关闭会议室选择弹窗\n\t\t\tcloseRoomModal() {\n\t\t\t\tthis.showRoomModal = false;\n\t\t\t},\n\t\t\t\n\t\t\t// 关闭人员弹窗\n\t\t\tclosePersonModal() {\n\t\t\t\tthis.showPersonModal = false;\n\t\t\t},\n\t\t\t\n\t\t\t// 确认新增/编辑人员\n\t\t\tconfirmPerson() {\n\t\t\t\t// 验证必填字段\n\t\t\t\tif (!this.currentPerson.deskNumber || !this.currentPerson.name || \n\t\t\t\t\t!this.currentPerson.title || !this.currentPerson.employeeId || \n\t\t\t\t\t!this.currentPerson.company) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请填写完整信息',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (this.personModalType === 'add') {\n\t\t\t\t// 新增人员\n\t\t\t\tconst newPerson = {\n\t\t\t\t\tid: Date.now(), // 简单的ID生成\n\t\t\t\t\tdeskNumber: this.currentPerson.deskNumber,\n\t\t\t\t\tname: this.currentPerson.name,\n\t\t\t\t\ttitle: this.currentPerson.title,\n\t\t\t\t\temployeeId: this.currentPerson.employeeId,\n\t\t\t\t\tcompany: this.currentPerson.company,\n\t\t\t\t\tselected: false\n\t\t\t\t};\n\t\t\t\tthis.peopleList.push(newPerson);\n\t\t\t\tthis.hasPeople = true;\n\t\t\t\t\n\t\t\t\t// 更新桌牌设备绑定状态\n\t\t\t\tif (newPerson.deskNumber) {\n\t\t\t\t\tthis.updateDeskBinding(newPerson.deskNumber, newPerson.id);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '新增成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\t// 编辑人员\n\t\t\t\tconst index = this.peopleList.findIndex(p => p.id === this.currentPerson.id);\n\t\t\t\tif (index !== -1) {\n\t\t\t\t\tconst oldPerson = this.peopleList[index];\n\t\t\t\t\t\n\t\t\t\t\t// 如果桌牌编号发生变化，需要更新绑定状态\n\t\t\t\t\tif (oldPerson.deskNumber !== this.currentPerson.deskNumber) {\n\t\t\t\t\t\t// 解绑旧桌牌\n\t\t\t\t\t\tif (oldPerson.deskNumber) {\n\t\t\t\t\t\t\tthis.updateDeskBinding(oldPerson.deskNumber, null);\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 绑定新桌牌\n\t\t\t\t\t\tif (this.currentPerson.deskNumber) {\n\t\t\t\t\t\t\tthis.updateDeskBinding(this.currentPerson.deskNumber, this.currentPerson.id);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthis.peopleList[index] = {\n\t\t\t\t\t\tid: this.currentPerson.id,\n\t\t\t\t\t\tdeskNumber: this.currentPerson.deskNumber,\n\t\t\t\t\t\tname: this.currentPerson.name,\n\t\t\t\t\t\ttitle: this.currentPerson.title,\n\t\t\t\t\t\temployeeId: this.currentPerson.employeeId,\n\t\t\t\t\t\tcompany: this.currentPerson.company,\n\t\t\t\t\t\tselected: this.currentPerson.selected\n\t\t\t\t\t};\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '编辑成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.showPersonModal = false;\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n.container {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\t// padding-bottom: 160rpx; \n}\n\n/* 会议室信息头部 */\n.room-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 30rpx;\n\tbackground-color: #ffffff;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.room-info {\n\tflex: 1;\n\tcursor: pointer;\n\tpadding: 10rpx;\n\tborder-radius: 8rpx;\n\ttransition: background-color 0.3s;\n}\n\n.room-info:hover {\n\tbackground-color: #f5f5f5;\n}\n\n.room-name {\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n\tcolor: #333333;\n}\n\n.switch-hint {\n\tfont-size: 24rpx;\n\tcolor: #999999;\n\tmargin-top: 5rpx;\n}\n\n.select-all {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.select-all-text {\n\tfont-size: 28rpx;\n\tcolor: #333333;\n\tmargin-left: 10rpx;\n}\n\n/* 空状态样式 */\n.empty-state {\n\tflex: 1;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\t// padding: 100rpx 40rpx;\n\tmin-height: calc(100vh - 200rpx); /* 确保有足够高度进行垂直居中 */\n}\n\n.empty-content {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tgap: 40rpx;\n\twidth: 100%;\n\tmax-width: 500rpx; /* 限制最大宽度 */\n\tmargin: 0 auto; /* 水平居中 */\n}\n\n.action-button {\n\twidth: 500rpx;\n\theight: 100rpx;\n\tborder-radius: 50rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-bottom: 40rpx;\n\tposition: relative;\n\tbackground: transparent;\n}\n\n.action-button::before {\n\tcontent: '';\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\t// background: linear-gradient(135deg, #8B0000 0%, #A52A2A 100%);\n\tz-index: 1;\n}\n.button-bg {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tz-index: 1;\n\tborder-radius: 50rpx;\n}\n.action-text {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #ffffff;\n\tposition: relative;\n\tz-index: 2;\n}\n\n/* 人员列表样式 */\n.people-list {\n\tpadding: 20rpx;\n\tflex: 1;\n}\n\n.person-item {\n\tbackground-color: #ffffff;\n\tborder-radius: 16rpx;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\n\toverflow: hidden;\n}\n\n.person-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 25rpx 30rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.desk-number {\n\tfont-size: 30rpx;\n\tfont-weight: 500;\n\tcolor: #333333;\n}\n\n.person-details {\n\tpadding: 30rpx;\n}\n\n.detail-row {\n\tdisplay: flex;\n\tmargin-bottom: 20rpx;\n}\n\n.detail-row:last-child {\n\tmargin-bottom: 0;\n}\n\n.detail-item {\n\tflex: 1;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.detail-label {\n\tfont-size: 26rpx;\n\tcolor: #666666;\n\twidth: 126rpx;\n}\n\n.detail-value {\n\tfont-size: 26rpx;\n\tcolor: #333333;\n\tflex: 1;\n}\n\n.person-actions {\n\tdisplay: flex;\n\tjustify-content: flex-end;\n\tpadding: 20rpx 30rpx;\n\tborder-top: 1rpx solid #f0f0f0;\n\tgap: 15rpx;\n}\n\n.action-btn {\n\tpadding: 0 25rpx;\n\theight: 56rpx;\n\tborder-radius: 8rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 24rpx;\n\tbox-shadow: 0 2rpx 5rpx rgba(0,0,0,0.1);\n}\n\n.delete-btn {\n\tbackground-color: #ffffff;\n\tborder: 1rpx solid #cccccc;\n\tcolor: #555555;\n}\n\n.edit-btn {\n\tbackground-color: #8B0000;\n\tcolor: #ffffff;\n}\n\n/* 浮动按钮样式 */\n.floating-buttons {\n\tposition: fixed;\n\tright: 30rpx;\n\tbottom: 200rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 20rpx;\n\tz-index: 100;\n}\n\n.float-btn {\n\twidth: 100rpx;\n\theight: 100rpx;\n\tborder-radius: 50rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);\n}\n\n.add-btn {\n\tbackground-color: #8B0000;\n}\n\n.float-btn-text {\n\tfont-size: 40rpx;\n\tfont-weight: 300;\n\tcolor: #ffffff;\n}\n\n.excel-btn {\n\tbackground-color: #8B0000;\n}\n\n.excel-text {\n\tfont-size: 20rpx;\n\tfont-weight: 600;\n\tcolor: #ffffff;\n}\n\n/* 底部删除按钮 */\n.bottom-action {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tpadding: 20rpx 30rpx;\n\tpadding-bottom: calc(20rpx + env(safe-area-inset-bottom));\n\tbackground-color: #ffffff;\n\tborder-top: 1rpx solid #f0f0f0;\n\tz-index: 99;\n}\n\n.delete-selected-btn {\n\twidth: 100%;\n\theight: 80rpx;\n\tbackground-color: #8B0000;\n\tborder-radius: 40rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\tcolor: #ffffff;\n\tbox-shadow: 0 4rpx 12rpx rgba(139, 0, 0, 0.3);\n}\n\n/* 会议室选择弹窗样式 */\n.room-modal {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tbackground-color: rgba(0, 0, 0, 0.5);\n\tdisplay: flex;\n\talign-items: flex-end;\n\tjustify-content: center;\n\tz-index: 1000;\n\ttransition: all 0.3s ease;\n}\n\n.room-modal-content {\n\twidth: 100%;\n\tmax-height: 70vh;\n\tbackground-color: #ffffff;\n\tborder-radius: 24rpx 24rpx 0 0;\n\toverflow: hidden;\n\tbox-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.2);\n\ttransform: translateY(0);\n\ttransition: transform 0.3s ease;\n\tanimation: slideUp 0.3s ease;\n}\n\n@keyframes slideUp {\n\tfrom {\n\t\ttransform: translateY(100%);\n\t}\n\tto {\n\t\ttransform: translateY(0);\n\t}\n}\n\n.modal-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 30rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.modal-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #333333;\n}\n\n.close-btn {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tborder-radius: 30rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground-color: #f5f5f5;\n\tcolor: #666666;\n\tfont-size: 36rpx;\n\tcursor: pointer;\n}\n\n.close-btn:hover {\n\tbackground-color: #e0e0e0;\n}\n\n.room-list {\n\tmax-height: 600rpx;\n\toverflow-y: auto;\n}\n\n.room-option {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 30rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n\tcursor: pointer;\n\ttransition: background-color 0.3s;\n}\n\n.room-option:last-child {\n\tborder-bottom: none;\n}\n\n.room-option:hover {\n\tbackground-color: #f8f8f8;\n}\n\n.room-option-name {\n\tfont-size: 30rpx;\n\tcolor: #333333;\n}\n\n.room-option-check {\n\twidth: 40rpx;\n\theight: 40rpx;\n\tborder-radius: 20rpx;\n\tbackground-color: #8B0000;\n\tcolor: #ffffff;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 24rpx;\n\t\tfont-weight: bold;\n\t}\n\n/* 新增/编辑人员弹窗样式 */\n.person-modal {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tbackground-color: rgba(0, 0, 0, 0.5);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tz-index: 1001;\n\ttransition: all 0.3s ease;\n}\n\n.person-modal-content {\n\twidth: 90%;\n\tmax-width: 700rpx;\n\tbackground-color: #ffffff;\n\tborder-radius: 16rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);\n\ttransform: scale(1);\n\ttransition: transform 0.3s ease;\n\tanimation: modalShow 0.3s ease;\n}\n\n@keyframes modalShow {\n\tfrom {\n\t\ttransform: scale(0.8);\n\t\topacity: 0;\n\t}\n\tto {\n\t\ttransform: scale(1);\n\t\topacity: 1;\n\t}\n}\n\n.person-form {\n\tpadding: 30rpx;\n\tmax-height: 60vh;\n\toverflow-y: auto;\n}\n\n.form-item {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 30rpx;\n\tpadding: 20rpx;\n\tbackground-color: #f8f8f8;\n\tborder-radius: 8rpx;\n}\n\n.form-item:last-child {\n\tmargin-bottom: 0;\n}\n\n.form-label {\n\tfont-size: 28rpx;\n\tcolor: #666666;\n\twidth: 160rpx;\n\tflex-shrink: 0;\n}\n\n.form-input {\n\tflex: 1;\n\tfont-size: 28rpx;\n\tcolor: #333333;\n\tbackground-color: transparent;\n\tborder: none;\n\toutline: none;\n}\n\n.form-input::placeholder {\n\tcolor: #999999;\n}\n\n.modal-actions {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tpadding: 30rpx;\n\tborder-top: 1rpx solid #f0f0f0;\n\tgap: 30rpx;\n}\n\n.modal-actions .action-btn {\n\tflex: 1;\n\theight: 80rpx;\n\tborder-radius: 8rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\tcursor: pointer;\n\ttransition: all 0.3s ease;\n}\n\n.cancel-btn {\n\tbackground-color: #f5f5f5;\n\tcolor: #666666;\n\tborder: 1rpx solid #e0e0e0;\n}\n\n.cancel-btn:hover {\n\tbackground-color: #e8e8e8;\n}\n\n.confirm-btn {\n\tbackground-color: #8B0000;\n\tcolor: #ffffff;\n\tborder: 1rpx solid #8B0000;\n}\n\n.confirm-btn:hover {\n\t\tbackground-color: #A52A2A;\n\t}\n\n/* 桌牌选择下拉框样式 */\n.form-select {\n\tflex: 1;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 0 20rpx;\n\tbackground-color: transparent;\n\tborder: none;\n\toutline: none;\n\tcursor: pointer;\n\ttransition: all 0.3s ease;\n}\n\n.form-select:hover {\n\tbackground-color: #f0f0f0;\n}\n\n.select-text {\n\tflex: 1;\n\tfont-size: 28rpx;\n\tcolor: #333333;\n}\n\n.select-text.placeholder {\n\tcolor: #999999;\n}\n\n.select-arrow {\n\tfont-size: 20rpx;\n\tcolor: #666666;\n\ttransition: transform 0.3s ease;\n}\n\n/* 桌牌选择弹窗样式 */\n.desk-modal {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tbackground-color: rgba(0, 0, 0, 0.5);\n\tdisplay: flex;\n\talign-items: flex-end;\n\tjustify-content: center;\n\tz-index: 1002;\n\ttransition: all 0.3s ease;\n}\n\n.desk-modal-content {\n\twidth: 100%;\n\tmax-width: 100%;\n\tmax-height: 70vh;\n\tbackground-color: #ffffff;\n\tborder-radius: 16rpx 16rpx 0 0;\n\toverflow: hidden;\n\tbox-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.2);\n\ttransform: translateY(0);\n\ttransition: transform 0.3s ease;\n\tanimation: slideUpDesk 0.3s ease;\n}\n\n@keyframes slideUpDesk {\n\tfrom {\n\t\ttransform: translateY(100%);\n\t}\n\tto {\n\t\ttransform: translateY(0);\n\t}\n}\n\n.desk-list {\n\tpadding: 20rpx;\n\tmax-height: 50vh;\n\toverflow-y: auto;\n}\n\n.desk-option {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 20rpx;\n\tmargin-bottom: 16rpx;\n\tbackground-color: #f8f8f8;\n\tborder-radius: 12rpx;\n\tcursor: pointer;\n\ttransition: all 0.3s ease;\n}\n\n.desk-option:hover {\n\tbackground-color: #e8e8e8;\n}\n\n.desk-option.disabled {\n\tbackground-color: #f5f5f5;\n\topacity: 0.6;\n\tcursor: not-allowed;\n}\n\n.desk-option.disabled:hover {\n\tbackground-color: #f5f5f5;\n}\n\n.desk-info {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 8rpx;\n}\n\n.desk-number {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #333333;\n}\n\n.desk-gateway {\n\tfont-size: 24rpx;\n\tcolor: #666666;\n}\n\n.desk-status {\n\tfont-size: 22rpx;\n\tpadding: 4rpx 12rpx;\n\tborder-radius: 12rpx;\n\talign-self: flex-start;\n}\n\n.desk-status.available {\n\tbackground-color: #e8f5e8;\n\tcolor: #4caf50;\n}\n\n.desk-status.bound {\n\tbackground-color: #fff3e0;\n\tcolor: #ff9800;\n}\n\n.desk-status.offline {\n\tbackground-color: #ffebee;\n\tcolor: #f44336;\n}\n\n.desk-check {\n\twidth: 40rpx;\n\theight: 40rpx;\n\tborder-radius: 50%;\n\tbackground-color: #8B0000;\n\tcolor: #ffffff;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 24rpx;\n\tfont-weight: bold;\n}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751873716637\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}