<u-picker vue-id="7f6e80aa-1" show="{{show}}" closeOnClickOverlay="{{closeOnClickOverlay}}" columns="{{columns}}" title="{{title}}" defaultIndex="{{innerDefaultIndex}}" cancelText="{{cancelText}}" confirmText="{{confirmText}}" cancelColor="{{cancelColor}}" confirmColor="{{confirmColor}}" data-ref="picker" data-event-opts="{{[['^close',[['close']]],['^cancel',[['cancel']]],['^confirm',[['confirm']]],['^change',[['change']]]]}}" bind:close="__e" bind:cancel="__e" bind:confirm="__e" bind:change="__e" class="data-v-fbda4e8a vue-ref" bind:__l="__l"></u-picker>